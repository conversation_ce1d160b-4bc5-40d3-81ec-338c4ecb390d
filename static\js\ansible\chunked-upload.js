/**
 * 分片上传管理器
 * 专为1GB左右的大文件设计，支持断点续传、暂停/恢复等功能
 */
class ChunkedUploadManager {
    constructor() {
        this.uploadTasks = new Map();
        this.maxConcurrentUploads = 2; // 大文件限制并发数
        this.currentUploads = 0;
        this.uploadQueue = [];
        this.pollInterval = 2000; // 2秒轮询一次（大文件轮询间隔可以长一些）
        this.pollTimers = new Map();
        this.defaultChunkSize = 10 * 1024 * 1024; // 10MB分片
        
        this.initializeUI();
    }

    /**
     * 初始化UI组件
     */
    initializeUI() {
        this.createProgressPanel();
        this.bindEvents();
    }

    /**
     * 创建分片上传进度面板
     */
    createProgressPanel() {
        if (document.getElementById('chunked-upload-panel')) {
            return;
        }

        const panel = document.createElement('div');
        panel.id = 'chunked-upload-panel';
        panel.className = 'chunked-upload-panel';
        panel.innerHTML = `
            <div class="panel-header">
                <h6><i class="bi bi-cloud-upload-fill"></i> 大文件上传</h6>
                <div class="panel-controls">
                    <button class="btn btn-sm btn-outline-secondary" onclick="chunkedUploadManager.togglePanel()">
                        <i class="bi bi-dash"></i>
                    </button>
                </div>
            </div>
            <div class="panel-body" id="chunked-upload-tasks-container">
                <div class="no-uploads text-muted text-center py-3">
                    <i class="bi bi-cloud-upload-fill fs-4"></i>
                    <p class="mb-0">暂无大文件上传任务</p>
                    <small>支持断点续传、暂停/恢复</small>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .chunked-upload-panel {
                position: fixed;
                bottom: 20px;
                left: 20px;
                width: 450px;
                max-height: 600px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1050;
                display: none;
            }
            .chunked-upload-panel.show {
                display: block;
            }
            .chunked-upload-panel .panel-header {
                padding: 12px 16px;
                border-bottom: 1px solid #dee2e6;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 8px 8px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .chunked-upload-panel .panel-body {
                padding: 16px;
                max-height: 500px;
                overflow-y: auto;
            }
            .chunked-task-item {
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 16px;
                background: #f8f9fa;
                position: relative;
            }
            .chunked-task-item:last-child {
                margin-bottom: 0;
            }
            .chunked-task-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;
            }
            .chunked-task-info {
                flex: 1;
                margin-right: 12px;
            }
            .chunked-task-filename {
                font-weight: 600;
                color: #495057;
                font-size: 14px;
                margin-bottom: 4px;
                word-break: break-all;
            }
            .chunked-task-size {
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 8px;
            }
            .chunked-task-status {
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 12px;
                font-weight: 500;
                display: inline-block;
            }
            .chunked-task-status.pending { background: #fff3cd; color: #856404; }
            .chunked-task-status.uploading { background: #d1ecf1; color: #0c5460; }
            .chunked-task-status.paused { background: #ffeaa7; color: #d63031; }
            .chunked-task-status.completed { background: #d4edda; color: #155724; }
            .chunked-task-status.failed { background: #f8d7da; color: #721c24; }
            .chunked-task-status.cancelled { background: #e2e3e5; color: #6c757d; }
            .chunked-task-progress {
                margin: 12px 0;
            }
            .chunked-progress {
                height: 8px;
                background: #e9ecef;
                border-radius: 4px;
                overflow: hidden;
                position: relative;
            }
            .chunked-progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #667eea, #764ba2);
                transition: width 0.3s ease;
                position: relative;
            }
            .chunked-progress-text {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 11px;
                font-weight: 600;
                color: white;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            }
            .chunked-task-details {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 12px;
            }
            .chunked-task-actions {
                display: flex;
                gap: 6px;
                flex-wrap: wrap;
            }
            .chunked-task-actions .btn {
                padding: 4px 8px;
                font-size: 11px;
                border-radius: 4px;
            }
            .chunk-info {
                font-size: 11px;
                color: #6c757d;
                margin-top: 4px;
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(panel);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 检测大文件并自动使用分片上传
        document.addEventListener('change', (event) => {
            if (event.target.type === 'file' && event.target.files.length > 0) {
                const file = event.target.files[0];
                const fileSizeMB = file.size / (1024 * 1024);
                
                // 如果文件大于100MB，显示分片上传建议
                if (fileSizeMB > 100) {
                    this.showLargeFileDialog(file, event.target);
                }
            }
        });
    }

    /**
     * 显示大文件上传对话框
     */
    showLargeFileDialog(file, fileInput) {
        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
        
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle text-warning"></i>
                            检测到大文件
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>文件名：</strong>${file.name}</p>
                        <p><strong>文件大小：</strong>${fileSizeMB} MB</p>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>建议使用分片上传：</strong>
                            <ul class="mb-0 mt-2">
                                <li>支持断点续传，网络中断后可恢复</li>
                                <li>支持暂停/恢复功能</li>
                                <li>更稳定的大文件传输</li>
                                <li>实时进度显示</li>
                            </ul>
                        </div>
                        <div class="form-group mt-3">
                            <label>分片大小：</label>
                            <select class="form-select" id="chunkSizeSelect">
                                <option value="5242880">5 MB（网络较慢）</option>
                                <option value="10485760" selected>10 MB（推荐）</option>
                                <option value="20971520">20 MB（网络较快）</option>
                                <option value="52428800">50 MB（高速网络）</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            使用普通上传
                        </button>
                        <button type="button" class="btn btn-primary" onclick="chunkedUploadManager.startChunkedUpload('${file.name}')">
                            <i class="bi bi-cloud-upload-fill"></i>
                            使用分片上传
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
        
        // 存储文件引用
        this.pendingFile = file;
        this.pendingFileInput = fileInput;
        
        // 清理模态框
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * 开始分片上传
     */
    async startChunkedUpload(filename) {
        if (!this.pendingFile) {
            this.showToast('error', '文件丢失，请重新选择');
            return;
        }

        const chunkSize = parseInt(document.getElementById('chunkSizeSelect').value);
        const pathInput = document.getElementById('uploadPath');
        const overwriteCheckbox = document.getElementById('overwriteFile');
        
        const path = pathInput ? pathInput.value : '/tmp';
        const overwrite = overwriteCheckbox ? overwriteCheckbox.checked : false;

        // 关闭模态框
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            }
        });

        // 开始分片上传
        await this.uploadFileChunked(this.pendingFile, path, overwrite, chunkSize);
        
        // 清空文件输入框
        if (this.pendingFileInput) {
            this.pendingFileInput.value = '';
        }
        
        // 清理引用
        this.pendingFile = null;
        this.pendingFileInput = null;
    }

    /**
     * 分片上传文件
     */
    async uploadFileChunked(file, destinationPath, overwrite = false, chunkSize = null) {
        if (!file) {
            this.showToast('error', '请选择文件');
            return false;
        }

        // 检查并发限制
        if (this.currentUploads >= this.maxConcurrentUploads) {
            this.uploadQueue.push({ file, destinationPath, overwrite, chunkSize });
            this.showToast('info', '已加入上传队列，等待处理...');
            return true;
        }

        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('path', destinationPath);
            formData.append('overwrite', overwrite);
            if (chunkSize) {
                formData.append('chunk_size', chunkSize);
            }

            const response = await fetch('/ansible/api/file-manager/upload-chunked', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const result = await response.json();

            if (result.success) {
                const taskId = result.task_id;
                
                const task = {
                    id: taskId,
                    filename: file.name,
                    status: 'pending',
                    progress: 0,
                    speed: 0,
                    fileSize: file.size,
                    chunkSize: chunkSize || this.defaultChunkSize,
                    startTime: Date.now(),
                    totalChunks: Math.ceil(file.size / (chunkSize || this.defaultChunkSize)),
                    uploadedChunks: 0
                };

                this.uploadTasks.set(taskId, task);
                this.currentUploads++;
                
                this.showPanel();
                this.renderTask(task);
                this.startPolling(taskId);
                
                this.showToast('success', '分片上传已开始');
                return true;
            } else {
                this.showToast('error', result.message || '上传失败');
                return false;
            }
        } catch (error) {
            console.error('分片上传失败:', error);
            this.showToast('error', '分片上传失败: ' + error.message);
            return false;
        }
    }

    /**
     * 开始轮询任务状态
     */
    startPolling(taskId) {
        const timer = setInterval(async () => {
            try {
                const response = await fetch(`/ansible/api/file-manager/upload-status/${taskId}`, {
                    credentials: 'same-origin'
                });
                
                const result = await response.json();
                
                if (result.success && result.status) {
                    const task = this.uploadTasks.get(taskId);
                    if (task) {
                        // 更新任务状态
                        Object.assign(task, result.status);
                        this.renderTask(task);
                        
                        // 检查是否完成
                        if (['completed', 'failed', 'cancelled'].includes(task.status)) {
                            this.stopPolling(taskId);
                            this.currentUploads--;
                            
                            if (task.status === 'completed') {
                                this.showToast('success', `文件 ${task.filename} 上传完成`);
                                if (typeof loadFileList === 'function') {
                                    loadFileList(currentPath);
                                }
                            } else if (task.status === 'failed') {
                                this.showToast('error', `文件 ${task.filename} 上传失败: ${task.error_message || '未知错误'}`);
                            }
                            
                            this.processQueue();
                        }
                    }
                }
            } catch (error) {
                console.error('轮询状态失败:', error);
                this.stopPolling(taskId);
            }
        }, this.pollInterval);
        
        this.pollTimers.set(taskId, timer);
    }

    /**
     * 停止轮询
     */
    stopPolling(taskId) {
        const timer = this.pollTimers.get(taskId);
        if (timer) {
            clearInterval(timer);
            this.pollTimers.delete(taskId);
        }
    }

    /**
     * 处理上传队列
     */
    processQueue() {
        if (this.uploadQueue.length > 0 && this.currentUploads < this.maxConcurrentUploads) {
            const { file, destinationPath, overwrite, chunkSize } = this.uploadQueue.shift();
            this.uploadFileChunked(file, destinationPath, overwrite, chunkSize);
        }
    }

    /**
     * 暂停上传
     */
    async pauseTask(taskId) {
        try {
            const response = await fetch(`/ansible/api/file-manager/upload-pause/${taskId}`, {
                method: 'POST',
                credentials: 'same-origin'
            });
            
            const result = await response.json();
            
            if (result.success) {
                const task = this.uploadTasks.get(taskId);
                if (task) {
                    task.status = 'paused';
                    this.renderTask(task);
                }
                this.showToast('info', '上传已暂停');
            }
        } catch (error) {
            console.error('暂停上传失败:', error);
        }
    }

    /**
     * 恢复上传
     */
    async resumeTask(taskId) {
        try {
            const response = await fetch(`/ansible/api/file-manager/upload-resume/${taskId}`, {
                method: 'POST',
                credentials: 'same-origin'
            });
            
            const result = await response.json();
            
            if (result.success) {
                const task = this.uploadTasks.get(taskId);
                if (task) {
                    task.status = 'uploading';
                    this.renderTask(task);
                }
                this.showToast('info', '上传已恢复');
            }
        } catch (error) {
            console.error('恢复上传失败:', error);
        }
    }

    /**
     * 取消上传
     */
    async cancelTask(taskId) {
        try {
            const response = await fetch(`/ansible/api/file-manager/upload-cancel/${taskId}`, {
                method: 'POST',
                credentials: 'same-origin'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.stopPolling(taskId);
                const task = this.uploadTasks.get(taskId);
                if (task) {
                    task.status = 'cancelled';
                    this.renderTask(task);
                }
                this.currentUploads--;
                this.processQueue();
                this.showToast('info', '上传已取消');
            }
        } catch (error) {
            console.error('取消上传失败:', error);
        }
    }

    /**
     * 渲染任务UI
     */
    renderTask(task) {
        const container = document.getElementById('chunked-upload-tasks-container');
        const noUploads = container.querySelector('.no-uploads');
        
        if (noUploads) {
            noUploads.style.display = 'none';
        }
        
        let taskElement = document.getElementById(`chunked-task-${task.id}`);
        
        if (!taskElement) {
            taskElement = document.createElement('div');
            taskElement.id = `chunked-task-${task.id}`;
            taskElement.className = 'chunked-task-item';
            container.appendChild(taskElement);
        }
        
        const statusText = {
            'pending': '等待中',
            'uploading': '上传中',
            'paused': '已暂停',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        };
        
        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };
        
        const formatSpeed = (bytesPerSecond) => {
            if (!bytesPerSecond) return '0 B/s';
            return formatFileSize(bytesPerSecond) + '/s';
        };
        
        const chunkInfo = task.total_chunks ? 
            `${task.uploaded_chunks || 0}/${task.total_chunks} 分片` : 
            `分片大小: ${formatFileSize(task.chunkSize || 0)}`;
        
        taskElement.innerHTML = `
            <div class="chunked-task-header">
                <div class="chunked-task-info">
                    <div class="chunked-task-filename" title="${task.filename}">${task.filename}</div>
                    <div class="chunked-task-size">${formatFileSize(task.file_size || task.fileSize || 0)}</div>
                    <span class="chunked-task-status ${task.status}">${statusText[task.status] || task.status}</span>
                </div>
            </div>
            <div class="chunked-task-progress">
                <div class="chunked-progress">
                    <div class="chunked-progress-bar" style="width: ${task.progress || 0}%">
                        <div class="chunked-progress-text">${Math.round(task.progress || 0)}%</div>
                    </div>
                </div>
            </div>
            <div class="chunked-task-details">
                <span>${formatSpeed(task.speed || 0)}</span>
                <span class="chunk-info">${chunkInfo}</span>
            </div>
            <div class="chunked-task-actions">
                ${task.status === 'uploading' ? 
                    `<button class="btn btn-warning btn-sm" onclick="chunkedUploadManager.pauseTask('${task.id}')">
                        <i class="bi bi-pause-fill"></i> 暂停
                    </button>` : ''}
                ${task.status === 'paused' ? 
                    `<button class="btn btn-success btn-sm" onclick="chunkedUploadManager.resumeTask('${task.id}')">
                        <i class="bi bi-play-fill"></i> 恢复
                    </button>` : ''}
                ${['uploading', 'pending', 'paused'].includes(task.status) ? 
                    `<button class="btn btn-danger btn-sm" onclick="chunkedUploadManager.cancelTask('${task.id}')">
                        <i class="bi bi-x"></i> 取消
                    </button>` : ''}
            </div>
        `;
    }

    /**
     * 显示/隐藏面板
     */
    showPanel() {
        const panel = document.getElementById('chunked-upload-panel');
        panel.classList.add('show');
    }

    hidePanel() {
        const panel = document.getElementById('chunked-upload-panel');
        panel.classList.remove('show');
    }

    togglePanel() {
        const panel = document.getElementById('chunked-upload-panel');
        panel.classList.toggle('show');
    }

    /**
     * 显示提示消息
     */
    showToast(type, message) {
        if (typeof showToast === 'function') {
            showToast(type, message);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 初始化分片上传管理器
window.chunkedUploadManager = new ChunkedUploadManager();
