#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
开发环境启动脚本
自动设置开发环境所需的环境变量
"""

import os
import sys
import subprocess

def setup_dev_environment():
    """设置开发环境变量"""
    
    # 开发环境默认配置
    dev_config = {
        'DB_PASSWORD': '123456',
        'SECRET_KEY': 'dev_secret_key_for_development_only_32chars',
        'EDIT_PASSWORD_HASH': 'dev_edit_password_hash',
        'JWT_SECRET': 'dev_jwt_secret_for_development_only_32chars',
        'FLASK_ENV': 'development',
        'FLASK_DEBUG': '1'
    }
    
    print("🚀 设置开发环境...")
    
    # 设置环境变量
    for key, value in dev_config.items():
        os.environ[key] = value
        print(f"✅ 设置 {key}")
    
    print("\n📋 当前环境变量:")
    for key in dev_config.keys():
        if 'PASSWORD' in key or 'SECRET' in key:
            print(f"   {key}: ***")
        else:
            print(f"   {key}: {os.environ.get(key)}")
    
    print("\n⚠️  注意: 这些是开发环境的默认配置")
    print("   生产环境请使用安全的密码和密钥!")
    print("\n🔧 启动应用...")

def main():
    """主函数"""
    try:
        # 设置开发环境
        setup_dev_environment()
        
        # 启动Flask应用
        from app import app
        app.run(host='0.0.0.0', port=5100, debug=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
