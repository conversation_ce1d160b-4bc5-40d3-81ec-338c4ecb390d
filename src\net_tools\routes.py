from flask import Blueprint, request, jsonify, render_template
from .network_tools import ping_ip, telnet_port, traceroute_ip, scan_ports, scan_ports_with_details

net_tools_bp = Blueprint('net_tools_bp', __name__, url_prefix='/tools', template_folder='../../templates')

@net_tools_bp.route('/ping', methods=['POST'])
def api_ping():
    data = request.get_json()
    if not data or 'ip_address' not in data:
        return jsonify({"error": "请求体中缺少 'ip_address'"}), 400
    
    ip_address = data['ip_address']
    packets = data.get('packets', 4) # 允许通过请求指定包数量，默认为4
    timeout = data.get('timeout', 10) # 允许通过请求指定超时时间，默认为10秒

    try:
        # 确保 packets 和 timeout 是整数
        packets = int(packets)
        timeout = int(timeout)
        if not (0 < packets <= 20): # 限制包数量范围
            return jsonify({"error": "包数量 (packets) 必须在 1 到 20 之间"}), 400
        if not (1 <= timeout <= 60): # 限制超时时间范围
            return jsonify({"error": "超时时间 (timeout) 必须在 1 到 60 秒之间"}), 400
    except ValueError:
        return jsonify({"error": "参数 'packets' 和 'timeout' 必须是有效的整数"}), 400

    result = ping_ip(ip_address, packets=packets, timeout_seconds=timeout)
    return jsonify(result)

@net_tools_bp.route('/telnet', methods=['POST'])
def api_telnet():
    data = request.get_json()
    if not data or 'ip_address' not in data or 'port' not in data:
        return jsonify({"error": "请求体中缺少 'ip_address' 或 'port'"}), 400
        
    ip_address = data['ip_address']
    port_str = data['port']
    timeout = data.get('timeout', 5) # 允许通过请求指定超时时间，默认为5秒

    try:
        port = int(port_str)
        timeout = int(timeout)
        if not (1 <= timeout <= 60): # 限制超时时间范围
            return jsonify({"error": "超时时间 (timeout) 必须在 1 到 60 秒之间"}), 400
    except ValueError:
        return jsonify({"error": "参数 'port' 和 'timeout' 必须是有效的整数"}), 400

    result = telnet_port(ip_address, port=port, timeout_seconds=timeout)
    return jsonify(result)

@net_tools_bp.route('/traceroute', methods=['POST'])
def api_traceroute():
    data = request.get_json()
    if not data or 'ip_address' not in data:
        return jsonify({"error": "请求体中缺少 'ip_address'"}), 400
    ip_address = data['ip_address']
    max_hops = data.get('max_hops', 30)
    timeout = data.get('timeout', 5)
    try:
        max_hops = int(max_hops)
        timeout = int(timeout)
        if not (1 <= max_hops <= 64):
            return jsonify({"error": "最大跳数 (max_hops) 必须在 1 到 64 之间"}), 400
        if not (1 <= timeout <= 30):
            return jsonify({"error": "超时时间 (timeout) 必须在 1 到 30 秒之间"}), 400
    except ValueError:
        return jsonify({"error": "参数 'max_hops' 和 'timeout' 必须是有效的整数"}), 400
    result = traceroute_ip(ip_address, max_hops=max_hops, timeout_seconds=timeout)
    return jsonify(result)

@net_tools_bp.route('/portscan', methods=['POST'])
def api_portscan():
    data = request.get_json()
    if not data or 'ip_address' not in data or 'ports' not in data:
        return jsonify({"error": "请求体中缺少 'ip_address' 或 'ports'"}), 400
    ip_address = data['ip_address']
    ports_str = data['ports']
    timeout = int(data.get('timeout', 1))
    
    # 超时时间范围限制
    if not (1 <= timeout <= 10):
        return jsonify({"error": "超时时间 (timeout) 必须在 1 到 10 秒之间"}), 400
    
    # 解析端口字符串，支持格式: 80,443,8000-8100,8443
    ports = set()
    try:
        for part in str(ports_str).split(','):
            part = part.strip()
            if '-' in part:
                start, end = part.split('-')
                start, end = int(start.strip()), int(end.strip())
                # 限制端口范围大小，防止过大的扫描请求
                if end - start > 1000:
                    return jsonify({"error": "单次端口范围扫描不能超过1000个端口"}), 400
                ports.update(range(start, end+1))
            elif part.isdigit():
                ports.add(int(part))
            else:
                return jsonify({"error": f"无效的端口格式: {part}"}), 400
    except ValueError:
        return jsonify({"error": "端口格式错误，正确格式例如: 80,443,8000-8100"}), 400
    
    # 限制最大端口数量
    if len(ports) > 2000:
        return jsonify({"error": "单次扫描端口数量不能超过2000个"}), 400
    
    # 验证所有端口都在有效范围内
    if not all(1 <= p <= 65535 for p in ports):
        return jsonify({"error": "端口号必须在 1-65535 范围内"}), 400
    
    if not ports:
        return jsonify({"error": "未提供有效端口"}), 400
    
    result = scan_ports(ip_address, sorted(ports), timeout=timeout)
    return jsonify(result)

@net_tools_bp.route('/portscan_detail', methods=['POST'])
def api_portscan_detail():
    """增强版端口扫描API，返回详细的服务信息和进程信息"""
    data = request.get_json()
    if not data or 'ip_address' not in data or 'ports' not in data:
        return jsonify({"error": "请求体中缺少 'ip_address' 或 'ports'"}), 400
    
    ip_address = data['ip_address']
    ports_str = data['ports']
    timeout = int(data.get('timeout', 1))
    
    # 超时时间范围限制
    if not (1 <= timeout <= 10):
        return jsonify({"error": "超时时间 (timeout) 必须在 1 到 10 秒之间"}), 400
    
    # 解析端口字符串，支持格式: 80,443,8000-8100,8443
    ports = set()
    try:
        for part in str(ports_str).split(','):
            part = part.strip()
            if '-' in part:
                start, end = part.split('-')
                start, end = int(start.strip()), int(end.strip())
                # 限制端口范围大小，防止过大的扫描请求
                if end - start > 1000:
                    return jsonify({"error": "单次端口范围扫描不能超过1000个端口"}), 400
                ports.update(range(start, end+1))
            elif part.isdigit():
                ports.add(int(part))
            else:
                return jsonify({"error": f"无效的端口格式: {part}"}), 400
    except ValueError:
        return jsonify({"error": "端口格式错误，正确格式例如: 80,443,8000-8100"}), 400
    
    # 限制最大端口数量
    if len(ports) > 1000:
        return jsonify({"error": "详细扫描模式下，单次扫描端口数量不能超过1000个"}), 400
    
    # 验证所有端口都在有效范围内
    if not all(1 <= p <= 65535 for p in ports):
        return jsonify({"error": "端口号必须在 1-65535 范围内"}), 400
    
    if not ports:
        return jsonify({"error": "未提供有效端口"}), 400
    
    result = scan_ports_with_details(ip_address, sorted(ports), timeout=timeout)
    return jsonify(result)

@net_tools_bp.route('/', methods=['GET'])
@net_tools_bp.route('/ui', methods=['GET']) # 添加一个 /ui 路径，更明确
def network_tools_ui():
    """提供网络工具的前端用户界面"""
    return render_template('net_tools.html')

# 以下代码已被注释掉，因为它与上面的路由冲突
# @net_tools_bp.route('/', methods=['GET'])
# def tools_index_page():
#     # 为了简单起见，我们直接返回一个简单的说明性HTML
#     # 在实际项目中，你会使用 render_template 和一个HTML文件
#     return '''
#     <h1>网络小工具 API</h1>
#     <p>这是一个提供网络诊断工具的API模块。</p>
#     <h2>Ping 工具</h2>
#     <p>POST 请求到 <code>/tools/ping</code></p>
#     <p>请求体 (JSON): <code>{"ip_address": "target_ip_or_domain", "packets": 4, "timeout": 10}</code> (packets 和 timeout 是可选的)</p>
#     <h2>Telnet 工具</h2>
#     <p>POST 请求到 <code>/tools/telnet</code></p>
#     <p>请求体 (JSON): <code>{"ip_address": "target_ip_or_domain", "port": 80, "timeout": 5}</code> (timeout 是可选的)</p>
#     ''' 