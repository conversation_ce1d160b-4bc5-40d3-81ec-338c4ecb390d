// 服务器管理
// 页面加载完成后自动加载分组信息
document.addEventListener('DOMContentLoaded', function() {
    // 添加全屏模态框样式
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('shown.bs.modal', function() {
            document.body.classList.add('modal-open');
        });
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.classList.remove('modal-open');
        });
    });

    // 初始化侧边栏切换功能
    initSidebarToggle();

    // 加载数据
    loadGroups();
    loadServers();
    loadTasks();
    loadPlaybooks();
    
    // 更新仪表盘统计
    updateDashboardStats();
    
    // 监听哈希变化事件
    window.addEventListener('hashchange', handleHashChange);
    
    // 初始加载时处理当前哈希
    handleHashChange();
});

// 处理哈希变化，根据哈希值显示对应的区域
function handleHashChange() {
    const hash = window.location.hash;
    if (hash) {
        const sectionId = hash.substring(1); // 去掉开头的#
        const section = document.getElementById(sectionId);
        
        if (section) {
            // 隐藏所有section
            document.querySelectorAll('.section').forEach(s => {
                s.style.display = 'none';
            });
            
            // 显示当前section
            section.style.display = 'block';
            
            // 更新导航栏活动状态
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === hash) {
                    link.classList.add('active');
                }
            });
        }
    } else {
        // 没有哈希值时默认显示服务器管理
        document.querySelectorAll('.section').forEach(s => {
            s.style.display = s.id === 'servers' ? 'block' : 'none';
        });
        
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#servers') {
                link.classList.add('active');
            }
        });
    }
}

// 初始化侧边栏切换功能
function initSidebarToggle() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const contentWrapper = document.querySelector('.content-wrapper');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            contentWrapper.classList.toggle('active');
        });
    }
    
    // 为侧边栏导航链接添加点击事件 - 不阻止默认行为，让哈希变化事件处理显示逻辑
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.addEventListener('click', function() {
            // 在移动设备上，点击导航链接后自动收起侧边栏
            if (window.innerWidth < 768) {
                sidebar.classList.remove('active');
                contentWrapper.classList.remove('active');
            }
        });
    });
}

// 更新仪表盘统计数据
function updateDashboardStats() {
    // 获取服务器数量
    fetch('/ansible/api/servers')
        .then(response => response.json())
        .then(servers => {
            document.getElementById('server-count').textContent = servers.length;
        });
    
    // 获取任务数量
    fetch('/ansible/api/tasks')
        .then(response => response.json())
        .then(tasks => {
            document.getElementById('task-count').textContent = tasks.length;
        });
    
    // 获取Playbook数量
    fetch('/ansible/api/playbooks')
        .then(response => response.json())
        .then(playbooks => {
            document.getElementById('playbook-count').textContent = playbooks.length;
        });
}

function loadGroups() {
    fetch('/ansible/api/groups')
        .then(response => response.json())
        .then(groups => {
            const groupList = document.getElementById('server-groups');
            if (groups.length === 0) {
                groupList.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>暂无服务器分组，请先添加分组
                    </div>
                `;
            } else {
                groupList.innerHTML = `
                    <div class="card mb-3">
                        <div class="card-header">
                            <i class="bi bi-folder me-2"></i>服务器分组
                        </div>
                        <div class="card-body">
                            <div class="row">
                                ${groups.map(group => `
                                    <div class="col-md-3 mb-3">
                                        <div class="card group-card h-100">
                                            <div class="card-body">
                                                <h6 class="card-title d-flex align-items-center">
                                                    <i class="bi bi-folder-fill me-2 text-primary"></i>${group.name}
                                                </h6>
                                                <p class="card-text small text-muted">${group.description || '无描述'}</p>
                                                <div class="mt-2">
                                                    <span class="badge bg-info mb-2">
                                                        <i class="bi bi-server me-1"></i>${group.server_count} 台服务器
                                                    </span>
                                                </div>
                                                <div class="btn-group btn-group-sm mt-2">
                                                    <button class="btn btn-outline-primary" onclick="showGroupServers(${group.id})">
                                                        <i class="bi bi-eye me-1"></i>查看服务器
                                                    </button>
                                                    <button class="btn btn-outline-secondary" onclick="showEditGroupForm(${group.id}, '${group.name}', '${group.description || ''}')">
                                                        <i class="bi bi-pencil me-1"></i>编辑
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="deleteGroup(${group.id})">
                                                        <i class="bi bi-trash me-1"></i>删除
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // 更新Playbook表单中的服务器组选择
            const playbookHosts = document.getElementById('playbook-hosts');
            if (playbookHosts) {
                playbookHosts.innerHTML = `
                    <option value="">请选择服务器组</option>
                    ${groups.map(group => `<option value="${group.name}">${group.name}</option>`).join('')}
                `;
            }
        });
}

function loadServers() {
    fetch('/ansible/api/servers')
        .then(response => response.json())
        .then(servers => {
            const serverList = document.getElementById('server-list');
            if (servers.length === 0) {
                serverList.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>暂无服务器，请先添加服务器
                    </div>
                `;
            } else {
                serverList.innerHTML = `
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="bi bi-hdd me-2"></i>主机名</th>
                                <th><i class="bi bi-globe me-2"></i>IP地址</th>
                                <th><i class="bi bi-door-open me-2"></i>SSH端口</th>
                                <th><i class="bi bi-folder me-2"></i>所属分组</th>
                                <th><i class="bi bi-info-circle me-2"></i>描述</th>
                                <th><i class="bi bi-gear me-2"></i>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${servers.map(server => `
                                <tr>
                                    <td>${server.hostname}</td>
                                    <td>${server.ip_address}</td>
                                    <td>${server.ssh_port}</td>
                                    <td>
                                        ${server.group_name ? 
                                            `<span class="badge bg-info"><i class="bi bi-folder-fill me-1"></i>${server.group_name}</span>` : 
                                            '<span class="badge bg-secondary">未分组</span>'}
                                    </td>
                                    <td>${server.description || '-'}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="showEditServer(${server.id})">
                                                <i class="bi bi-pencil me-1"></i>编辑
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteServer(${server.id})">
                                                <i class="bi bi-trash me-1"></i>删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            }
            
            // 更新仪表盘统计
            document.getElementById('server-count').textContent = servers.length;
        });
}

function showAddGroupForm() {
    const groupList = document.getElementById('server-groups');
    groupList.innerHTML = `
        <div class="card">
            <div class="card-header">
                <i class="bi bi-folder-plus me-2"></i>添加服务器分组
            </div>
            <div class="card-body">
                <form id="group-form" class="mb-3">
                    <div class="mb-3">
                        <label class="form-label">分组名称</label>
                        <input type="text" class="form-control" id="group-name" required placeholder="输入分组名称">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <input type="text" class="form-control" id="group-description" placeholder="输入分组描述（可选）">
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="loadGroups()">
                            <i class="bi bi-x-circle me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="addGroup()">
                            <i class="bi bi-save me-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
}

function addGroup() {
    const name = document.getElementById('group-name').value;
    const description = document.getElementById('group-description').value;

    if (!name) {
        showToast('error', '分组名称不能为空');
        return;
    }

    showLoading('正在添加分组...');

    fetch('/ansible/api/groups', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name,
            description
        })
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || '添加分组失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', data.message);
        loadGroups();
        updateDashboardStats();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '添加分组失败：' + error.message);
    });
}

function loadTasks() {
    fetch('/ansible/api/tasks')
        .then(response => response.json())
        .then(tasks => {
            const taskList = document.getElementById('task-list');
            taskList.innerHTML = `
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="bi bi-hash me-2"></i>ID</th>
                            <th><i class="bi bi-card-text me-2"></i>任务名称</th>
                            <th><i class="bi bi-code-slash me-2"></i>执行内容</th>
                            <th><i class="bi bi-server me-2"></i>目标服务器</th>
                            <th><i class="bi bi-clock me-2"></i>创建时间</th>
                            <th><i class="bi bi-check-circle me-2"></i>状态</th>
                            <th><i class="bi bi-gear me-2"></i>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tasks.map(task => `
                            <tr>
                                <td>${task.id}</td>
                                <td>${task.task_name || task.name || '未命名任务'}</td>
                                <td>
                                    <a href="#" onclick="viewExecutionContent('${task.task_type}', ${task.id}); return false;" 
                                       class="text-decoration-none" title="点击查看详细内容">
                                        <i class="bi bi-${task.task_type === 'playbook' ? 'file-earmark-code' : 'terminal'} me-1"></i>
                                        ${task.task_type === 'playbook' ? 'playbook' : '命令'}
                                    </a>
                                </td>
                                <td>
                                    <a href="#" onclick="viewTargetServers(${task.id}); return false;" 
                                       class="text-decoration-none" title="点击查看目标服务器">
                                        <span class="badge bg-info">
                                            <i class="bi bi-server me-1"></i>${task.target_type === 'group' ? '服务器组' : '单台服务器'}
                                        </span>
                                    </a>
                                </td>
                                <td>${task.created_at}</td>
                                <td>
                                    ${getStatusBadge(task.status)}
                                </td>
                                                                <td>                                    <div class="btn-group btn-group-sm">                                        <button class="btn btn-outline-primary" onclick="viewTaskResult(${task.id})">                                            <i class="bi bi-eye me-1"></i>详情                                        </button>                                        <button class="btn btn-outline-info" onclick="viewTaskExecutionHistory(${task.id})">                                            <i class="bi bi-clock-history me-1"></i>执行历史                                        </button>                                        <button class="btn btn-outline-secondary" onclick="showEditTaskForm(${task.id})">                                            <i class="bi bi-pencil me-1"></i>编辑                                        </button>                                        <button class="btn btn-outline-success" onclick="executeTask(${task.id})">                                            <i class="bi bi-play me-1"></i>执行                                        </button>                                        <button class="btn btn-outline-danger" onclick="deleteTask(${task.id})">                                            <i class="bi bi-trash me-1"></i>删除                                        </button>                                    </div>                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('task-list').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>加载任务列表失败
                </div>
            `;
        });
}

// 获取任务状态徽章
function getStatusBadge(status) {
    switch(status) {
        case 'success':
        case 'completed':
            return '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>成功</span>';
        case 'failed':
            return '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>失败</span>';
        case 'running':
            return '<span class="badge bg-warning"><i class="bi bi-hourglass-split me-1"></i>运行中</span>';
        case 'pending':
            return '<span class="badge bg-secondary"><i class="bi bi-clock me-1"></i>等待中</span>';
        default:
            return '<span class="badge bg-info"><i class="bi bi-question-circle me-1"></i>未知</span>';
    }
}

function loadPlaybooks() {
    showLoading('正在加载Playbook列表...');
    
    fetch('/ansible/api/playbooks')
        .then(response => {
            hideLoading();
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || '获取Playbook列表失败');
                });
            }
            return response.json();
        })
        .then(playbooks => {
            const playbookList = document.getElementById('playbook-list');
            
            if (playbooks.length === 0) {
                playbookList.innerHTML = '<div class="alert alert-info">暂无Playbook，请点击"添加Playbook"按钮创建</div>';
                return;
            }
            
            playbookList.innerHTML = `
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th># ID</th>
                            <th>Playbook名称</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${playbooks.map(playbook => `
                            <tr>
                                <td>${playbook.id}</td>
                                <td>${playbook.name}</td>
                                <td>${playbook.created_at || ''}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewPlaybookContent(${playbook.id})">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary me-1" onclick="editPlaybook(${playbook.id})">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deletePlaybook(${playbook.id})">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        })
        .catch(error => {
            hideLoading();
            showToast('error', '获取Playbook列表失败：' + error.message);
        });
}

// 显示添加任务表单
function showAddTaskForm() {
    const taskList = document.getElementById('task-list');
    taskList.innerHTML = `
        <div class="card">
            <div class="card-header">
                <i class="bi bi-plus-circle me-2"></i>创建任务
            </div>
            <div class="card-body">
                <form id="task-form" class="mb-3">
                    <div class="mb-3">
                        <label class="form-label">任务名称</label>
                        <input type="text" class="form-control" id="task-name" required placeholder="输入任务名称">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">任务类型</label>
                        <select class="form-select" id="task-type" required>
                            <option value="">请选择任务类型</option>
                            <option value="command">命令</option>
                            <option value="playbook">Playbook</option>
                        </select>
                    </div>
                    <div class="mb-3" id="command-input-container">
                        <label class="form-label">命令</label>
                        <input type="text" class="form-control" id="task-command" placeholder="输入要执行的命令">
                    </div>
                    <div class="mb-3 d-none" id="playbook-input-container">
                        <label class="form-label">Playbook</label>
                        <select class="form-select" id="task-playbook">
                            <option value="">请选择Playbook</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">目标类型</label>
                        <select class="form-select" id="target-type" required>
                            <option value="">请选择目标类型</option>
                            <option value="server">单台服务器</option>
                            <option value="group">服务器组</option>
                        </select>
                    </div>
                    <div class="mb-3 d-none" id="server-target-container">
                        <label class="form-label">目标服务器</label>
                        <select class="form-select" id="target-server">
                            <option value="">请选择服务器</option>
                        </select>
                    </div>
                    <div class="mb-3 d-none" id="group-target-container">
                        <label class="form-label">目标服务器组</label>
                        <select class="form-select" id="target-group">
                            <option value="">请选择服务器组</option>
                        </select>
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="loadTasks()">
                            <i class="bi bi-x-circle me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="addTask()">
                            <i class="bi bi-save me-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    // 加载服务器列表
    fetch('/ansible/api/servers')
        .then(response => response.json())
        .then(servers => {
            const serverSelect = document.getElementById('target-server');
            servers.forEach(server => {
                const option = document.createElement('option');
                option.value = server.id;
                option.textContent = `${server.hostname} (${server.ip_address})`;
                serverSelect.appendChild(option);
            });
        });
    
    // 加载服务器组列表
    fetch('/ansible/api/server-groups')
        .then(response => response.json())
        .then(groups => {
            const groupSelect = document.getElementById('target-group');
            groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.name;
                groupSelect.appendChild(option);
            });
        });
    
    // 加载Playbook列表
    fetch('/ansible/api/playbooks')
        .then(response => response.json())
        .then(playbooks => {
            const playbookSelect = document.getElementById('task-playbook');
            playbooks.forEach(playbook => {
                const option = document.createElement('option');
                option.value = playbook.id;
                option.textContent = playbook.name;
                playbookSelect.appendChild(option);
            });
        });
    
    // 添加任务类型切换事件
    document.getElementById('task-type').addEventListener('change', function() {
        const commandContainer = document.getElementById('command-input-container');
        const playbookContainer = document.getElementById('playbook-input-container');
        
        if (this.value === 'command') {
            commandContainer.classList.remove('d-none');
            playbookContainer.classList.add('d-none');
        } else if (this.value === 'playbook') {
            commandContainer.classList.add('d-none');
            playbookContainer.classList.remove('d-none');
        } else {
            commandContainer.classList.add('d-none');
            playbookContainer.classList.add('d-none');
        }
    });
    
    // 添加目标类型切换事件
    document.getElementById('target-type').addEventListener('change', function() {
        const serverContainer = document.getElementById('server-target-container');
        const groupContainer = document.getElementById('group-target-container');
        
        if (this.value === 'server') {
            serverContainer.classList.remove('d-none');
            groupContainer.classList.add('d-none');
        } else if (this.value === 'group') {
            serverContainer.classList.add('d-none');
            groupContainer.classList.remove('d-none');
        } else {
            serverContainer.classList.add('d-none');
            groupContainer.classList.add('d-none');
        }
    });
}

// 添加任务
function addTask() {
    const name = document.getElementById('task-name').value;
    const taskType = document.getElementById('task-type').value;
    const command = document.getElementById('task-command').value;
    const playbookId = document.getElementById('task-playbook').value;
    const targetType = document.getElementById('target-type').value;
    const serverId = document.getElementById('target-server').value;
    const groupId = document.getElementById('target-group').value;
    
    if (!name) {
        showToast('error', '任务名称不能为空');
        return;
    }
    
    if (!taskType) {
        showToast('error', '请选择任务类型');
        return;
    }
    
    if (taskType === 'command' && !command) {
        showToast('error', '命令不能为空');
        return;
    }
    
    if (taskType === 'playbook' && !playbookId) {
        showToast('error', '请选择Playbook');
        return;
    }
    
    if (!targetType) {
        showToast('error', '请选择目标类型');
        return;
    }
    
    if (targetType === 'server' && !serverId) {
        showToast('error', '请选择目标服务器');
        return;
    }
    
    if (targetType === 'group' && !groupId) {
        showToast('error', '请选择目标服务器组');
        return;
    }
    
    const taskData = {
        task_name: name,
        task_type: taskType,
        target_type: targetType, // 添加 target_type 到发送的数据中
        target_servers: targetType === 'server' ? [serverId] : [groupId]  // 将目标转换为hosts数组
    };
    
    if (taskType === 'command') {
        taskData.command = command;
    } else {
        taskData.playbook_id = playbookId;
    }
    
    showLoading('正在创建任务...');
    
    fetch('/ansible/api/tasks', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || '创建任务失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', data.message || '任务创建成功');
        loadTasks();
        updateDashboardStats();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '创建任务失败：' + error.message);
    });
}

// 显示Playbook表单
function showPlaybookForm() {
    createPlaybook();
}

// 加载服务器组
function loadServerGroups(selectElementId) {
    const selectElement = document.getElementById(selectElementId);
    
    // 检查元素是否存在
    if (!selectElement) {
        console.error(`找不到ID为${selectElementId}的元素`);
        return;
    }
    
    // 清空现有选项（保留第一个默认选项）
    while (selectElement.options.length > 0) {
        selectElement.remove(0);
    }
    
    // 添加默认选项 - 根据不同的选择器ID添加不同的默认选项
    const defaultOption = document.createElement('option');
    if (selectElementId === 'server-group') {
        defaultOption.value = '';
        defaultOption.textContent = '请选择分组';
    } else {
        defaultOption.value = 'localhost';
        defaultOption.textContent = 'localhost';
    }
    selectElement.appendChild(defaultOption);
    
    // 从API获取服务器组
    fetch('/ansible/api/server-groups')
        .then(response => {
            if (!response.ok) {
                throw new Error('获取服务器组失败');
            }
            return response.json();
        })
        .then(groups => {
            // 添加服务器组选项
            groups.forEach(group => {
                const option = document.createElement('option');
                // 如果是添加服务器的下拉列表，使用组ID作为值
                if (selectElementId === 'server-group') {
                    option.value = group.id;
                    option.textContent = group.name;
                } else {
                    option.value = group.name;
                    option.textContent = group.name;
                }
                selectElement.appendChild(option);
            });
        })
        .catch(error => {
            console.error('加载服务器组失败:', error);
            // 如果API不可用，添加一些默认选项
            const defaultGroups = ['web_servers', 'db_servers', 'app_servers'];
            defaultGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group;
                option.textContent = group;
                selectElement.appendChild(option);
            });
        });
}

// 编辑Playbook
function editPlaybook(playbookId) {
    console.log('开始编辑Playbook:', playbookId);
    
    showLoading('正在加载Playbook详情...');
    
    fetch(`/ansible/api/playbooks/${playbookId}`)
        .then(response => {
            hideLoading();
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || '获取Playbook详情失败');
                });
            }
            return response.json();
        })
        .then(playbook => {
            // 显示模态框
            const modalElement = document.getElementById('edit-playbook-modal');
            if (!modalElement) {
                throw new Error('找不到编辑Playbook的模态框');
            }
            
            const modal = new bootstrap.Modal(modalElement);
            
            // 填充表单数据
            const idInput = document.getElementById('edit-playbook-id');
            const nameInput = document.getElementById('edit-playbook-name');
            const descriptionInput = document.getElementById('edit-playbook-description');
            const contentInput = document.getElementById('edit-playbook-content');
            
            if (!idInput || !nameInput || !contentInput) {
                throw new Error('找不到必要的表单元素');
            }
            
            // 确保数据有效
            idInput.value = playbook.id || '';
            nameInput.value = playbook.name || '';
            descriptionInput.value = playbook.description || '';
            contentInput.value = playbook.content || '';
            
            // 显示模态框
            modal.show();
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('error', '获取Playbook详情失败：' + error.message);
        });
}

// 更新Playbook
function updatePlaybook() {
    console.log('开始更新Playbook...');
    
    const idInput = document.getElementById('edit-playbook-id');
    const nameInput = document.getElementById('edit-playbook-name');
    const descriptionInput = document.getElementById('edit-playbook-description');
    const contentInput = document.getElementById('edit-playbook-content');
    
    if (!idInput || !nameInput || !contentInput) {
        showToast('error', '找不到必要的表单元素');
        return;
    }
    
    const playbookId = idInput.value;
    const name = nameInput.value.trim();
    const description = descriptionInput ? descriptionInput.value.trim() : '';
    const content = contentInput.value.trim();
    
    if (!name) {
        showToast('error', 'Playbook名称不能为空');
        return;
    }
    
    if (!content) {
        showToast('error', 'Playbook内容不能为空');
        return;
    }
    
    showLoading('正在更新Playbook...');
    
    fetch(`/ansible/api/playbooks/${playbookId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: name,
            content: content,
            description: description
        })
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(err => {
                throw new Error(err.message || '更新Playbook失败');
            });
        }
        return response.json();
    })
    .then(data => {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('edit-playbook-modal'));
        if (modal) {
            modal.hide();
        }
        
        // 显示成功消息
        showToast('success', 'Playbook更新成功');
        
        // 重新加载Playbook列表
        loadPlaybooks();
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showToast('error', error.message);
    });
}

function addPlaybook() {
    const name = document.getElementById('playbook-name').value;
    const content = document.getElementById('playbook-content').value;
    const description = document.getElementById('playbook-description').value;
    
    if (!name) {
        showToast('error', 'Playbook名称不能为空');
        return;
    }
    
    if (!content) {
        showToast('error', 'Playbook内容不能为空');
        return;
    }
    
    showLoading('正在创建Playbook...');
    
    fetch('/ansible/api/playbooks', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name,
            content,
            description
        })
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || '创建Playbook失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', data.message || 'Playbook创建成功');
        document.getElementById('playbook-editor').classList.add('d-none');
        loadPlaybooks();
        updateDashboardStats();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '创建Playbook失败：' + error.message);
    });
}

// 取消Playbook编辑
function cancelPlaybookEdit() {
    document.getElementById('playbook-editor').classList.add('d-none');
}

// 查看Playbook内容
function viewPlaybookContent(playbookId) {
    // 清理之前可能存在的模态框
    cleanupModals();
    
    showLoading('正在加载Playbook内容...');
    
    fetch(`/ansible/api/playbooks/${playbookId}`)
        .then(response => {
            hideLoading();
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || '获取Playbook内容失败');
                });
            }
            return response.json();
        })
        .then(playbook => {
            // 显示模态框
            const modalElement = document.getElementById('playbookContentModal');
            if (!modalElement) {
                throw new Error('找不到Playbook内容模态框');
            }
            
            const modal = new bootstrap.Modal(modalElement);
            document.getElementById('playbookContentTitle').innerHTML = `<i class="bi bi-file-earmark-code me-2"></i>Playbook内容: ${playbook.name}`;
            // 修复：使用正确的ID，避免与创建Playbook模态框中的元素ID冲突
            document.getElementById('viewPlaybookContent').value = playbook.content || '无内容';
            
            // 添加模态框关闭事件监听器
            modalElement.addEventListener('hidden.bs.modal', function() {
                cleanupModals();
            }, { once: true });
            
            modal.show();
        })
        .catch(error => {
            hideLoading();
            showToast('error', '获取Playbook内容失败：' + error.message);
        });
}

// 更新Playbook
function updatePlaybook(playbookId) {
    const name = document.getElementById('playbook-name').value;
    const content = document.getElementById('playbook-content').value;
    const description = document.getElementById('playbook-description').value;
    
    if (!name) {
        showToast('error', 'Playbook名称不能为空');
        return;
    }
    
    if (!content) {
        showToast('error', 'Playbook内容不能为空');
        return;
    }
    
    showLoading('正在更新Playbook...');
    
    fetch(`/ansible/api/playbooks/${playbookId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name,
            content,
            description
        })
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || '更新Playbook失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', data.message || 'Playbook更新成功');
        document.getElementById('playbook-editor').classList.add('d-none');
        loadPlaybooks();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '更新Playbook失败：' + error.message);
    });
}

// 删除Playbook
function deletePlaybook(playbookId) {
    showDeleteConfirm('确定要删除这个Playbook吗？', '删除Playbook', function(confirmed) {
        if (confirmed) {
            showLoading();
            
            fetch(`/ansible/api/playbooks/${playbookId}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('删除Playbook失败');
                }
                return response.json();
            })
            .then(data => {
                hideLoading();
                showToast('success', 'Playbook删除成功');
                loadPlaybooks();
                updateDashboardStats();
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showToast('error', '删除Playbook失败: ' + error.message);
            });
        }
    });
}

function viewTaskResult(taskId) {
    // 清理之前可能存在的模态框
    cleanupModals();
    
    // 显示加载指示器
    showLoading();
    
    fetch(`/ansible/api/tasks/${taskId}`)
        .then(response => {
            if (!response.ok) {
                hideLoading();
                showToast('error', '获取任务详情失败');
                return Promise.reject('获取任务详情失败');
            }
            return response.json();
        })
        .then(task => {
            const modalElement = document.getElementById('taskResultModal');
            if (!modalElement) {
                throw new Error('找不到任务结果模态框');
            }
            
            const modal = new bootstrap.Modal(modalElement);
            const titleElement = document.getElementById('taskResultTitle');
            const contentElement = document.getElementById('taskResultContent');
            
            if (!titleElement || !contentElement) {
                throw new Error('找不到任务结果模态框的标题或内容元素');
            }
            
            titleElement.textContent = `任务结果: ${task.task_name}`;
            
            // 简化显示，只显示执行结果
            let resultContent = '';
            
            // 检查任务是否有结果，无论状态如何
            if (task.result) {
                try {
                    // 尝试解析JSON结果
                    const resultObj = JSON.parse(task.result);
                    if (resultObj.formatted_output) {
                        resultContent = resultObj.formatted_output;
                    } else if (resultObj.stdout) {
                        // 如果有stdout但没有formatted_output，创建一个简单的格式化显示
                        resultContent = `<pre class="p-3 bg-light">${resultObj.stdout}</pre>`;
                    } else {
                        resultContent = `<pre>${JSON.stringify(resultObj, null, 2)}</pre>`;
                    }
                } catch (e) {
                    // 如果解析失败，显示原始结果
                    resultContent = `<pre>${task.result || '无结果'}</pre>`;
                }
            } else if (task.formatted_result) {
                // 直接使用格式化的结果
                resultContent = task.formatted_result;
            } else if (task.status === 'running') {
                // 任务尚未完成
                resultContent = `<div class="alert alert-warning">
                    <i class="bi bi-hourglass-split me-2"></i>
                    任务正在执行中，暂无结果可显示
                </div>`;
            } else {
                resultContent = `<div class="alert alert-info">无可用的执行结果</div>`;
            }
            
            // 将结果内容显示在contentElement区域
            contentElement.innerHTML = resultContent;
            
            // 添加模态框关闭事件监听器
            modalElement.addEventListener('hidden.bs.modal', function() {
                cleanupModals();
            }, { once: true });
            
            // 隐藏加载指示器
            hideLoading();
            
            // 显示模态框
            modal.show();
        })
        .catch(error => {
            console.error('获取任务详情失败:', error);
            hideLoading();
            showToast('error', '获取任务详情失败: ' + error.message);
        });
}

// 执行任务
function executeTask(taskId) {
    showPasswordPrompt('请输入跳板机密码', '执行任务', function(password) {
        if (password !== null) {
            // 立即更新UI，将任务状态显示为"运行中"
            const taskRows = document.querySelectorAll('tr td:first-child');
            for (let i = 0; i < taskRows.length; i++) {
                if (taskRows[i].textContent.trim() == taskId) {
                    const statusCell = taskRows[i].parentElement.querySelector('td:nth-child(5)');
                    if (statusCell) {
                        statusCell.innerHTML = getStatusBadge('running');
                    }
                    break;
                }
            }
            
            fetch(`/ansible/api/tasks/${taskId}/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    password: password
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '执行任务失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                showToast('success', data.message || '任务执行已开始');
                // 延迟刷新任务列表，给后端一些时间来更新任务状态
                setTimeout(() => {
                    loadTasks();
                }, 1000);
            })
            .catch(error => {
                showToast('error', '执行任务失败：' + error.message);
                // 如果执行失败，重新加载任务列表以恢复正确的状态
                loadTasks();
            });
        }
    });
}

// 删除任务
function deleteTask(taskId) {
    showDeleteConfirm('确定要删除这个任务吗？', '删除任务', function(confirmed) {
        if (confirmed) {
            showLoading('正在删除任务...');
            
            fetch(`/ansible/api/tasks/${taskId}`, {
                method: 'DELETE'
            })
            .then(response => {
                hideLoading();
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '删除任务失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                showToast('success', data.message || '任务删除成功');
                loadTasks();
                updateDashboardStats();
            })
            .catch(error => {
                hideLoading();
                showToast('error', '删除任务失败：' + error.message);
            });
        }
    });
}

// 显示分组服务器
function showGroupServers(groupId) {
    showLoading('正在加载分组服务器...');
    
    fetch(`/ansible/api/server-groups/${groupId}/servers`)
        .then(response => {
            hideLoading();
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || '获取分组服务器失败');
                });
            }
            return response.json();
        })
        .then(servers => {
            const serverList = document.getElementById('server-list');
            if (servers.length === 0) {
                serverList.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>该分组下暂无服务器
                    </div>
                `;
            } else {
                serverList.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="bi bi-server me-2"></i>分组服务器列表</h5>
                        <button class="btn btn-secondary btn-sm" onclick="loadServers()">
                            <i class="bi bi-arrow-left me-1"></i>返回全部服务器
                        </button>
                    </div>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="bi bi-hdd me-2"></i>主机名</th>
                                <th><i class="bi bi-globe me-2"></i>IP地址</th>
                                <th><i class="bi bi-door-open me-2"></i>SSH端口</th>
                                <th><i class="bi bi-folder me-2"></i>所属分组</th>
                                <th><i class="bi bi-info-circle me-2"></i>描述</th>
                                <th><i class="bi bi-gear me-2"></i>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${servers.map(server => `
                                <tr>
                                    <td>${server.hostname}</td>
                                    <td>${server.ip_address}</td>
                                    <td>${server.ssh_port}</td>
                                    <td>
                                        <span class="badge bg-info">
                                            <i class="bi bi-server me-1"></i>${server.group_name}
                                        </span>
                                    </td>
                                    <td>${server.description || '-'}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="showEditServer(${server.id})">
                                                <i class="bi bi-pencil me-1"></i>编辑
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteServer(${server.id})">
                                                <i class="bi bi-trash me-1"></i>删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            }
        })
        .catch(error => {
            hideLoading();
            showToast('error', '获取分组服务器失败：' + error.message);
        });
}

// 显示编辑服务器表单
function showEditServer(serverId) {
    showLoading('正在加载服务器信息...');
    
    // 获取服务器信息
    fetch(`/ansible/api/servers/${serverId}`)
        .then(response => {
            hideLoading();
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || '获取服务器信息失败');
                });
            }
            return response.json();
        })
        .then(server => {
            const serverList = document.getElementById('server-list');
            serverList.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-pencil-square me-2"></i>编辑服务器
                    </div>
                    <div class="card-body">
                        <form id="server-form" class="mb-3">
                            <input type="hidden" id="server-id" value="${server.id}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">主机名</label>
                                    <input type="text" class="form-control" id="hostname" value="${server.hostname}" required placeholder="输入主机名">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">IP地址</label>
                                    <input type="text" class="form-control" id="ip_address" value="${server.ip_address}" required placeholder="输入IP地址">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">SSH端口</label>
                                    <input type="number" class="form-control" id="ssh_port" value="${server.ssh_port}" placeholder="SSH端口">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">所属分组</label>
                                    <select class="form-select" id="group_id">
                                        <option value="">请选择分组</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">描述</label>
                                <input type="text" class="form-control" id="description" value="${server.description || ''}" placeholder="输入服务器描述（可选）">
                            </div>
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-secondary" onclick="loadServers()">
                                    <i class="bi bi-x-circle me-1"></i>取消
                                </button>
                                <button type="button" class="btn btn-primary" onclick="updateServer()">
                                    <i class="bi bi-save me-1"></i>保存
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            
            // 加载分组列表
            fetch('/ansible/api/server-groups')
                .then(response => response.json())
                .then(groups => {
                    const groupSelect = document.getElementById('group_id');
                    groups.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group.id;
                        option.textContent = group.name;
                        if (server.group_id === group.id) {
                            option.selected = true;
                        }
                        groupSelect.appendChild(option);
                    });
                });
        })
        .catch(error => {
            hideLoading();
            showToast('error', '获取服务器信息失败：' + error.message);
        });
}

// 更新服务器信息
function updateServer() {
    const serverId = document.getElementById('server-id').value;
    const hostname = document.getElementById('hostname').value;
    const ip_address = document.getElementById('ip_address').value;
    const ssh_port = document.getElementById('ssh_port').value;
    const group_id = document.getElementById('group_id').value;
    const description = document.getElementById('description').value;
    
    if (!hostname) {
        showToast('error', '主机名不能为空');
        return;
    }
    
    if (!ip_address) {
        showToast('error', 'IP地址不能为空');
        return;
    }
    
    const serverData = {
        hostname,
        ip_address: ip_address,
        ssh_port: ssh_port,
        description: description
    };
    
    if (group_id) {
        serverData.group_id = group_id;
    }
    
    showLoading('正在更新服务器信息...');
    
    fetch(`/ansible/api/servers/${serverId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(serverData)
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || '更新服务器失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', '服务器信息更新成功');
        loadServers();
        updateDashboardStats();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '更新服务器失败：' + error.message);
    });
}

// 显示加载中
function showLoading(message = '正在处理，请稍候...') {
    let loadingIndicator = document.getElementById('loading-indicator');
    
    // 如果loading-indicator不存在，创建一个
    if (!loadingIndicator) {
        loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'loading-indicator';
        loadingIndicator.className = 'loading-overlay';
        
        const loadingContent = document.createElement('div');
        loadingContent.className = 'loading-content';
        
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-light';
        spinner.setAttribute('role', 'status');
        
        const loadingMessage = document.createElement('div');
        loadingMessage.className = 'message text-light mt-2';
        
        loadingContent.appendChild(spinner);
        loadingContent.appendChild(loadingMessage);
        loadingIndicator.appendChild(loadingContent);
        
        document.body.appendChild(loadingIndicator);
    }
    
    const loadingMessage = loadingIndicator.querySelector('.message');
    if (loadingMessage) {
        loadingMessage.textContent = message;
    }
    
    loadingIndicator.classList.remove('d-none');
}

// 隐藏加载中
function hideLoading() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.classList.add('d-none');
    }
}

// 显示Toast通知
function showToast(type, message, duration = 3000) {
    // 确保toast容器存在
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;
    
    // 设置图标
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="bi bi-check-circle-fill"></i>';
            title = '成功';
            break;
        case 'error':
            icon = '<i class="bi bi-x-circle-fill"></i>';
            title = '错误';
            break;
        case 'warning':
            icon = '<i class="bi bi-exclamation-triangle-fill"></i>';
            title = '警告';
            break;
        case 'info':
        default:
            icon = '<i class="bi bi-info-circle-fill"></i>';
            title = '提示';
            break;
    }
    
    // 构建toast内容
    toast.innerHTML = `
        <div class="toast-icon">${icon}</div>
        <div class="toast-content">
            <div class="toast-title">${title}</div>
            <div class="toast-message">${message}</div>
        </div>
        <button class="toast-close" onclick="this.parentElement.classList.add('toast-out'); setTimeout(() => this.parentElement.remove(), 300);">
            <i class="bi bi-x"></i>
        </button>
    `;
    
    // 添加到容器
    toastContainer.appendChild(toast);
    
    // 显示toast
    setTimeout(() => toast.classList.add('show'), 10);
    
    // 设置自动关闭
    if (duration > 0) {
        setTimeout(() => {
            toast.classList.add('toast-out');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }
}

// 删除服务器分组
function deleteGroup(groupId) {
    showDeleteConfirm('确定要删除这个服务器分组吗？', '删除服务器分组', function(confirmed) {
        if (confirmed) {
            showLoading();
            
            fetch(`/ansible/api/groups/${groupId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('删除服务器分组失败');
                }
                return response.json();
            })
            .then(data => {
                hideLoading();
                showToast('success', '服务器分组删除成功');
                loadGroups(); // 重新加载服务器分组
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showToast('error', '删除服务器分组失败: ' + error.message);
            });
        }
    });
}

// 删除服务器
function deleteServer(serverId) {
    showDeleteConfirm('确定要删除这个服务器吗？', '删除服务器', function(confirmed) {
        if (confirmed) {
            showLoading('正在删除服务器...');
            
            fetch(`/ansible/api/servers/${serverId}`, {
                method: 'DELETE'
            })
            .then(response => {
                hideLoading();
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || '删除服务器失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                showToast('success', '服务器删除成功');
                loadServers(); // 重新加载服务器列表
                loadGroups();  // 重新加载分组信息，更新分组中的服务器数量
                updateDashboardStats(); // 更新仪表盘统计
            })
            .catch(error => {
                hideLoading();
                showToast('error', '删除服务器失败：' + error.message);
            });
        }
    });
}

// 显示添加服务器模态框
function showAddServerForm() {
    const serverList = document.getElementById('server-list');
    serverList.innerHTML = `
        <div class="card">
            <div class="card-header">
                <i class="bi bi-plus-circle me-2"></i>添加服务器
            </div>
            <div class="card-body">
                <form id="server-form" class="mb-3">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">主机名</label>
                            <input type="text" class="form-control" id="hostname" required placeholder="输入主机名">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">IP地址</label>
                            <input type="text" class="form-control" id="ip_address" required placeholder="输入IP地址">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">SSH端口</label>
                            <input type="number" class="form-control" id="ssh_port" value="22" placeholder="SSH端口">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">所属分组</label>
                            <select class="form-select" id="group_id">
                                <option value="">请选择分组</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <input type="text" class="form-control" id="description" placeholder="输入服务器描述（可选）">
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="loadServers()">
                            <i class="bi bi-x-circle me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="addServer()">
                            <i class="bi bi-save me-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    // 加载分组列表
    fetch('/ansible/api/server-groups')
        .then(response => response.json())
        .then(groups => {
            const groupSelect = document.getElementById('group_id');
            groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.name;
                groupSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('加载服务器组失败:', error);
            showToast('error', '加载服务器组失败');
        });
}

// 添加服务器
function addServer() {
    const hostname = document.getElementById('hostname').value;
    const ipAddress = document.getElementById('ip_address').value;
    const sshPort = document.getElementById('ssh_port').value;
    const groupId = document.getElementById('group_id').value;
    const description = document.getElementById('description').value;
    
    if (!hostname) {
        showToast('error', '主机名不能为空');
        return;
    }
    
    if (!ipAddress) {
        showToast('error', 'IP地址不能为空');
        return;
    }
    
    const serverData = {
        hostname,
        ip_address: ipAddress,
        ssh_port: sshPort,
        description: description
    };
    
    if (groupId) {
        serverData.group_id = groupId;
    }
    
    showLoading('正在添加服务器...');
    
    fetch('/ansible/api/servers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(serverData)
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(err => {
                throw new Error(err.message || '添加服务器失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', '服务器添加成功');
        loadServers();
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', error.message);
    });
}

// 添加CSS类到document.head
function addCssToHead(cssText) {
    if (!document.getElementById('fullscreen-modal-styles')) {
        const style = document.createElement('style');
        style.id = 'fullscreen-modal-styles';
        style.textContent = cssText;
        document.head.appendChild(style);
    }
}

// 初始化全屏切换功能
function initFullscreenToggle() {
    const fullscreenButton = document.getElementById('toggleFullscreen');
    if (fullscreenButton) {
        fullscreenButton.addEventListener('click', function() {
            const modal = document.querySelector('.modal-dialog.task-result-modal');
            modal.classList.toggle('fullscreen-modal');
            
            if (modal.classList.contains('fullscreen-modal')) {
                this.innerHTML = '<i class="bi bi-fullscreen-exit"></i> 退出全屏';
                modal.style.maxWidth = '100%';
                modal.style.margin = '0';
                document.querySelector('.modal-content.task-result-content').style.height = '100vh';
                document.querySelector('.modal-content.task-result-content').style.borderRadius = '0';
            } else {
                this.innerHTML = '<i class="bi bi-arrows-fullscreen"></i> 全屏';
                modal.style.maxWidth = '90%';
                modal.style.margin = '2rem auto';
                document.querySelector('.modal-content.task-result-content').style.height = '90vh';
                document.querySelector('.modal-content.task-result-content').style.borderRadius = '0.5rem';
            }
        });
    }
}

// 保存Playbook
function savePlaybook() {
    const nameInput = document.getElementById('playbookName');
    const contentInput = document.getElementById('playbookContent');
    const descriptionInput = document.getElementById('playbookDescription');
    
    if (!nameInput || !contentInput) {
        showToast('error', '找不到必要的表单元素');
        return;
    }
    
    const name = nameInput.value.trim();
    const content = contentInput.value.trim();
    const description = descriptionInput ? descriptionInput.value.trim() : '';
    
    // 详细的字段验证
    if (!name) {
        showToast('error', '请填写Playbook名称');
        return;
    }
    
    if (!content) {
        showToast('error', '请填写Playbook内容');
        return;
    }
    
    showLoading('正在保存Playbook...');
    
    // 构建请求数据
    const playbookData = {
        name: name,
        content: content,
        description: description
    };
    
    console.log('发送的数据:', playbookData); // 调试用
    
    // 发送请求
    fetch('/ansible/api/playbooks', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(playbookData)
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(err => {
                throw new Error(err.message || '保存Playbook失败');
            });
        }
        return response.json();
    })
    .then(data => {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('createPlaybookModal'));
        if (modal) {
            modal.hide();
        }
        
        // 显示成功消息
        showToast('success', 'Playbook保存成功');
        
        // 重新加载Playbook列表
        loadPlaybooks();
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showToast('error', error.message);
    });
}

// 显示自定义确认对话框
function showConfirm(message, title, callback) {
    const modalElement = document.getElementById('confirmActionModal');
    const modal = new bootstrap.Modal(modalElement);
    const confirmTitle = document.getElementById('confirmActionTitle');
    const confirmMessage = document.getElementById('confirmActionMessage');
    const confirmButton = document.getElementById('confirmActionButton');
    
    confirmTitle.textContent = title || '确认操作';
    confirmMessage.textContent = message;
    
    // 移除之前的事件监听器
    const newConfirmButton = confirmButton.cloneNode(true);
    confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);
    
    // 添加新的事件监听器
    newConfirmButton.addEventListener('click', function() {
        // 在隐藏模态框之前移除焦点
        document.activeElement.blur();
        modal.hide();
        if (typeof callback === 'function') {
            setTimeout(() => callback(true), 100);
        }
    });
    
    // 处理模态框关闭事件
    const handleModalHidden = function(event) {
        // 如果不是通过确认按钮关闭的，则视为取消
        if (!newConfirmButton.classList.contains('clicked')) {
            if (typeof callback === 'function') {
                setTimeout(() => callback(false), 100);
            }
        }
        // 移除事件监听器
        modalElement.removeEventListener('hidden.bs.modal', handleModalHidden);
    };
    
    // 当模态框关闭时的处理
    modalElement.addEventListener('hidden.bs.modal', handleModalHidden);
    
    modal.show();
}

// 显示自定义密码输入对话框
function showPasswordPrompt(message, title, callback) {
    // 清理之前可能存在的模态框
    cleanupModals();
    
    const modalElement = document.getElementById('passwordPromptModal');
    if (!modalElement) {
        showToast('error', '找不到密码输入模态框');
        return;
    }

    
    const modal = new bootstrap.Modal(modalElement);
    
    const promptTitle = document.getElementById('passwordPromptTitle');
    const promptMessage = document.getElementById('passwordPromptMessage');
    const passwordField = document.getElementById('serverPassword');
    const confirmButton = document.getElementById('passwordPromptBtn');
    
    promptTitle.textContent = title || '输入密码';
    promptMessage.textContent = message;
    passwordField.value = '';
    
    // 移除之前的事件监听器
    const newConfirmButton = confirmButton.cloneNode(true);
    confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);
    
    // 添加新的事件监听器
    newConfirmButton.addEventListener('click', function() {
        const password = passwordField.value;
        // 在隐藏模态框之前移除焦点
        document.activeElement.blur();
        modal.hide();
        if (typeof callback === 'function') {
            setTimeout(() => callback(password), 100);
        }
    });
    
    // 处理模态框关闭事件
    const handleModalHidden = function(event) {
        // 如果不是通过确认按钮关闭的，则视为取消
        if (!newConfirmButton.classList.contains('clicked')) {
            if (typeof callback === 'function') {
                setTimeout(() => callback(null), 100);
            }
        }
        // 移除事件监听器
        modalElement.removeEventListener('hidden.bs.modal', handleModalHidden);
        // 清理模态框
        cleanupModals();
    };
    
    // 当模态框关闭时的处理
    modalElement.addEventListener('hidden.bs.modal', handleModalHidden);
    
    // 支持按回车键确认
    passwordField.addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            newConfirmButton.click();
        }
    });
    
    modal.show();
    // 自动聚焦到密码输入框
    setTimeout(() => passwordField.focus(), 300);
}

// 显示自定义删除确认对话框
function showDeleteConfirm(message, title, callback) {
    const modalElement = document.getElementById('deleteConfirmModal');
    
    // 确保模态框元素存在
    if (!modalElement) {
        console.error('找不到删除确认模态框元素');
        alert('系统错误：找不到删除确认模态框');
        return;
    }
    
    // 使用 jQuery 初始化模态框，避免直接使用 Bootstrap Modal 构造函数
    const modal = new bootstrap.Modal(modalElement);
    const confirmTitle = document.getElementById('deleteConfirmTitle');
    const confirmMessage = document.getElementById('deleteConfirmMessage');
    const confirmButton = document.getElementById('deleteConfirmBtn');
    
    if (!confirmTitle || !confirmMessage || !confirmButton) {
        console.error('找不到删除确认模态框的必要子元素');
        return;
    }
    
    confirmTitle.textContent = title || '删除确认';
    confirmMessage.textContent = message;
    
    // 移除之前的事件监听器
    const newConfirmButton = confirmButton.cloneNode(true);
    confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);
    
    // 添加新的事件监听器
    newConfirmButton.addEventListener('click', function() {
        // 在隐藏模态框之前移除焦点
        document.activeElement.blur();
        modal.hide();
        if (typeof callback === 'function') {
            setTimeout(() => callback(true), 100);
        }
    });
    
    // 处理模态框关闭事件
    const handleModalHidden = function(event) {
        // 如果不是通过确认按钮关闭的，则视为取消
        if (!newConfirmButton.classList.contains('clicked')) {
            if (typeof callback === 'function') {
                setTimeout(() => callback(false), 100);
            }
        }
        // 移除事件监听器
        modalElement.removeEventListener('hidden.bs.modal', handleModalHidden);
    };
    
    // 当模态框关闭时的处理
    modalElement.addEventListener('hidden.bs.modal', handleModalHidden);
    
    // 显示模态框
    try {
        modal.show();
    } catch (error) {
        console.error('显示删除确认模态框失败：', error);
        // 降级处理：使用原生确认对话框
        if (confirm(message)) {
            if (typeof callback === 'function') {
                callback(true);
            }
        } else {
            if (typeof callback === 'function') {
                callback(false);
            }
        }
    }
}

// 查看任务的目标服务器
function viewTargetServers(taskId) {
    // 清理可能存在的模态框
    cleanupModals();
    
    // 显示加载中
    showLoading('正在获取目标服务器信息...');
    
    // 获取目标服务器信息
    fetch(`/ansible/api/tasks/${taskId}/target_servers`)
        .then(response => response.json())
        .then(data => {
            // 隐藏加载中
            hideLoading();
            
            // 构建目标服务器信息HTML
            let contentHtml = '';
            
            if (data.length === 0) {
                contentHtml = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>没有找到目标服务器信息
                    </div>
                `;
            } else {
                contentHtml = `<div class="server-list">`;
                
                // 处理每个目标（可能是单台服务器或服务器组）
                data.forEach(target => {
                    if (target.type === 'server') {
                        // 单台服务器
                        contentHtml += `
                            <div class="server-item mb-3">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <i class="bi bi-hdd me-2"></i>单台服务器
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title">${target.name}</h5>
                                        <p class="card-text">
                                            <i class="bi bi-globe me-2"></i>IP地址: ${target.ip}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        `;
                    } else if (target.type === 'group') {
                        // 服务器组
                        contentHtml += `
                            <div class="server-group mb-3">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <i class="bi bi-folder me-2"></i>服务器组: ${target.name}
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title">包含 ${target.servers.length} 台服务器</h5>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>主机名</th>
                                                        <th>IP地址</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${target.servers.map(server => `
                                                        <tr>
                                                            <td>${server.name}</td>
                                                            <td>${server.ip}</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                });
                
                contentHtml += `</div>`;
            }
            
            // 显示模态框
            showContentModal('目标服务器信息', contentHtml);
        })
        .catch(error => {
            // 隐藏加载中
            hideLoading();
            console.error('Error:', error);
            showToast('error', '获取目标服务器信息失败');
        });
}

// 查看执行内容
function viewExecutionContent(taskType, taskId) {
    showLoading();
    
    // 确保先清理可能存在的模态框
    cleanupModals();
    
    fetch(`/ansible/api/tasks/${taskId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取任务详情失败');
            }
            return response.json();
        })
        .then(task => {
            hideLoading();
            
            let title = '';
            let content = '';
            
            if (taskType === 'playbook') {
                // 处理Playbook路径显示为相对路径
                let displayPath = task.playbook_path || '';
                if (displayPath.includes('playbooks')) {
                    displayPath = displayPath.substring(displayPath.indexOf('playbooks'));
                }
                
                title = `Playbook: ${task.task_name}`;
                
                // 处理Playbook路径
                if (task.playbook_path && task.playbook_path !== '--' && !task.playbook_path.startsWith('---')) {
                    // 显示加载中的内容
                    showContentModal(title, `
                        <div class="mb-3">
                            <strong>Playbook路径:</strong> ${displayPath}
                        </div>
                        <div class="mb-3">
                            <strong>内容:</strong>
                            <div class="text-center p-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载Playbook内容...</p>
                            </div>
                        </div>
                    `);
                    
                    // 读取Playbook内容
                    fetch(`/ansible/api/playbooks/content?path=${encodeURIComponent(task.playbook_path)}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('无法读取Playbook内容');
                            }
                            return response.text();
                        })
                        .then(playbookContent => {
                            // 更新模态框内容
                            const contentModalBody = document.getElementById('contentModalBody');
                            if (contentModalBody) {
                                contentModalBody.innerHTML = `
                                    <div class="mb-3">
                                        <strong>Playbook路径:</strong> ${displayPath}
                                    </div>
                                    <div class="mb-3">
                                        <strong>内容:</strong>
                                        <pre class="p-3 code-block bg-dark text-light rounded">${playbookContent || '无内容'}</pre>
                                    </div>
                                `;
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // 更新模态框内容，显示错误信息
                            const contentModalBody = document.getElementById('contentModalBody');
                            if (contentModalBody) {
                                contentModalBody.innerHTML = `
                                    <div class="mb-3">
                                        <strong>Playbook路径:</strong> ${displayPath}
                                    </div>
                                    <div class="alert alert-danger">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        读取Playbook内容失败: ${error.message}
                                    </div>
                                `;
                            }
                        });
                } else {
                    showContentModal(title, `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            未指定Playbook路径或路径无效
                        </div>
                    `);
                }
            } else {
                // 显示Ad-hoc命令内容
                title = `命令: ${task.task_name}`;
                content = `
                    <div class="mb-3">
                        <strong>命令内容:</strong>
                        <pre class="p-3 code-block bg-dark text-light rounded">${task.command || '无命令内容'}</pre>
                    </div>
                `;
                showContentModal(title, content);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            hideLoading();
            showToast('error', error.message);
        });
}

// 显示内容模态框
function showContentModal(title, content) {
    // 检查是否已存在内容模态框
    let contentModal = document.getElementById('contentModal');
    
    // 先移除可能存在的旧模态框和背景遮罩
    if (contentModal) {
        const oldModal = bootstrap.Modal.getInstance(contentModal);
        if (oldModal) {
            oldModal.dispose();
        }
        contentModal.remove();
        
        // 移除所有可能存在的模态框背景
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        
        // 移除body上的modal-open类
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }
    
    // 创建模态框
    const modalHtml = `
        <div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="contentModalLabel"></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="contentModalBody">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    contentModal = document.getElementById('contentModal');
    
    // 更新模态框内容
    document.getElementById('contentModalLabel').textContent = title;
    document.getElementById('contentModalBody').innerHTML = content;
    
    // 添加模态框关闭事件处理
    contentModal.addEventListener('hidden.bs.modal', function() {
        // 当模态框隐藏时，确保完全清理
        const modal = bootstrap.Modal.getInstance(contentModal);
        if (modal) {
            modal.dispose();
        }
        contentModal.remove();
        
        // 移除所有模态框背景
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        
        // 移除body上的modal-open类
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    });
    
    // 显示模态框
    const modal = new bootstrap.Modal(contentModal);
    modal.show();
}

// 清理所有模态框和背景
function cleanupModals() {
    // 移除可能存在的模态框
    const contentModal = document.getElementById('contentModal');
    
    // 先移除可能存在的旧模态框和背景遮罩
    if (contentModal) {
        const oldModal = bootstrap.Modal.getInstance(contentModal);
        if (oldModal) {
            oldModal.dispose();
        }
        contentModal.remove();
        
        // 移除所有可能存在的模态框背景
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        
        // 移除body上的modal-open类
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }
}

// 添加服务器
function addServer() {
    // 获取表单数据
    const hostname = document.getElementById('hostname').value;
    const ipAddress = document.getElementById('ip_address').value;
    const sshPort = document.getElementById('ssh_port').value;
    const groupId = document.getElementById('group_id').value;
    const description = document.getElementById('description').value;
    
    // 验证表单
    if (!hostname || !ipAddress || !sshPort) {
        showToast('error', '请填写所有必填字段');
        return;
    }
    
    // 构建请求数据
    const serverData = {
        hostname,
        ip_address: ipAddress,
        ssh_port: sshPort,
        description: description
    };
    
    // 如果选择了分组，则添加分组ID
    if (groupId) {
        serverData.group_id = groupId;
    }
    
    // 发送请求
    fetch('/ansible/api/servers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(serverData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => {
                throw new Error(err.message || '添加服务器失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', '服务器添加成功');
        loadServers();
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', error.message);
    });
}

// 查看任务执行历史
function viewTaskExecutionHistory(taskId) {
    showLoading();
    
    // 确保先清理可能存在的模态框
    cleanupModals();
    
    fetch(`/ansible/api/tasks/${taskId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取任务详情失败');
            }
            return response.json();
        })
        .then(task => {
            // 获取执行历史
            return fetch(`/ansible/api/tasks/${taskId}/executions`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取执行历史失败');
                    }
                    return response.json().then(executions => ({ task, executions }));
                });
        })
        .then(data => {
            hideLoading();
            
            const { task, executions } = data;
            
            // 检查是否有执行记录
            if (executions.length === 0) {
                showContentModal(`执行历史: ${task.task_name}`, `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        该任务暂无执行记录
                    </div>
                `);
                return;
            }
            
            // 构建执行历史列表
            let historyContent = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>执行ID</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${executions.map(execution => `
                                <tr>
                                    <td>${execution.id}</td>
                                    <td>${execution.start_time || '--'}</td>
                                    <td>${execution.end_time || '--'}</td>
                                    <td>${getStatusBadge(execution.status)}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewExecutionDetails(${execution.id})">
                                            <i class="bi bi-eye me-1"></i>查看结果
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            showContentModal(`执行历史: ${task.task_name}`, historyContent);
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('error', error.message);
        });
}

// 查看执行详情
function viewExecutionDetails(executionId) {
    showLoading();
    
    // 确保先清理可能存在的模态框
    cleanupModals();
    
    fetch(`/ansible/api/tasks/executions/${executionId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取执行详情失败');
            }
            return response.json();
        })
        .then(execution => {
            hideLoading();
            
            // 构建执行详情内容
            const detailsContent = `
                <div class="mb-3">
                    <strong>任务名称:</strong> ${execution.task_name}
                </div>
                <div class="mb-3">
                    <strong>执行状态:</strong> ${getStatusBadge(execution.status)}
                </div>
                <div class="mb-3">
                    <strong>开始时间:</strong> ${execution.start_time || '--'}
                </div>
                <div class="mb-3">
                    <strong>结束时间:</strong> ${execution.end_time || '--'}
                </div>
                <div class="mb-3">
                    <strong>执行结果:</strong>
                    <div class="mt-2">
                        <pre class="p-3 code-block bg-dark text-light rounded">${execution.formatted_result || '<div class="alert alert-info">无执行结果</div>'}</pre>
                    </div>
                </div>
            `;
            
            showContentModal(`执行详情: #${executionId}`, detailsContent);
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('error', error.message);
        });
}

// 创建新的Playbook
function createPlaybook() {
    // 显示创建Playbook的模态框
    const modalElement = document.getElementById('createPlaybookModal');
    if (!modalElement) {
        showToast('error', '找不到创建Playbook的模态框');
        return;
    }

    
    const modal = new bootstrap.Modal(modalElement);
    
    // 清空表单
    document.getElementById('create-playbook-form').reset();
    
    // 获取默认Playbook模板
    const defaultTemplate = document.getElementById('default-playbook-template');
    if (defaultTemplate) {
        document.getElementById('playbookContent').value = defaultTemplate.textContent.trim();
    }
    
    // 显示模态框
    modal.show();
    
    // 处理表单提交
    document.getElementById('create-playbook-form').onsubmit = function(e) {
        e.preventDefault();
        
        const name = document.getElementById('playbookName').value;
        const content = document.getElementById('playbookContent').value;
        const description = document.getElementById('playbookDescription').value;
        
        if (!name) {
            showToast('error', 'Playbook名称不能为空');
            return;
        }
        
        if (!content) {
            showToast('error', 'Playbook内容不能为空');
            return;
        }
        
        showLoading('正在保存Playbook...');
        
        fetch('/ansible/api/playbooks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name,
                content,
                description
            })
        })
        .then(response => {
            hideLoading();
            if (!response.ok) {
                return response.json().then(err => {
                    throw new Error(err.message || '保存Playbook失败');
                });
            }
            return response.json();
        })
        .then(data => {
            showToast('success', 'Playbook保存成功');
            modal.hide();
            loadPlaybooks();
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('error', error.message);
        });
    };
}

// 创建Playbook模态框中的保存按钮点击事件
function savePlaybook() {
    const nameInput = document.getElementById('playbookName');
    const contentInput = document.getElementById('playbookContent');
    const descriptionInput = document.getElementById('playbookDescription');
    
    if (!nameInput || !contentInput) {
        showToast('error', '找不到必要的表单元素');
        return;
    }
    
    const name = nameInput.value.trim();
    const content = contentInput.value.trim();
    const description = descriptionInput ? descriptionInput.value.trim() : '';
    
    // 详细的字段验证
    if (!name) {
        showToast('error', '请填写Playbook名称');
        return;
    }
    
    if (!content) {
        showToast('error', '请填写Playbook内容');
        return;
    }
    
    showLoading('正在保存Playbook...');
    
    // 构建请求数据
    const playbookData = {
        name: name,
        content: content,
        description: description
    };
    
    console.log('发送的数据:', playbookData); // 调试用
    
    // 发送请求
    fetch('/ansible/api/playbooks', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(playbookData)
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(err => {
                throw new Error(err.message || '保存Playbook失败');
            });
        }
        return response.json();
    })
    .then(data => {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('createPlaybookModal'));
        if (modal) {
            modal.hide();
        }
        
        // 显示成功消息
        showToast('success', 'Playbook保存成功');
        
        // 重新加载Playbook列表
        loadPlaybooks();
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showToast('error', error.message);
    });
}

// 编辑Playbook模态框中的保存按钮点击事件
function updatePlaybook() {
    console.log('开始更新Playbook...');
    
    const idInput = document.getElementById('edit-playbook-id');
    const nameInput = document.getElementById('edit-playbook-name');
    const descriptionInput = document.getElementById('edit-playbook-description');
    const contentInput = document.getElementById('edit-playbook-content');
    
    if (!idInput || !nameInput || !contentInput) {
        showToast('error', '找不到必要的表单元素');
        return;
    }
    
    const playbookId = idInput.value;
    const name = nameInput.value.trim();
    const description = descriptionInput ? descriptionInput.value.trim() : '';
    const content = contentInput.value.trim();
    
    if (!name) {
        showToast('error', 'Playbook名称不能为空');
        return;
    }
    
    if (!content) {
        showToast('error', 'Playbook内容不能为空');
        return;
    }
    
    showLoading('正在更新Playbook...');
    
    fetch(`/ansible/api/playbooks/${playbookId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: name,
            content: content,
            description: description
        })
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(err => {
                throw new Error(err.message || '更新Playbook失败');
            });
        }
        return response.json();
    })
    .then(data => {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('edit-playbook-modal'));
        if (modal) {
            modal.hide();
        }
        
        // 显示成功消息
        showToast('success', 'Playbook更新成功');
        
        // 重新加载Playbook列表
        loadPlaybooks();
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showToast('error', error.message);
    });
}

// 显示编辑分组表单
function showEditGroupForm(groupId, groupName, groupDescription) {
    const groupList = document.getElementById('server-groups');
    groupList.innerHTML = `
        <div class="card">
            <div class="card-header">
                <i class="bi bi-pencil-square me-2"></i>编辑服务器分组
            </div>
            <div class="card-body">
                <form id="edit-group-form" class="mb-3">
                    <input type="hidden" id="edit-group-id" value="${groupId}">
                    <div class="mb-3">
                        <label class="form-label">分组名称</label>
                        <input type="text" class="form-control" id="edit-group-name" required value="${groupName}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <input type="text" class="form-control" id="edit-group-description" value="${groupDescription}" placeholder="输入分组描述（可选）">
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="loadGroups()">
                            <i class="bi bi-x-circle me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="updateGroup()">
                            <i class="bi bi-save me-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
}

// 更新服务器分组
function updateGroup() {
    const groupId = document.getElementById('edit-group-id').value;
    const name = document.getElementById('edit-group-name').value;
    const description = document.getElementById('edit-group-description').value;

    if (!name) {
        showToast('error', '分组名称不能为空');
        return;
    }

    showLoading('正在更新分组...');

    fetch(`/ansible/api/groups/${groupId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name,
            description
        })
    })
    .then(response => {
        hideLoading();
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || '更新分组失败');
            });
        }
        return response.json();
    })
    .then(data => {
        showToast('success', data.message);
        loadGroups();
        updateDashboardStats();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '更新分组失败：' + error.message);
    });
}

// 显示编辑任务表单
function showEditTaskForm(taskId) {
    showLoading('正在加载任务信息...');
    
    // 清空之前的表单数据
    document.getElementById('edit-task-id').value = '';
    document.getElementById('edit-task-name').value = '';
    
    // 先加载服务器、服务器组和Playbook信息
    Promise.all([
        fetch('/ansible/api/servers').then(res => res.json()),
        fetch('/ansible/api/playbooks').then(res => res.json()),
        fetch(`/ansible/api/tasks/${taskId}`).then(res => res.json()),
        fetch('/ansible/api/server-groups').then(res => res.json())
    ])
    .then(([servers, playbooks, task, groups]) => {
        hideLoading();
        
        // 填充服务器选项
        const targetSelect = document.getElementById('edit-task-target');
        targetSelect.innerHTML = '<option value="">请选择目标服务器</option>';
        servers.forEach(server => {
            const option = document.createElement('option');
            option.value = server.id;
            option.textContent = `${server.hostname} (${server.ip_address})`;
            targetSelect.appendChild(option);
        });
        
        // 填充服务器组选项
        const groupSelect = document.getElementById('edit-task-group');
        groupSelect.innerHTML = '<option value="">请选择服务器组</option>';
        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = group.name;
            groupSelect.appendChild(option);
        });
        
        // 填充Playbook选项
        const playbookSelect = document.getElementById('edit-task-playbook');
        playbookSelect.innerHTML = '<option value="">请选择Playbook</option>';
        playbooks.forEach(playbook => {
            const option = document.createElement('option');
            option.value = playbook.id;
            option.textContent = playbook.name;
            playbookSelect.appendChild(option);
        });
        
        // 填充任务信息
        document.getElementById('edit-task-id').value = task.id;
        document.getElementById('edit-task-name').value = task.task_name;
        
        const taskTypeSelect = document.getElementById('edit-task-type');
        taskTypeSelect.value = task.task_type;
        
        // 根据任务类型显示/隐藏相应选项
        const adhocOptions = document.getElementById('edit-adhoc-options');
        const playbookOptions = document.getElementById('edit-playbook-options');
        
        if (task.task_type === 'adhoc') {
            adhocOptions.style.display = 'block';
            playbookOptions.style.display = 'none';
            document.getElementById('edit-task-command').value = task.command || '';
        } else {
            adhocOptions.style.display = 'none';
            playbookOptions.style.display = 'block';
            
            // 直接使用 task.playbook_id 来设置选中的Playbook
            if (task.playbook_id) {
                playbookSelect.value = task.playbook_id;
            }
        }
        
        // 设置目标类型，并根据后端返回的 task.target_type 设置初始值
        const targetTypeSelect = document.getElementById('edit-target-type');
        if (task.target_type) {
            targetTypeSelect.value = task.target_type;
        } else {
            // 如果后端没有 target_type，则根据 target_servers 推断 (兼容旧数据)
            let inferredTargetType = 'server'; 
            if (task.target_servers) {
                const targetServerIds = task.target_servers.split(',');
                if (targetServerIds.length === 1) {
                    const singleTargetId = targetServerIds[0];
                    const isGroup = groups.some(group => group.id.toString() === singleTargetId || String(group.id) === singleTargetId);
                    if (isGroup) {
                        inferredTargetType = 'group';
                    }
                }
            }
            targetTypeSelect.value = inferredTargetType;
        }

        // 根据目标类型显示/隐藏相应选择器
        const serverContainer = document.getElementById('edit-server-target-container');
        const groupContainer = document.getElementById('edit-group-target-container');
        
        if (targetTypeSelect.value === 'server') {
            serverContainer.classList.remove('d-none');
            groupContainer.classList.add('d-none');
            // 设置单台服务器选择器的值
            if (task.target_servers && task.target_type === 'server') {
                 const serverIds = task.target_servers.split(',');
                 if (serverIds.length === 1) document.getElementById('edit-task-target').value = serverIds[0];
            }
        } else if (targetTypeSelect.value === 'group') {
            serverContainer.classList.add('d-none');
            groupContainer.classList.remove('d-none');
            // 设置服务器组选择器的值
            if (task.target_servers && task.target_type === 'group') {
                const groupIds = task.target_servers.split(',');
                if (groupIds.length === 1) document.getElementById('edit-task-group').value = groupIds[0];
            }
        }
        
        // 添加目标类型切换事件
        targetTypeSelect.addEventListener('change', function() {
            if (this.value === 'server') {
                serverContainer.classList.remove('d-none');
                groupContainer.classList.add('d-none');
            } else if (this.value === 'group') {
                serverContainer.classList.add('d-none');
                groupContainer.classList.remove('d-none');
            }
        });
        
        // 显示编辑模态框
        const modal = new bootstrap.Modal(document.getElementById('edit-task-modal'));
        modal.show();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '加载任务信息失败：' + error.message);
    });
}

// 更新任务
function updateTask() {
    const taskId = document.getElementById('edit-task-id').value;
    const taskName = document.getElementById('edit-task-name').value;
    const taskType = document.getElementById('edit-task-type').value;
    const targetType = document.getElementById('edit-target-type').value;
    
    // 根据目标类型获取选择的服务器或服务器组
    let targetServers;
    if (targetType === 'server') {
        targetServers = document.getElementById('edit-task-target').value;
    } else if (targetType === 'group') {
        targetServers = document.getElementById('edit-task-group').value;
    }
    
    if (!taskName || !taskType || !targetType || !targetServers) {
        showToast('error', '请填写所有必填项');
        return;
    }
    
    let taskData = {
        task_name: taskName,
        task_type: taskType,
        target_type: targetType, // 添加 target_type 到发送的数据中
        target_servers: targetServers
    };
    
    if (taskType === 'adhoc') {
        taskData.command = document.getElementById('edit-task-command').value;
        if (!taskData.command) {
            showToast('error', '请填写命令内容');
            return;
        }
    } else {
        const playbookId = document.getElementById('edit-task-playbook').value;
        if (!playbookId) {
            showToast('error', '请选择Playbook');
            return;
        }
        taskData.playbook_id = playbookId;
    }
    
    showLoading('正在更新任务...');
    
    fetch(`/ansible/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || '更新任务失败');
            });
        }
        return response.json();
    })
    .then(data => {
        hideLoading();
        showToast('success', '任务更新成功');
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('edit-task-modal'));
        modal.hide();
        
        // 重新加载任务列表
        loadTasks();
    })
    .catch(error => {
        hideLoading();
        showToast('error', '更新任务失败：' + error.message);
    });
}

// 测试上传面板功能
function testUploadPanel() {
    console.log('测试上传面板功能');

    if (typeof asyncUploadManager !== 'undefined') {
        console.log('异步上传管理器存在');

        // 显示面板
        asyncUploadManager.showPanel();

        // 创建一个测试任务
        const testTask = {
            id: 'test_' + Date.now(),
            filename: 'test_file.txt',
            status: 'uploading',
            progress: 45,
            speed: 1024 * 1024 * 2, // 2MB/s
            fileSize: 1024 * 1024 * 100, // 100MB
            startTime: Date.now()
        };

        asyncUploadManager.uploadTasks.set(testTask.id, testTask);
        asyncUploadManager.renderTask(testTask);

        console.log('已创建测试任务:', testTask);

        // 显示提示
        if (typeof showToast === 'function') {
            showToast('info', '测试任务已创建，请查看传输管理器');
        } else {
            alert('测试任务已创建，请查看传输管理器');
        }
    } else {
        console.error('异步上传管理器不存在');
        if (typeof showToast === 'function') {
            showToast('error', '异步上传管理器未初始化');
        } else {
            alert('异步上传管理器未初始化');
        }
    }

    // 测试分片上传管理器
    if (typeof chunkedUploadManager !== 'undefined') {
        console.log('分片上传管理器存在');
        chunkedUploadManager.showPanel();

        // 创建一个测试任务
        const testChunkedTask = {
            id: 'chunked_test_' + Date.now(),
            filename: 'large_test_file.zip',
            status: 'uploading',
            progress: 65,
            speed: 1024 * 1024 * 5, // 5MB/s
            fileSize: 1024 * 1024 * 1024, // 1GB
            chunkSize: 1024 * 1024 * 10, // 10MB
            totalChunks: 100,
            uploadedChunks: 65,
            startTime: Date.now()
        };

        chunkedUploadManager.uploadTasks.set(testChunkedTask.id, testChunkedTask);
        chunkedUploadManager.renderTask(testChunkedTask);

        console.log('已创建分片测试任务:', testChunkedTask);
    } else {
        console.log('分片上传管理器不存在');
    }
}