<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 上传功能修复测试</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📤 测试上传功能</h5>
                    </div>
                    <div class="card-body">
                        <form id="testUploadForm">
                            <div class="mb-3">
                                <label for="testFile" class="form-label">选择文件</label>
                                <input type="file" class="form-control" id="testFile" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="testPath" class="form-label">上传路径</label>
                                <input type="text" class="form-control" id="testPath" value="/data/image_bak" placeholder="/data/image_bak">
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="testOverwrite">
                                <label class="form-check-label" for="testOverwrite">
                                    覆盖已有文件
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-cloud-upload"></i> 开始上传
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">等待测试...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>🔍 修复内容检查</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>✅ 已修复的问题</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        Flask上下文错误
                                    </li>
                                    <li class="list-group-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        立即显示传输管理器
                                    </li>
                                    <li class="list-group-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        覆盖文件选项处理
                                    </li>
                                    <li class="list-group-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        统一传输管理器
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-4">
                                <h6>🎯 预期效果</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        点击上传立即显示传输管理器
                                    </li>
                                    <li class="list-group-item">
                                        实时显示上传进度
                                    </li>
                                    <li class="list-group-item">
                                        支持暂停/取消操作
                                    </li>
                                    <li class="list-group-item">
                                        正确处理文件覆盖
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-4">
                                <h6>🧪 测试步骤</h6>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">选择一个测试文件</li>
                                    <li class="list-group-item">设置上传路径</li>
                                    <li class="list-group-item">勾选覆盖选项（可选）</li>
                                    <li class="list-group-item">点击上传按钮</li>
                                    <li class="list-group-item">观察传输管理器是否立即显示</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟统一传输管理器
        class MockFileTransferManager {
            constructor() {
                this.transfers = [];
                this.nextId = 1;
                this.isVisible = false;
            }
            
            addUpload(filename, fileSize, path, overwrite) {
                const id = this.nextId++;
                const transfer = {
                    id,
                    type: 'upload',
                    file: filename,
                    fileSize: fileSize,
                    destinationPath: path,
                    overwrite: overwrite,
                    status: 'pending',
                    progress: 0,
                    speed: 0,
                    startTime: Date.now()
                };
                
                this.transfers.push(transfer);
                this.updateTestResults(`✅ 已添加上传任务: ${filename} (${this.formatFileSize(fileSize)})`);
                this.updateTestResults(`📁 目标路径: ${path}`);
                this.updateTestResults(`🔄 覆盖模式: ${overwrite ? '是' : '否'}`);
                
                return id;
            }
            
            showTransferManager() {
                this.isVisible = true;
                this.updateTestResults('📱 传输管理器已显示');
            }
            
            updateTransferUI(transfer) {
                this.updateTestResults(`🔄 更新任务状态: ${transfer.status} (${transfer.progress}%)`);
            }
            
            startUpload(transfer, file) {
                this.updateTestResults(`🚀 开始上传: ${transfer.file}`);
                
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        transfer.status = 'completed';
                        this.updateTestResults(`✅ 上传完成: ${transfer.file}`);
                    } else {
                        transfer.status = 'running';
                    }
                    
                    transfer.progress = Math.round(progress);
                    transfer.speed = Math.random() * 1024 * 1024 * 5; // 0-5MB/s
                    
                    this.updateTestResults(`📊 进度更新: ${transfer.progress}% (${this.formatSpeed(transfer.speed)})`);
                }, 1000);
            }
            
            formatFileSize(bytes) {
                if (!bytes) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            formatSpeed(bytesPerSecond) {
                return this.formatFileSize(bytesPerSecond) + '/s';
            }
            
            updateTestResults(message) {
                const results = document.getElementById('testResults');
                const time = new Date().toLocaleTimeString();
                results.innerHTML += `<div class="small text-muted">[${time}] ${message}</div>`;
                results.scrollTop = results.scrollHeight;
            }
        }
        
        // 初始化模拟管理器
        window.fileTransferManager = new MockFileTransferManager();
        
        // 模拟showToast函数
        window.showToast = function(type, message) {
            fileTransferManager.updateTestResults(`💬 ${type.toUpperCase()}: ${message}`);
        };
        
        // 测试表单提交
        document.getElementById('testUploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('testFile');
            const pathInput = document.getElementById('testPath');
            const overwriteCheckbox = document.getElementById('testOverwrite');
            
            if (!fileInput.files || fileInput.files.length === 0) {
                showToast('error', '请选择文件');
                return;
            }
            
            const file = fileInput.files[0];
            const path = pathInput.value || '/data/image_bak';
            const overwrite = overwriteCheckbox.checked;
            
            // 清空之前的结果
            document.getElementById('testResults').innerHTML = '<h6>🧪 测试开始</h6>';
            
            // 模拟上传流程
            fileTransferManager.updateTestResults('🎯 开始测试上传功能...');
            
            // 立即显示传输管理器
            fileTransferManager.showTransferManager();
            
            // 添加上传任务
            const transferId = fileTransferManager.addUpload(file.name, file.size, path, overwrite);
            const transfer = fileTransferManager.transfers.find(t => t.id === transferId);
            
            // 立即更新状态
            transfer.status = 'pending';
            transfer.progress = 0;
            fileTransferManager.updateTransferUI(transfer);
            
            // 显示提示
            showToast('info', '文件上传已开始，请查看传输管理器');
            
            // 开始上传
            fileTransferManager.startUpload(transfer, file);
        });
        
        // 页面加载完成后的检查
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <h6>🔧 系统检查</h6>
                <div class="small text-success">✅ 统一传输管理器已加载</div>
                <div class="small text-success">✅ showToast函数已模拟</div>
                <div class="small text-success">✅ 测试环境准备完成</div>
                <hr>
                <p class="text-info">请选择文件并点击上传按钮开始测试</p>
            `;
        });
    </script>
</body>
</html>
