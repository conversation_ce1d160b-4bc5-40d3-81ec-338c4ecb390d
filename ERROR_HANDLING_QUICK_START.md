# 统一错误处理快速开始指南

## 🚀 快速集成

### 1. 自动集成（推荐）
```bash
# 运行自动集成脚本
python src/utils/integrate_error_handling.py
```

### 2. 手动集成
如果需要手动集成，请按以下步骤操作：

#### 步骤1: 更新主应用文件
在 `app.py` 中添加：
```python
# 导入错误处理模块
from src.utils.error_handler import register_error_handlers

# 注册错误处理器
register_error_handlers(app)
```

#### 步骤2: 更新路由文件
在路由文件中添加：
```python
from src.utils.error_handler import (
    handle_exceptions, DatabaseError, ValidationError,
    create_error_response, create_success_response,
    ErrorCategory, ErrorLevel
)

# 使用装饰器
@handle_exceptions(category=ErrorCategory.DATABASE)
def your_route():
    # 你的代码
    pass
```

#### 步骤3: 更新配置文件
在 `.env` 文件中添加：
```bash
# 错误处理配置
LOG_LEVEL=INFO
ERROR_MONITOR_ENABLED=true
ERROR_RETENTION_DAYS=7
DEFAULT_RETRY_ATTEMPTS=3
SHOW_DETAILED_ERRORS=false
```

## 📝 使用示例

### 基础用法
```python
from src.utils.error_handler import handle_exceptions, ErrorCategory

@handle_exceptions(category=ErrorCategory.DATABASE)
def get_users():
    """获取用户列表"""
    users = db.session.query(User).all()
    return create_success_response(users)
```

### 自定义异常
```python
from src.utils.error_handler import DatabaseError, ValidationError

def create_user(data):
    """创建用户"""
    # 数据验证
    if not data.get('email'):
        raise ValidationError("邮箱不能为空")
    
    # 数据库操作
    try:
        user = User(**data)
        db.session.add(user)
        db.session.commit()
        return create_success_response(user.to_dict(), "用户创建成功")
    except Exception as e:
        raise DatabaseError("用户创建失败", details={'error': str(e)})
```

### 重试机制
```python
from src.utils.error_handler import retry_on_error

@retry_on_error(max_attempts=3, delay=1.0)
def external_api_call():
    """调用外部API"""
    response = requests.get("https://api.example.com/data")
    return response.json()
```

## 📊 错误监控

### 查看错误统计
```python
from src.utils.error_monitor import error_monitor

# 获取24小时内的错误统计
stats = error_monitor.get_stats(hours=24)
print(f"总错误数: {stats['total_errors']}")
print(f"按分类统计: {stats['errors_by_category']}")
```

### 生成错误报告
```python
# 生成错误报告
report = error_monitor.generate_report(hours=24)
print(report)
```

## 🔧 配置选项

### 基础配置
```bash
# 日志级别
LOG_LEVEL=INFO

# 错误监控
ERROR_MONITOR_ENABLED=true
ERROR_RETENTION_DAYS=7

# 重试配置
DEFAULT_RETRY_ATTEMPTS=3
DEFAULT_RETRY_DELAY=1.0
```

### 高级配置
```bash
# 日志文件配置
LOG_FILE_PATH=logs/system.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# 错误存储配置
ERROR_STORAGE_PATH=logs/error_monitor.json
ERROR_BUFFER_SIZE=1000

# 错误响应配置
SHOW_DETAILED_ERRORS=false
ERROR_RESPONSE_TIMEOUT=30
```

## 🛠️ 常用模式

### 1. API路由错误处理
```python
@app.route('/api/users', methods=['POST'])
@handle_exceptions(category=ErrorCategory.BUSINESS)
def create_user():
    data = request.get_json()
    
    # 验证数据
    if not data:
        raise ValidationError("请求数据不能为空")
    
    # 业务逻辑
    user = User.create(data)
    return create_success_response(user.to_dict(), "用户创建成功")
```

### 2. 数据库操作错误处理
```python
@retry_on_error(max_attempts=3, delay=1.0)
def get_user_by_id(user_id):
    try:
        user = db.session.query(User).filter_by(id=user_id).first()
        if not user:
            raise ValidationError(f"用户 {user_id} 不存在")
        return user
    except SQLAlchemyError as e:
        raise DatabaseError("数据库查询失败", details={'user_id': user_id, 'error': str(e)})
```

### 3. 外部服务调用错误处理
```python
@retry_on_error(max_attempts=3, delay=2.0, backoff_factor=2.0)
def call_payment_service(order_data):
    try:
        response = requests.post(
            "https://payment.example.com/api/charge",
            json=order_data,
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        raise NetworkError("支付服务调用失败", details={'error': str(e)})
```

## 📈 监控和报告

### 错误统计API
```python
@app.route('/api/admin/error-stats')
def get_error_stats():
    """获取错误统计"""
    hours = request.args.get('hours', 24, type=int)
    stats = error_monitor.get_stats(hours)
    return create_success_response(stats)
```

### 错误详情API
```python
@app.route('/api/admin/error-details')
def get_error_details():
    """获取错误详情"""
    category = request.args.get('category')
    limit = request.args.get('limit', 100, type=int)
    
    errors = error_monitor.get_error_details(
        category=category,
        limit=limit
    )
    return create_success_response(errors)
```

## 🔍 故障排除

### 常见问题

1. **导入错误**
```bash
ModuleNotFoundError: No module named 'src.utils.error_handler'
```
**解决方案**: 确保在项目根目录运行，或检查Python路径设置

2. **配置未生效**
```bash
# 检查配置是否正确加载
python -c "import os; print(os.environ.get('ERROR_MONITOR_ENABLED'))"
```

3. **错误日志过多**
```bash
# 清理旧错误记录
python -c "from src.utils.error_monitor import error_monitor; error_monitor.clear_old_errors(days=3)"
```

### 调试技巧

1. **启用详细日志**
```bash
LOG_LEVEL=DEBUG
SHOW_DETAILED_ERRORS=true
```

2. **检查错误处理器注册**
```python
# 在app.py中添加调试信息
print(f"错误处理器已注册: {len(app.error_handler_spec)}")
```

3. **测试错误处理**
```python
# 创建测试路由
@app.route('/test-error')
def test_error():
    raise DatabaseError("测试错误")
```

## 📚 更多资源

- **[完整文档](UNIFIED_ERROR_HANDLING.md)** - 详细的错误处理文档
- **[使用示例](src/utils/error_examples.py)** - 更多使用示例
- **[配置说明](src/utils/error_config.py)** - 配置选项详解

## 🎯 下一步

1. **集成错误处理**: 运行集成脚本或手动集成
2. **配置监控**: 设置错误监控和报告
3. **测试功能**: 验证错误处理是否正常工作
4. **优化配置**: 根据实际需求调整配置参数
5. **监控运行**: 定期查看错误统计和报告

通过实施统一错误处理机制，您的系统将具备更好的稳定性、可维护性和用户体验！
