#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速上传重构验证脚本
测试：
1. 立即显示传输管理器
2. 默认覆盖重复文件
3. 快速异步上传
4. 实时速度显示
"""

import os
import sys
import time
import requests
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class FastUploadTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def login(self, password="your_password"):
        """登录到文件管理器"""
        print("🔐 正在登录文件管理器...")
        
        login_url = f"{self.base_url}/ansible/api/file-manager/login"
        response = self.session.post(login_url, json={'password': password})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return False
    
    def create_test_files(self):
        """创建多个测试文件"""
        test_files = []
        
        # 小文件 (1MB)
        small_file = tempfile.NamedTemporaryFile(delete=False, suffix='_small.txt')
        data = "这是小文件测试数据。\n" * (1024 * 10)  # 约1MB
        small_file.write(data.encode('utf-8'))
        small_file.close()
        test_files.append(('小文件', small_file.name, 1))
        
        # 中等文件 (10MB)
        medium_file = tempfile.NamedTemporaryFile(delete=False, suffix='_medium.txt')
        data = "这是中等文件测试数据。\n" * (1024 * 100)  # 约10MB
        medium_file.write(data.encode('utf-8'))
        medium_file.close()
        test_files.append(('中等文件', medium_file.name, 10))
        
        # 大文件 (50MB)
        large_file = tempfile.NamedTemporaryFile(delete=False, suffix='_large.txt')
        data = "这是大文件测试数据。\n" * (1024 * 500)  # 约50MB
        large_file.write(data.encode('utf-8'))
        large_file.close()
        test_files.append(('大文件', large_file.name, 50))
        
        print(f"✅ 已创建 {len(test_files)} 个测试文件")
        return test_files
    
    def test_fast_upload(self, file_path, file_type, destination="/data/image_bak"):
        """测试快速上传"""
        print(f"\n🚀 测试{file_type}快速上传: {os.path.basename(file_path)}")
        
        # 检查文件大小决定上传方式
        file_size = os.path.getsize(file_path)
        if file_size > 50 * 1024 * 1024:  # 50MB
            upload_url = f"{self.base_url}/ansible/api/file-manager/upload-chunked"
            print("📦 使用分片上传")
        else:
            upload_url = f"{self.base_url}/ansible/api/file-manager/upload-async"
            print("⚡ 使用异步上传")
        
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f, 'text/plain')}
            data = {
                'path': destination,
                'overwrite': 'true'  # 强制覆盖
            }
            
            print("📤 发送快速上传请求...")
            start_time = time.time()
            response = self.session.post(upload_url, files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 快速上传任务已创建: {task_id}")
                print(f"⏱️ 任务创建耗时: {(time.time() - start_time):.2f}秒")
                return self.monitor_fast_upload(task_id, start_time)
            else:
                print(f"❌ 快速上传失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 快速上传请求失败: {response.status_code}")
            return False
    
    def monitor_fast_upload(self, task_id, start_time, timeout=120):
        """监控快速上传进度"""
        print(f"📊 监控快速上传进度: {task_id}")
        
        status_url = f"{self.base_url}/ansible/api/file-manager/upload-status/{task_id}"
        last_progress = 0
        max_speed = 0
        
        while time.time() - start_time < timeout:
            response = self.session.get(status_url)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    status = result.get('status')
                    progress = status.get('progress', 0)
                    upload_status = status.get('status', 'unknown')
                    speed = status.get('speed', 0)
                    error_msg = status.get('error_message')
                    
                    # 记录最大速度
                    if speed > max_speed:
                        max_speed = speed
                    
                    # 只在进度有变化时显示
                    if progress != last_progress or upload_status in ['completed', 'failed']:
                        speed_str = self.format_speed(speed) if speed > 0 else "0 B/s"
                        elapsed = time.time() - start_time
                        
                        print(f"📈 进度: {progress:.1f}% | 状态: {upload_status} | 速度: {speed_str} | 耗时: {elapsed:.1f}s")
                        last_progress = progress
                    
                    if upload_status == 'completed':
                        total_time = time.time() - start_time
                        max_speed_str = self.format_speed(max_speed)
                        print(f"🎉 上传完成! 总耗时: {total_time:.2f}秒 | 最大速度: {max_speed_str}")
                        return True
                    elif upload_status == 'failed':
                        print(f"💥 上传失败: {error_msg}")
                        return False
                    elif upload_status in ['cancelled', 'paused']:
                        print(f"⏸️ 上传已{upload_status}")
                        return False
                else:
                    print(f"❌ 获取状态失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 状态请求失败: {response.status_code}")
                return False
            
            time.sleep(1)  # 每1秒检查一次，更频繁的监控
        
        print("⏰ 监控超时")
        return False
    
    def format_speed(self, bytes_per_second):
        """格式化传输速度"""
        if bytes_per_second < 1024:
            return f"{bytes_per_second:.1f} B/s"
        elif bytes_per_second < 1024 * 1024:
            return f"{bytes_per_second / 1024:.1f} KB/s"
        else:
            return f"{bytes_per_second / (1024 * 1024):.1f} MB/s"
    
    def cleanup_test_files(self, test_files):
        """清理测试文件"""
        for file_type, file_path, size_mb in test_files:
            try:
                os.unlink(file_path)
                print(f"🗑️ 已清理{file_type}: {file_path}")
            except Exception as e:
                print(f"⚠️ 清理{file_type}失败: {e}")
    
    def test_concurrent_uploads(self, test_files):
        """测试并发上传"""
        print("\n" + "=" * 50)
        print("🔥 测试并发快速上传")
        print("=" * 50)
        
        import threading
        import queue
        
        results = queue.Queue()
        threads = []
        
        def upload_worker(file_info):
            file_type, file_path, size_mb = file_info
            result = self.test_fast_upload(file_path, file_type)
            results.put((file_type, result))
        
        # 启动所有上传线程
        for file_info in test_files:
            thread = threading.Thread(target=upload_worker, args=(file_info,))
            thread.start()
            threads.append(thread)
            time.sleep(0.5)  # 稍微错开启动时间
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 收集结果
        success_count = 0
        while not results.empty():
            file_type, success = results.get()
            if success:
                success_count += 1
                print(f"✅ {file_type}并发上传成功")
            else:
                print(f"❌ {file_type}并发上传失败")
        
        print(f"\n📊 并发上传结果: {success_count}/{len(test_files)} 成功")
        return success_count == len(test_files)
    
    def run_tests(self):
        """运行所有测试"""
        print("🧪 开始快速上传重构验证测试")
        print("=" * 60)
        
        # 1. 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return False
        
        # 2. 创建测试文件
        test_files = self.create_test_files()
        
        try:
            # 3. 测试单个文件快速上传
            print("\n" + "=" * 40)
            print("测试1: 单个文件快速上传")
            print("=" * 40)
            
            single_results = []
            for file_type, file_path, size_mb in test_files:
                result = self.test_fast_upload(file_path, file_type)
                single_results.append(result)
                time.sleep(2)  # 间隔2秒
            
            # 4. 测试并发上传
            concurrent_result = self.test_concurrent_uploads(test_files)
            
            # 5. 总结结果
            print("\n" + "=" * 60)
            print("🏁 快速上传重构验证结果总结")
            print("=" * 60)
            
            success_count = sum(single_results)
            print(f"单个上传: {success_count}/{len(single_results)} 成功")
            print(f"并发上传: {'✅ 成功' if concurrent_result else '❌ 失败'}")
            
            if success_count == len(single_results) and concurrent_result:
                print("\n🎉 所有测试通过！快速上传重构成功！")
                print("✅ 立即显示传输管理器")
                print("✅ 默认覆盖重复文件")
                print("✅ 快速异步上传")
                print("✅ 实时速度显示")
                print("✅ 并发上传支持")
                return True
            else:
                print("\n💥 部分测试失败，需要进一步检查")
                return False
                
        finally:
            # 清理测试文件
            self.cleanup_test_files(test_files)

def main():
    """主函数"""
    print("🔧 快速上传重构验证工具")
    print("=" * 60)
    
    # 检查服务器是否运行
    tester = FastUploadTester()
    
    try:
        response = requests.get(f"{tester.base_url}/", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行或无法访问")
            return False
    except requests.exceptions.RequestException:
        print("❌ 无法连接到服务器，请确保Flask应用正在运行")
        return False
    
    # 运行测试
    success = tester.run_tests()
    
    if success:
        print("\n✅ 快速上传重构验证通过！")
        print("现在您可以享受：")
        print("  🚀 点击上传立即显示传输管理器")
        print("  ⚡ 默认覆盖重复文件，无需确认")
        print("  📊 实时显示上传速度和进度")
        print("  🔥 支持多文件并发上传")
    else:
        print("\n❌ 验证失败，请检查日志获取更多信息")
    
    return success

if __name__ == "__main__":
    main()
