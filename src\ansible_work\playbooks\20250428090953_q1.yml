---
- name: 部署并更新远程服务器上的 Docker 服务
  hosts: docker_server
  become: yes  # 使用 sudo 权限
  vars:
    app_version: v4.8.5  # 应用版本号，只需修改此处
    tar_file: "myapp_{{ app_version }}.tar"  # tar 包文件名，动态生成
    local_tar_path: "/data/image_bak/myapp_{{ app_version }}.tar"  # 7.3 上 tar 文件路径
    remote_tar_path: "/tmp/myapp_{{ app_version }}.tar"  # 7.49 上目标存储路径
    container_name: myapp  # Docker 容器名称
    image_name: "myapp:{{ app_version }}"  # Docker 镜像名称和标签
    port_mapping: "5000:5000"  # 端口映射

  tasks:
    - name: 检查本地 tar 文件是否存在
      ansible.builtin.stat:
        path: "{{ local_tar_path }}"
      delegate_to: localhost  # 在 7.3 上执行
      register: local_tar_stat
      failed_when: not local_tar_stat.stat.exists or local_tar_stat.stat.isdir  # 如果文件不存在或为目录，失败

    - name: 复制 tar 包到远程服务器
      ansible.builtin.copy:
        src: "{{ local_tar_path }}"
        dest: "{{ remote_tar_path }}"
        mode: '0644'

    - name: 检查远程 tar 文件是否有效
      ansible.builtin.stat:
        path: "{{ remote_tar_path }}"
      register: remote_tar_stat
      failed_when: not remote_tar_stat.stat.exists or remote_tar_stat.stat.isdir  # 如果文件不存在或为目录，失败

    - name: 检查容器是否存在
      ansible.builtin.command: docker inspect {{ container_name }}
      register: inspect_result
      failed_when: false  # 即使容器不存在，也不失败
      changed_when: false

    - name: 设置容器存在标志
      ansible.builtin.set_fact:
        container_exists: "{{ inspect_result.rc == 0 }}"

    - name: 停止现有的 Docker 容器
      ansible.builtin.command: docker stop {{ container_name }}
      when: container_exists  # 仅在容器存在时执行
      register: stop_result
      changed_when: stop_result.rc == 0
      failed_when: stop_result.rc != 0  # 如果停止失败，任务失败

    - name: 删除现有的 Docker 容器
      ansible.builtin.command: docker rm {{ container_name }}
      when: container_exists  # 仅在容器存在时执行
      register: rm_result
      changed_when: rm_result.rc == 0
      failed_when: rm_result.rc != 0  # 如果删除失败，任务失败

    - name: 从 tar 文件加载 Docker 镜像
      ansible.builtin.command: docker load -i {{ remote_tar_path }}
      register: load_result
      changed_when: load_result.rc == 0
      failed_when: load_result.rc != 0  # 如果加载失败，任务失败

    - name: 运行新的 Docker 容器
      ansible.builtin.command: docker run -d --name {{ container_name }} -p {{ port_mapping }} {{ image_name }}
      register: run_result
      changed_when: run_result.rc == 0
      failed_when: run_result.rc != 0  # 如果运行失败，任务失败

    - name: 清理远程服务器上的 tar 文件
      ansible.builtin.file:
        path: "{{ remote_tar_path }}"
        state: absent