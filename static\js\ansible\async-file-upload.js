/**
 * 异步文件上传管理器
 * 提供高性能的文件上传功能，支持进度跟踪、暂停/恢复、取消等功能
 */
class AsyncFileUploadManager {
    constructor() {
        this.uploadTasks = new Map();
        this.maxConcurrentUploads = 3;
        this.currentUploads = 0;
        this.uploadQueue = [];
        this.pollInterval = 1000; // 1秒轮询一次
        this.pollTimers = new Map();
        
        this.initializeUI();
    }

    /**
     * 初始化UI组件
     */
    initializeUI() {
        // 创建上传进度面板
        this.createProgressPanel();
        
        // 绑定文件选择事件
        this.bindFileInputEvents();
    }

    /**
     * 使用统一的传输管理器，不创建独立面板
     */
    createProgressPanel() {
        // 不再创建独立面板，使用统一的传输管理器
        console.log('使用统一传输管理器，不创建独立上传面板');
        return;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .async-upload-panel {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 400px;
                max-height: 500px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1050;
                display: none;
            }
            .async-upload-panel.show {
                display: block;
            }
            .async-upload-panel .panel-header {
                padding: 12px 16px;
                border-bottom: 1px solid #dee2e6;
                background: #f8f9fa;
                border-radius: 8px 8px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .async-upload-panel .panel-body {
                padding: 16px;
                max-height: 400px;
                overflow-y: auto;
            }
            .upload-task-item {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 12px;
                margin-bottom: 12px;
                background: #f8f9fa;
            }
            .upload-task-item:last-child {
                margin-bottom: 0;
            }
            .task-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }
            .task-filename {
                font-weight: 500;
                color: #495057;
                font-size: 14px;
                flex: 1;
                margin-right: 8px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .task-status {
                font-size: 12px;
                padding: 2px 8px;
                border-radius: 12px;
                font-weight: 500;
            }
            .task-status.pending { background: #fff3cd; color: #856404; }
            .task-status.uploading { background: #d1ecf1; color: #0c5460; }
            .task-status.completed { background: #d4edda; color: #155724; }
            .task-status.failed { background: #f8d7da; color: #721c24; }
            .task-status.cancelled { background: #e2e3e5; color: #6c757d; }
            .task-progress {
                margin-bottom: 8px;
            }
            .progress {
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
            }
            .progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #007bff, #0056b3);
                transition: width 0.3s ease;
            }
            .task-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                color: #6c757d;
            }
            .task-actions {
                display: flex;
                gap: 4px;
            }
            .task-actions .btn {
                padding: 2px 6px;
                font-size: 11px;
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(panel);
    }

    /**
     * 绑定文件输入事件
     */
    bindFileInputEvents() {
        // 重写原有的上传函数
        window.uploadFileAsync = (file, path, overwrite = false) => {
            return this.uploadFile(file, path, overwrite);
        };
    }

    /**
     * 上传文件（异步）
     */
    async uploadFile(file, destinationPath, overwrite = false) {
        console.log('AsyncFileUploadManager.uploadFile 被调用:', {
            filename: file ? file.name : 'null',
            size: file ? file.size : 0,
            path: destinationPath,
            overwrite: overwrite
        });

        if (!file) {
            this.showToast('error', '请选择文件');
            return false;
        }

        // 使用统一传输管理器显示任务
        if (typeof fileTransferManager !== 'undefined') {
            const transferId = fileTransferManager.addUpload(file.name, file.size, destinationPath, overwrite);
            console.log('已添加到统一传输管理器:', transferId);
        } else {
            console.warn('统一传输管理器不存在，使用备用方案');
        }

        console.log('已创建临时任务并显示UI:', tempTaskId);

        // 检查并发上传限制
        if (this.currentUploads >= this.maxConcurrentUploads) {
            this.uploadQueue.push({ file, destinationPath, overwrite });
            this.showToast('info', '已加入上传队列，等待处理...');

            // 更新临时任务状态
            tempTask.status = 'queued';
            this.renderTask(tempTask);
            return true;
        }

        try {
            // 更新状态为正在初始化
            tempTask.status = 'initializing';
            tempTask.progress = 5;
            this.renderTask(tempTask);

            // 创建FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('path', destinationPath);
            formData.append('overwrite', overwrite);

            console.log('正在发送异步上传请求...');

            // 发起异步上传请求
            const response = await fetch('/ansible/api/file-manager/upload-async', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const result = await response.json();
            console.log('异步上传请求响应:', result);

            if (result.success) {
                const taskId = result.task_id;
                console.log('获得真实任务ID:', taskId);

                // 使用统一传输管理器
                if (typeof fileTransferManager !== 'undefined') {
                    const transferId = fileTransferManager.addUpload(file.name, file.size, destinationPath, overwrite);

                    // 保存任务ID映射
                    const transfer = fileTransferManager.transfers.find(t => t.id === transferId);
                    if (transfer) {
                        transfer.taskId = taskId;
                        transfer.status = 'running';
                        transfer.progress = 5;
                        fileTransferManager.updateTransferUI(transfer);
                    }

                    // 开始轮询状态
                    this.startPolling(taskId, transferId);

                    this.showToast('success', '文件上传已开始');
                    console.log('异步上传启动成功，使用统一传输管理器');
                    return true;
                } else {
                    // 备用方案：使用原有逻辑
                    const task = {
                        id: taskId,
                        filename: file.name,
                        status: 'pending',
                        progress: 5,
                        speed: 0,
                        fileSize: file.size,
                        startTime: Date.now()
                    };

                    this.uploadTasks.set(taskId, task);
                    this.currentUploads++;

                    this.renderTask(task);
                    this.startPolling(taskId);

                    this.showToast('success', '文件上传已开始');
                    console.log('异步上传启动成功，使用备用方案');
                    return true;
                }
            } else {
                console.error('异步上传请求失败:', result.message);

                // 更新临时任务为失败状态
                tempTask.status = 'failed';
                tempTask.error_message = result.message || '上传失败';
                this.renderTask(tempTask);

                this.showToast('error', result.message || '上传失败');
                return false;
            }
        } catch (error) {
            console.error('上传文件失败:', error);

            // 更新临时任务为失败状态
            tempTask.status = 'failed';
            tempTask.error_message = error.message;
            this.renderTask(tempTask);

            this.showToast('error', '上传文件失败: ' + error.message);
            return false;
        }
    }

    /**
     * 开始轮询任务状态
     */
    startPolling(taskId) {
        const timer = setInterval(async () => {
            try {
                const response = await fetch(`/ansible/api/file-manager/upload-status/${taskId}`, {
                    credentials: 'same-origin'
                });
                
                const result = await response.json();
                
                if (result.success && result.status) {
                    const task = this.uploadTasks.get(taskId);
                    if (task) {
                        // 更新任务状态
                        Object.assign(task, result.status);
                        this.renderTask(task);
                        
                        // 检查是否完成
                        if (['completed', 'failed', 'cancelled'].includes(task.status)) {
                            this.stopPolling(taskId);
                            this.currentUploads--;
                            
                            if (task.status === 'completed') {
                                this.showToast('success', `文件 ${task.filename} 上传完成`);
                                // 刷新文件列表
                                if (typeof loadFileList === 'function') {
                                    loadFileList(currentPath);
                                }
                            } else if (task.status === 'failed') {
                                this.showToast('error', `文件 ${task.filename} 上传失败: ${task.error_message || '未知错误'}`);
                            }
                            
                            // 处理队列中的下一个任务
                            this.processQueue();
                        }
                    }
                }
            } catch (error) {
                console.error('轮询状态失败:', error);
                this.stopPolling(taskId);
            }
        }, this.pollInterval);
        
        this.pollTimers.set(taskId, timer);
    }

    /**
     * 停止轮询
     */
    stopPolling(taskId) {
        const timer = this.pollTimers.get(taskId);
        if (timer) {
            clearInterval(timer);
            this.pollTimers.delete(taskId);
        }
    }

    /**
     * 处理上传队列
     */
    processQueue() {
        if (this.uploadQueue.length > 0 && this.currentUploads < this.maxConcurrentUploads) {
            const { file, destinationPath, overwrite } = this.uploadQueue.shift();
            this.uploadFile(file, destinationPath, overwrite);
        }
    }

    /**
     * 取消上传任务
     */
    async cancelTask(taskId) {
        try {
            const response = await fetch(`/ansible/api/file-manager/upload-cancel/${taskId}`, {
                method: 'POST',
                credentials: 'same-origin'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.stopPolling(taskId);
                const task = this.uploadTasks.get(taskId);
                if (task) {
                    task.status = 'cancelled';
                    this.renderTask(task);
                }
                this.currentUploads--;
                this.processQueue();
                this.showToast('info', '上传已取消');
            }
        } catch (error) {
            console.error('取消上传失败:', error);
        }
    }

    /**
     * 渲染任务UI
     */
    renderTask(task) {
        const container = document.getElementById('upload-tasks-container');
        const noUploads = container.querySelector('.no-uploads');
        
        if (noUploads) {
            noUploads.style.display = 'none';
        }
        
        let taskElement = document.getElementById(`upload-task-${task.id}`);
        
        if (!taskElement) {
            taskElement = document.createElement('div');
            taskElement.id = `upload-task-${task.id}`;
            taskElement.className = 'upload-task-item';
            container.appendChild(taskElement);
        }
        
        const statusText = {
            'pending': '等待中',
            'queued': '队列中',
            'initializing': '初始化中',
            'uploading': '上传中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        };
        
        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };
        
        const formatSpeed = (bytesPerSecond) => {
            if (!bytesPerSecond) return '0 B/s';
            return formatFileSize(bytesPerSecond) + '/s';
        };
        
        taskElement.innerHTML = `
            <div class="task-header">
                <div class="task-filename" title="${task.filename}">${task.filename}</div>
                <span class="task-status ${task.status}">${statusText[task.status] || task.status}</span>
            </div>
            <div class="task-progress">
                <div class="progress">
                    <div class="progress-bar" style="width: ${task.progress || 0}%"></div>
                </div>
            </div>
            <div class="task-info">
                <span>${Math.round(task.progress || 0)}% - ${formatFileSize(task.file_size || 0)}</span>
                <span>${formatSpeed(task.speed || 0)}</span>
                <div class="task-actions">
                    ${task.status === 'uploading' || task.status === 'pending' ? 
                        `<button class="btn btn-outline-danger btn-sm" onclick="asyncUploadManager.cancelTask('${task.id}')">
                            <i class="bi bi-x"></i>
                        </button>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 显示/隐藏面板
     */
    showPanel() {
        const panel = document.getElementById('async-upload-panel');
        panel.classList.add('show');
    }

    hidePanel() {
        const panel = document.getElementById('async-upload-panel');
        panel.classList.remove('show');
    }

    togglePanel() {
        const panel = document.getElementById('async-upload-panel');
        panel.classList.toggle('show');
    }

    /**
     * 显示提示消息
     */
    showToast(type, message) {
        if (typeof showToast === 'function') {
            showToast(type, message);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 初始化异步上传管理器
window.asyncUploadManager = new AsyncFileUploadManager();

// 等待DOM加载完成后再覆盖上传函数
document.addEventListener('DOMContentLoaded', function() {
    // 延迟一点时间确保所有脚本都加载完成
    setTimeout(function() {
        console.log('正在初始化异步上传功能...');

        // 保存原始的上传函数
        if (typeof window.uploadFile === 'function') {
            window.originalUploadFile = window.uploadFile;
            console.log('已保存原始上传函数');
        }

        // 覆盖上传函数
        window.uploadFile = function() {
            console.log('异步上传函数被调用');

            const fileInput = document.getElementById('fileToUpload');
            const pathInput = document.getElementById('uploadPath');
            const overwriteCheckbox = document.getElementById('overwriteFile');

            if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                asyncUploadManager.showToast('error', '请选择文件');
                return;
            }

            const file = fileInput.files[0];
            const path = pathInput ? pathInput.value : (typeof currentPath !== 'undefined' ? currentPath : '/');
            const overwrite = overwriteCheckbox ? overwriteCheckbox.checked : false;

            console.log('上传参数:', {
                filename: file.name,
                size: file.size,
                path: path,
                overwrite: overwrite
            });

            console.log('开始异步上传:', {
                filename: file.name,
                size: file.size,
                path: path,
                overwrite: overwrite
            });

            // 使用统一传输管理器
            if (typeof fileTransferManager !== 'undefined') {
                console.log('使用统一传输管理器');

                // 立即显示传输管理器
                fileTransferManager.showTransferManager();

                // 立即添加上传任务并显示
                const transferId = fileTransferManager.addUpload(file.name, file.size, path, overwrite);
                const transfer = fileTransferManager.transfers.find(t => t.id === transferId);

                // 立即更新状态为准备中
                transfer.status = 'pending';
                transfer.progress = 0;
                fileTransferManager.updateTransferUI(transfer);

                // 显示提示
                if (typeof showToast === 'function') {
                    showToast('info', '文件上传已开始，请查看传输管理器');
                }

                // 开始上传
                fileTransferManager.startUpload(transfer, file);

                // 清空文件输入框并隐藏模态框
                fileInput.value = '';
                const modalElement = document.getElementById('uploadFileModal');
                if (modalElement) {
                    try {
                        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
                        modal.hide();
                    } catch (error) {
                        console.error('隐藏模态框失败:', error);
                    }
                }
                return;
            }

            // 备用方案：使用异步上传管理器
            asyncUploadManager.showPanel();
            asyncUploadManager.uploadFile(file, path, overwrite).then(success => {
                if (success) {
                    console.log('异步上传启动成功');

                    // 清空文件输入框
                    fileInput.value = '';

                    // 隐藏模态框
                    const modalElement = document.getElementById('uploadFileModal');
                    if (modalElement) {
                        try {
                            const modal = bootstrap.Modal.getInstance(modalElement);
                            if (modal) {
                                modal.hide();
                            } else {
                                // 如果没有实例，创建一个新的并隐藏
                                const newModal = new bootstrap.Modal(modalElement);
                                newModal.hide();
                            }
                        } catch (error) {
                            console.error('隐藏模态框失败:', error);
                        }
                    }
                } else {
                    console.log('异步上传启动失败');
                }
            }).catch(error => {
                console.error('异步上传出错:', error);
                asyncUploadManager.showToast('error', '上传失败: ' + error.message);
            });
        };

        console.log('异步上传功能初始化完成');
    }, 500); // 延迟500ms确保所有脚本加载完成
});

// 如果页面已经加载完成，立即执行
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(function() {
        if (typeof window.uploadFile === 'function') {
            window.originalUploadFile = window.uploadFile;
        }

        window.uploadFile = function() {
            console.log('异步上传函数被调用（立即执行版本）');

            const fileInput = document.getElementById('fileToUpload');
            const pathInput = document.getElementById('uploadPath');
            const overwriteCheckbox = document.getElementById('overwriteFile');

            if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                asyncUploadManager.showToast('error', '请选择文件');
                return;
            }

            const file = fileInput.files[0];
            const path = pathInput ? pathInput.value : (typeof currentPath !== 'undefined' ? currentPath : '/');
            const overwrite = overwriteCheckbox ? overwriteCheckbox.checked : false;

            // 使用统一传输管理器
            if (typeof fileTransferManager !== 'undefined') {
                console.log('使用统一传输管理器（立即执行版本）');

                // 立即显示传输管理器
                fileTransferManager.showTransferManager();

                // 立即添加上传任务并显示
                const transferId = fileTransferManager.addUpload(file.name, file.size, path, overwrite);
                const transfer = fileTransferManager.transfers.find(t => t.id === transferId);

                // 立即更新状态为准备中
                transfer.status = 'pending';
                transfer.progress = 0;
                fileTransferManager.updateTransferUI(transfer);

                // 显示提示
                if (typeof showToast === 'function') {
                    showToast('info', '文件上传已开始，请查看传输管理器');
                }

                // 开始上传
                fileTransferManager.startUpload(transfer, file);

                fileInput.value = '';
                const modalElement = document.getElementById('uploadFileModal');
                if (modalElement) {
                    try {
                        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
                        modal.hide();
                    } catch (error) {
                        console.error('隐藏模态框失败:', error);
                    }
                }
                return;
            }

            // 备用方案
            asyncUploadManager.showPanel();
            asyncUploadManager.uploadFile(file, path, overwrite).then(success => {
                if (success) {
                    fileInput.value = '';
                    const modalElement = document.getElementById('uploadFileModal');
                    if (modalElement) {
                        try {
                            const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
                            modal.hide();
                        } catch (error) {
                            console.error('隐藏模态框失败:', error);
                        }
                    }
                }
            });
        };
    }, 100);
}
