#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Flask应用工厂
统一应用创建、配置加载、扩展初始化和蓝图注册
"""

import os
import logging
from flask import Flask
from src.utils.extensions import db
from src.utils.error_handler import register_error_handlers
from src.utils.error_monitor import error_monitor

logger = logging.getLogger(__name__)

def create_app(config_name='default'):
    """
    应用工厂函数
    创建并配置Flask应用实例
    """
    # 获取项目根目录
    import os
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # 创建Flask应用，指定模板和静态文件路径
    app = Flask(__name__,
                template_folder=os.path.join(project_root, 'templates'),
                static_folder=os.path.join(project_root, 'static'))
    
    # 加载配置
    load_config(app)
    
    # 初始化扩展
    init_extensions(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 设置应用钩子
    setup_app_hooks(app)
    
    # 初始化数据库
    init_databases(app)
    
    return app

def load_config(app):
    """加载应用配置"""
    # 基础配置
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key')
    app.config['JWT_SECRET'] = os.environ.get('JWT_SECRET', 'jwt-secret-key')
    
    # 数据库配置
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('MAIN_DB_URI', 'sqlite:///app.db')

    # 只有在环境变量存在时才添加绑定
    binds = {}
    if os.environ.get('MYSQL_AUDIT_DB_URI'):
        binds['mysql_audit'] = os.environ.get('MYSQL_AUDIT_DB_URI')
    if os.environ.get('ANSIBLE_DB_URI'):
        binds['ansible'] = os.environ.get('ANSIBLE_DB_URI')

    if binds:
        app.config['SQLALCHEMY_BINDS'] = binds
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_POOL_SIZE'] = 10
    app.config['SQLALCHEMY_MAX_OVERFLOW'] = 20
    app.config['SQLALCHEMY_POOL_TIMEOUT'] = 30
    app.config['SQLALCHEMY_POOL_RECYCLE'] = 1800
    
    # 版本信息
    app.config['CURRENT_VERSION'] = 'v4.8.5'
    app.config['SHOW_VERSION_NOTIFICATION'] = False
    app.config['VERSION_RELEASE_NOTES'] = """1. 新增数据治理模块，支持对数据库进行全面质量检查
2. 优化数据中台功能，提升数据加载速度
3. 新增数据显示模块，支持对数据进行可视化展示
4. 修复已知问题，提高系统稳定性
5. 新增ansible模块，支持对服务器进行批量管理
6. 新增入湖数据统计功能，支持对入湖数据进行统计
7. 新增库表挂接率功能，支持对目录挂接情况进行统计分析
8. 实施代码重构优化，提高系统可维护性"""
    
    # 错误监控配置
    app.config['ERROR_MONITOR'] = error_monitor
    
    logger.info("应用配置加载完成")

def init_extensions(app):
    """初始化Flask扩展"""
    # 初始化SQLAlchemy
    db.init_app(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    logger.info("Flask扩展初始化完成")

def register_blueprints(app):
    """注册所有蓝图"""
    
    # 核心路由蓝图
    try:
        from src.core_routes.routes import core_bp
        app.register_blueprint(core_bp)
        logger.info("已注册核心路由蓝图")
    except ImportError as e:
        logger.warning(f"无法导入核心路由蓝图: {e}")
    
    # 网络工具模块蓝图
    try:
        from src.net_tools import net_tools_bp
        app.register_blueprint(net_tools_bp)
        logger.info("已注册网络工具蓝图")
    except ImportError as e:
        logger.warning(f"无法导入网络工具蓝图: {e}")
    
    # 服务器管理模块蓝图
    try:
        from src.server_management.server_routes import server_bp
        app.register_blueprint(server_bp, url_prefix='')
        logger.info("已注册服务器管理蓝图")
    except ImportError as e:
        logger.warning(f"无法导入服务器管理蓝图: {e}")
    
    # 数据中台模块蓝图
    try:
        from src.data_center.data_center_routes import data_center
        app.register_blueprint(data_center, url_prefix='')
        logger.info("已注册数据中台蓝图")
    except ImportError as e:
        logger.warning(f"无法导入数据中台蓝图: {e}")
    
    # Ansible Work 模块蓝图
    try:
        from src.ansible_work import ansible_bp
        app.register_blueprint(ansible_bp)
        logger.info("已注册Ansible蓝图")
    except ImportError as e:
        logger.warning(f"无法导入Ansible蓝图: {e}")
    
    # MySQL Audit 蓝图
    try:
        from src.mysql_audit.audit_routes import mysql_audit_bp
        app.register_blueprint(mysql_audit_bp)
        logger.info("已注册MySQL审计蓝图")
    except ImportError as e:
        logger.warning(f"无法导入MySQL审计蓝图: {e}")
    
    # 区县数据模块蓝图
    try:
        from src.county_data import county_data_bp
        app.register_blueprint(county_data_bp, url_prefix='/county')
        logger.info("已注册区县数据蓝图")
    except ImportError as e:
        logger.warning(f"无法导入区县数据蓝图: {e}")
    
    # 数据展示蓝图
    try:
        from src.county_data.data_display import data_display
        app.register_blueprint(data_display)
        logger.info("已注册数据展示蓝图")
    except ImportError as e:
        logger.warning(f"无法导入数据展示蓝图: {e}")
    
    # 搜索与辅助查询蓝图
    try:
        from src.county_data.search_routes import search_bp
        app.register_blueprint(search_bp)
        logger.info("已注册搜索蓝图")
    except ImportError as e:
        logger.warning(f"无法导入搜索蓝图: {e}")
    
    # 数据可视化和统计蓝图
    try:
        from src.county_data.data_visualization_routes import visualization_bp
        app.register_blueprint(visualization_bp)
        logger.info("已注册数据可视化蓝图")
    except ImportError as e:
        logger.warning(f"无法导入数据可视化蓝图: {e}")
    
    # 县区卡片统计数据蓝图
    try:
        from src.county_data.summary_stats_routes import summary_stats_bp
        app.register_blueprint(summary_stats_bp)
        logger.info("已注册县区卡片统计蓝图")
    except ImportError as e:
        logger.warning(f"无法导入县区卡片统计蓝图: {e}")
    
    # 库表挂接率蓝图
    try:
        from src.county_data.table_connection_rate import table_connection_rate_bp
        app.register_blueprint(table_connection_rate_bp)
        logger.info("已注册库表挂接率蓝图")
    except ImportError as e:
        logger.warning(f"无法导入库表挂接率蓝图: {e}")
    
    # 指标统计蓝图
    try:
        from src.county_data.metrics_stats_routes import metrics_stats_bp
        app.register_blueprint(metrics_stats_bp)
        logger.info("已注册指标统计蓝图")
    except ImportError as e:
        logger.warning(f"无法导入指标统计蓝图: {e}")
    
    # 错误统计蓝图
    try:
        from src.utils.error_stats_routes import error_stats_bp
        app.register_blueprint(error_stats_bp)
        logger.info("已注册错误统计蓝图")
    except ImportError as e:
        logger.warning(f"无法导入错误统计蓝图: {e}")

    # 性能监控蓝图
    try:
        from src.utils.performance_routes import performance_bp
        app.register_blueprint(performance_bp)
        logger.info("已注册性能监控蓝图")
    except ImportError as e:
        logger.warning(f"无法导入性能监控蓝图: {e}")

def setup_app_hooks(app):
    """设置应用钩子函数"""
    
    @app.teardown_appcontext
    def close_db_connections(exception=None):
        """在应用上下文结束时关闭数据库连接"""
        try:
            from src.utils.db_manager import DatabaseManager
            DatabaseManager.close_all_connections()
        except Exception as e:
            logger.error(f"关闭数据库连接时发生错误: {str(e)}")
        
        if exception:
            logger.error(f"应用上下文结束时发生错误: {str(exception)}")
    
    # 注意：before_first_request 在 Flask 2.2+ 中已弃用
    # 如果需要，可以使用 before_request 或其他方式

def init_databases(app):
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            logger.info("数据库表创建成功")
            
            # 初始化MySQL审计数据库
            try:
                from src.mysql_audit.models import init_db
                from src.mysql_audit.audit_routes import load_system_settings
                
                if init_db():
                    logger.info("MySQL审计数据库初始化成功")
                    load_system_settings()
                    logger.info("MySQL审计系统设置加载成功")
                else:
                    logger.warning("MySQL审计数据库初始化失败")
            except ImportError as e:
                logger.warning(f"无法初始化MySQL审计数据库: {e}")
            except Exception as e:
                logger.error(f"MySQL审计数据库初始化过程中发生错误: {e}")
            
            # 初始化数据中台缓存表
            try:
                from src.data_center.data_center_routes import init_cache_tables
                init_cache_tables()
                logger.info("数据中台缓存表初始化成功")
            except ImportError as e:
                logger.warning(f"无法初始化数据中台缓存表: {e}")
            except Exception as e:
                logger.error(f"数据中台缓存表初始化过程中发生错误: {e}")
                
        except Exception as e:
            logger.error(f"数据库初始化过程中发生错误: {e}")

def create_minimal_app():
    """创建最小化应用（用于测试）"""
    app = Flask(__name__)
    load_config(app)
    init_extensions(app)
    return app
