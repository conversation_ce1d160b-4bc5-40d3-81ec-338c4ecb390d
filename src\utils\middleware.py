# -*- coding: utf-8 -*-
"""
请求中间件
提供请求预处理、日志记录、安全检查等功能
"""

import time
import uuid
import logging
from typing import Optional
from flask import request, g, current_app
from functools import wraps

from .exceptions import SecurityError, RateLimitError
from .input_validator import input_validator

logger = logging.getLogger(__name__)

class RequestMiddleware:
    """请求中间件"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        app.teardown_request(self.teardown_request)
    
    @staticmethod
    def before_request():
        """请求前处理"""
        # 生成请求ID
        request.request_id = str(uuid.uuid4())
        
        # 记录请求开始时间
        g.start_time = time.time()
        
        # 记录请求信息
        logger.info(f"请求开始 [ID: {request.request_id}] {request.method} {request.url}")
        
        # 安全检查
        RequestMiddleware._security_check()
        
        # 请求大小检查
        RequestMiddleware._check_request_size()
    
    @staticmethod
    def after_request(response):
        """请求后处理"""
        # 计算请求处理时间
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            
            # 记录响应信息
            logger.info(f"请求完成 [ID: {getattr(request, 'request_id', 'unknown')}] "
                       f"状态码: {response.status_code}, 耗时: {duration:.3f}s")
            
            # 添加响应头
            response.headers['X-Request-ID'] = getattr(request, 'request_id', 'unknown')
            response.headers['X-Response-Time'] = f"{duration:.3f}s"
        
        # 安全响应头
        RequestMiddleware._add_security_headers(response)
        
        return response
    
    @staticmethod
    def teardown_request(exception=None):
        """请求清理"""
        if exception:
            logger.error(f"请求异常 [ID: {getattr(request, 'request_id', 'unknown')}]: {str(exception)}")
    
    @staticmethod
    def _security_check():
        """安全检查"""
        # 检查User-Agent
        user_agent = request.headers.get('User-Agent', '')
        if not user_agent:
            logger.warning(f"请求缺少User-Agent [ID: {request.request_id}]")
        
        # 检查可疑的User-Agent
        suspicious_agents = ['sqlmap', 'nmap', 'nikto', 'dirb', 'gobuster']
        if any(agent in user_agent.lower() for agent in suspicious_agents):
            raise SecurityError("检测到可疑的User-Agent")
        
        # 检查请求头中的可疑内容
        for header_name, header_value in request.headers:
            if any(keyword in header_value.lower() for keyword in ['<script', 'javascript:', 'vbscript:']):
                raise SecurityError("请求头包含可疑内容")
    
    @staticmethod
    def _check_request_size():
        """检查请求大小"""
        content_length = request.content_length
        if content_length:
            max_size = current_app.config.get('MAX_CONTENT_LENGTH', 10 * 1024 * 1024)  # 默认10MB
            if content_length > max_size:
                raise SecurityError("请求体过大")
    
    @staticmethod
    def _add_security_headers(response):
        """添加安全响应头"""
        # 防止XSS攻击
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # HTTPS相关（如果是HTTPS）
        if request.is_secure:
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # 内容安全策略（根据需要调整）
        response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

class RateLimiter:
    """简单的频率限制器"""
    
    def __init__(self):
        self.requests = {}  # 存储请求记录
    
    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """
        检查是否允许请求
        
        Args:
            key: 限制键（通常是IP地址）
            limit: 限制次数
            window: 时间窗口（秒）
            
        Returns:
            bool: 是否允许
        """
        now = time.time()
        
        # 清理过期记录
        if key in self.requests:
            self.requests[key] = [req_time for req_time in self.requests[key] 
                                 if now - req_time < window]
        else:
            self.requests[key] = []
        
        # 检查是否超过限制
        if len(self.requests[key]) >= limit:
            return False
        
        # 记录当前请求
        self.requests[key].append(now)
        return True

# 全局频率限制器
rate_limiter = RateLimiter()

def require_rate_limit(limit: int = 100, window: int = 3600, key_func=None):
    """
    频率限制装饰器
    
    Args:
        limit: 限制次数
        window: 时间窗口（秒）
        key_func: 生成限制键的函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成限制键
            if key_func:
                key = key_func()
            else:
                key = request.remote_addr or 'unknown'
            
            # 检查频率限制
            if not rate_limiter.is_allowed(key, limit, window):
                raise RateLimitError(f"请求过于频繁，请在 {window} 秒后重试")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def require_json():
    """要求JSON请求的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not request.is_json:
                raise SecurityError("请求必须是JSON格式")
            return func(*args, **kwargs)
        return wrapper
    return decorator

def validate_ip_access(allowed_ips: list = None):
    """IP访问控制装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if allowed_ips:
                client_ip = request.remote_addr
                if client_ip not in allowed_ips:
                    raise SecurityError("IP地址不在允许列表中")
            return func(*args, **kwargs)
        return wrapper
    return decorator

# 全局中间件实例
request_middleware = RequestMiddleware()
