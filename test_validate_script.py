#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试配置验证脚本是否能正常运行
"""

import sys
import os
import subprocess

def test_script_execution():
    """测试脚本执行"""
    print("🧪 测试配置验证脚本执行")
    print("=" * 50)
    
    scripts_to_test = [
        ("根目录入口脚本", "python validate_config.py"),
        ("工具脚本直接运行", "python src/utils/validate_config.py"),
    ]
    
    for name, command in scripts_to_test:
        print(f"\n📋 测试: {name}")
        print(f"命令: {command}")
        print("-" * 30)
        
        try:
            # 运行命令并捕获输出
            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=10
            )
            
            print("✅ 脚本执行成功")
            print("输出:")
            print(result.stdout)
            
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ 脚本执行超时（可能在等待用户输入）")
        except FileNotFoundError:
            print("❌ 找不到 Python 解释器或脚本文件")
        except Exception as e:
            print(f"❌ 执行失败: {str(e)}")

def test_import_paths():
    """测试导入路径"""
    print("\n🔍 测试导入路径")
    print("=" * 50)
    
    # 添加项目根目录到路径
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    tests = [
        ("src.utils.validate_config", "from src.utils.validate_config import main"),
        ("src.utils.db_config", "from src.utils.db_config import DB_HOST"),
    ]
    
    for name, import_statement in tests:
        print(f"\n📦 测试导入: {name}")
        try:
            exec(import_statement)
            print("✅ 导入成功")
        except ImportError as e:
            print(f"❌ 导入失败: {str(e)}")
        except Exception as e:
            print(f"⚠️  导入时发生其他错误: {str(e)}")

def main():
    """主函数"""
    print("🔧 配置验证脚本测试工具")
    print("=" * 60)
    
    # 测试导入路径
    test_import_paths()
    
    # 测试脚本执行
    test_script_execution()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
    print("\n💡 使用建议:")
    print("1. 如果导入测试通过，说明模块结构正确")
    print("2. 如果脚本执行测试通过，说明可以正常运行")
    print("3. 推荐使用: python validate_config.py")

if __name__ == '__main__':
    main()
