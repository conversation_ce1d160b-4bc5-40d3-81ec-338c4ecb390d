# -*- coding: utf-8 -*-
"""
配置管理工具类
用于安全地管理系统配置，支持环境变量和配置文件
"""

import os
import configparser
import logging
from typing import Optional, Dict, Any
import secrets
import hashlib

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器，优先使用环境变量，其次使用配置文件"""
    
    def __init__(self, config_file: str = 'config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
                logger.info(f"配置文件 {self.config_file} 加载成功")
            else:
                logger.warning(f"配置文件 {self.config_file} 不存在，将使用默认配置")
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
    
    def get(self, section: str, key: str, default: Any = None, env_var: str = None) -> Any:
        """
        获取配置值，优先级：环境变量 > 配置文件 > 默认值
        
        Args:
            section: 配置文件中的节名
            key: 配置键名
            default: 默认值
            env_var: 环境变量名，如果不提供则自动生成
        
        Returns:
            配置值
        """
        # 自动生成环境变量名
        if env_var is None:
            env_var = f"{section.upper()}_{key.upper()}"
        
        # 优先从环境变量获取
        env_value = os.environ.get(env_var)
        if env_value is not None:
            return self._convert_type(env_value, default)
        
        # 从配置文件获取
        try:
            if self.config.has_section(section) and self.config.has_option(section, key):
                config_value = self.config.get(section, key)
                if config_value:  # 非空值
                    return self._convert_type(config_value, default)
        except Exception as e:
            logger.warning(f"从配置文件获取 {section}.{key} 失败: {str(e)}")
        
        # 返回默认值
        return default
    
    def _convert_type(self, value: str, reference: Any) -> Any:
        """根据参考值的类型转换字符串值"""
        if reference is None:
            return value
        
        try:
            if isinstance(reference, bool):
                return value.lower() in ('true', '1', 'yes', 'on')
            elif isinstance(reference, int):
                return int(value)
            elif isinstance(reference, float):
                return float(value)
            else:
                return value
        except (ValueError, TypeError):
            logger.warning(f"类型转换失败，返回原始字符串值: {value}")
            return value
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            'host': self.get('DATABASE', 'host', '127.0.0.1'),
            'port': self.get('DATABASE', 'port', 3306),
            'user': self.get('DATABASE', 'user', 'root'),
            'password': self.get('DATABASE', 'password', '', 'DB_PASSWORD'),
            'database_main': self.get('DATABASE', 'database_main', 'excel'),
            'database_mysql_log': self.get('DATABASE', 'database_mysql_log', 'mysql_log'),
            'database_ansible': self.get('DATABASE', 'database_ansible', 'ansible_ui')
        }
    
    def get_security_config(self) -> Dict[str, str]:
        """获取安全配置"""
        # 生成随机密钥作为默认值
        default_secret = secrets.token_hex(32)
        
        return {
            'secret_key': self.get('SECURITY', 'secret_key', default_secret, 'SECRET_KEY'),
            'edit_password_hash': self.get('SECURITY', 'edit_password_hash', '', 'EDIT_PASSWORD_HASH'),
            'jwt_secret': self.get('SECURITY', 'jwt_secret', default_secret, 'JWT_SECRET')
        }
    
    def get_ansible_config(self) -> Dict[str, Any]:
        """获取Ansible配置"""
        return {
            'ansible_host': self.get('ANSIBLE', 'ansible_host', '127.0.0.1'),
            'jump_host': self.get('ANSIBLE', 'jump_host', '127.0.0.1'),
            'jump_port': self.get('ANSIBLE', 'jump_port', 22),
            'ansible_port': self.get('ANSIBLE', 'ansible_port', 22)
        }
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return {
            'port': self.get('SERVER', 'port', 5000),
            'debug': self.get('SERVER', 'debug', False),
            'skip_static_download': self.get('SETTINGS', 'skip_static_download', True)
        }
    
    @staticmethod
    def hash_password(password: str) -> str:
        """生成密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """验证密码"""
        return hashlib.sha256(password.encode()).hexdigest() == password_hash

# 全局配置管理器实例
config_manager = ConfigManager()
