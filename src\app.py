from flask import Flask, render_template, redirect, url_for
from src.county_data.county_routes import county_bp
from src.county_data.summary_stats_routes import summary_stats_bp, create_period
from src.county_data.metrics_stats_routes import metrics_stats_bp
from src.net_tools import net_tools_bp
from src.utils.database_helpers import get_db_connection, init_database
import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import atexit
import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 注册蓝图
app.register_blueprint(county_bp)
app.register_blueprint(summary_stats_bp)
app.register_blueprint(metrics_stats_bp, url_prefix='/metrics')
app.register_blueprint(net_tools_bp)

# 首页
@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

# 初始化定时任务调度器
def init_scheduler():
    """初始化定时任务调度器，设置每周一上午11点自动创建新期次和收集指标数据"""
    scheduler = BackgroundScheduler()
    
    # 添加每周一上午11点执行的任务
    scheduler.add_job(
        func=scheduled_period_creation,
        trigger=CronTrigger(day_of_week='mon', hour=11, minute=0),
        id='create_period_job',
        name='每周一上午11点创建新期次并收集指标数据',
        replace_existing=True
    )
    
    # 添加应用启动时检查和创建期次的任务
    scheduler.add_job(
        func=check_and_create_period_if_needed,
        trigger='date',
        run_date=datetime.datetime.now() + datetime.timedelta(seconds=10),
        id='initial_period_check',
        name='启动时检查期次数据'
    )
    
    # 启动调度器
    scheduler.start()
    logger.info("定时任务调度器已启动")
    
    # 确保应用退出时关闭调度器
    atexit.register(lambda: scheduler.shutdown())

def scheduled_period_creation():
    """定时任务：创建新的期次并收集指标数据"""
    try:
        logger.info("开始执行定时任务：创建新期次并收集指标数据")
        connection = get_db_connection()
        with connection:
            # 调用create_period函数创建新期次并收集指标数据
            result = create_period()
            logger.info(f"定时任务执行结果：{result}")
    except Exception as e:
        logger.error(f"定时任务执行失败：{str(e)}")
        import traceback
        traceback.print_exc()

def check_and_create_period_if_needed():
    """检查是否存在当前期次，如果不存在则创建"""
    try:
        logger.info("检查并创建初始期次数据")
        connection = get_db_connection()
        with connection.begin() as transaction:
            # 检查是否有期次数据
            result = connection.execute("SELECT COUNT(*) FROM periods_metadata").scalar()
            if result == 0:
                logger.info("未找到期次数据，创建初始期次")
                # 执行SQL脚本创建初始期次
                with open('metrics_stats_sql/init_period_data.sql', 'r') as f:
                    sql_script = f.read()
                # 分割并执行SQL语句
                for statement in sql_script.split(';'):
                    if statement.strip():
                        connection.execute(statement)
                logger.info("初始期次创建成功")
                
                # 调用create_period函数收集指标数据
                create_period()
            else:
                logger.info(f"已存在{result}个期次记录，无需创建初始期次")
    except Exception as e:
        logger.error(f"检查和创建初始期次失败：{str(e)}")
        import traceback
        traceback.print_exc()

# 初始化应用
def create_app():
    """初始化应用"""
    # 初始化数据库连接
    init_database()
    
    # 初始化定时任务调度器
    init_scheduler()
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000) 