#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
密钥生成工具
生成安全的密码、密钥和哈希值
"""

import secrets
import string
import hashlib
import os

def generate_random_key(length=64):
    """生成随机密钥"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_strong_password(length=20):
    """生成强密码"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_sha256_hash(password):
    """生成SHA256哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def generate_url_safe_token(length=32):
    """生成URL安全的令牌"""
    return secrets.token_urlsafe(length)

def main():
    """主函数"""
    print("🔐 密钥生成工具")
    print("=" * 50)
    
    # 生成各种密钥
    keys = {
        "SECRET_KEY": generate_random_key(64),
        "JWT_SECRET": generate_random_key(64),
        "API_ACCESS_KEY": generate_random_key(64),
        "RATE_LIMIT_SECRET": generate_random_key(64),
        "ANSIBLE_VAULT_PASSWORD": generate_random_key(64)
    }
    
    # 生成密码
    passwords = {
        "DB_PASSWORD": generate_strong_password(16),
        "ADMIN_PASSWORD": generate_strong_password(16),
        "REDIS_PASSWORD": generate_strong_password(16),
        "MAIL_PASSWORD": generate_strong_password(16)
    }
    
    print("\n🔑 生成的密钥:")
    for name, key in keys.items():
        print(f"{name}={key}")
    
    print("\n🔒 生成的密码:")
    for name, password in passwords.items():
        print(f"{name}={password}")
        if name in ["ADMIN_PASSWORD"]:
            hash_value = generate_sha256_hash(password)
            print(f"{name}_HASH={hash_value}")
    
    print("\n📝 特殊哈希值:")
    # 为常用密码生成哈希
    common_passwords = {
        "AdminEdit@2024!": "编辑模式密码",
        "Admin@123456!": "管理员密码",
        "123456": "默认数据库密码"
    }
    
    for password, description in common_passwords.items():
        hash_value = generate_sha256_hash(password)
        print(f"{description} ({password}): {hash_value}")
    
    print("\n🛡️  URL安全令牌:")
    for i in range(3):
        token = generate_url_safe_token(32)
        print(f"TOKEN_{i+1}={token}")
    
    print("\n" + "=" * 50)
    print("💡 使用建议:")
    print("1. 复制需要的密钥到 .env 文件中")
    print("2. 不要在生产环境使用默认密码")
    print("3. 定期轮换密钥（建议每3-6个月）")
    print("4. 妥善保管密钥，不要泄露")
    
    # 生成 .env 文件模板
    print("\n📄 生成 .env 文件模板...")
    env_content = f"""# 自动生成的环境变量配置
# 生成时间: {os.popen('date').read().strip()}

# 数据库配置
DATABASE_HOST=**************
DATABASE_PORT=3310
DATABASE_USER=root
DB_PASSWORD={passwords['DB_PASSWORD']}
DATABASE_MAIN=excel
DATABASE_MYSQL_LOG=mysql_log
DATABASE_ANSIBLE=ansible_ui

# 安全配置
SECRET_KEY={keys['SECRET_KEY']}
EDIT_PASSWORD_HASH={generate_sha256_hash('AdminEdit@2024!')}
JWT_SECRET={keys['JWT_SECRET']}

# 应用安全配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD_HASH={generate_sha256_hash(passwords['ADMIN_PASSWORD'])}
API_ACCESS_KEY={keys['API_ACCESS_KEY']}
RATE_LIMIT_SECRET={keys['RATE_LIMIT_SECRET']}

# Ansible配置
ANSIBLE_HOST=***********
JUMP_HOST=************
JUMP_PORT=6233
ANSIBLE_PORT=22
ANSIBLE_VAULT_PASSWORD={keys['ANSIBLE_VAULT_PASSWORD']}

# 服务器配置
SERVER_PORT=5100
SERVER_DEBUG=false
SERVER_HOST=0.0.0.0
MAX_CONTENT_LENGTH=10737418240
SESSION_TIMEOUT=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/app.log
ENABLE_VERBOSE_LOGGING=false

# 其他配置
SETTINGS_SKIP_STATIC_DOWNLOAD=true
FORCE_HTTPS=false
ALLOWED_HOSTS=localhost,127.0.0.1,**************
CORS_ORIGINS=http://localhost:5100

# 生成的管理员密码: {passwords['ADMIN_PASSWORD']}
# 请妥善保管此密码！
"""
    
    # 保存到文件
    with open('.env.generated', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ 已生成 .env.generated 文件")
    print("📋 管理员密码:", passwords['ADMIN_PASSWORD'])
    print("🔐 编辑模式密码: AdminEdit@2024!")

if __name__ == '__main__':
    main()
