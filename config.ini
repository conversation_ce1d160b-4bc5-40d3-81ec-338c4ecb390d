[SETTINGS]
; 是否跳过静态文件下载，在内网环境下建议设置为true
skip_static_download = true

; 数据库配置
[DATABASE]
; 主数据库配置
host = **************
port = 3310
user = root
; 密码通过环境变量 DB_PASSWORD 设置
password =
database_main = excel
database_mysql_log = mysql_log
database_ansible = ansible_ui

; 服务器配置
[SERVER]
; 服务器端口
port = 5100
; 是否开启调试模式
debug = true

; 安全配置
[SECURITY]
; 敏感配置通过 .env 文件设置
; SECRET_KEY, EDIT_PASSWORD_HASH, JWT_SECRET

; Ansible配置
[ANSIBLE]
; Ansible主机配置
ansible_host = ***********
jump_host = ************
jump_port = 6233
ansible_port = 22
