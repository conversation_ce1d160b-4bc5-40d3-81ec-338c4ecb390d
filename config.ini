[SETTINGS]
; 是否跳过静态文件下载，在内网环境下建议设置为true
skip_static_download = true

; 数据库配置
[DATABASE]
; 主数据库配置
host = **************
port = 3310
user = root
; 注意：密码应通过环境变量设置，此处仅作为备用
password =
database_main = excel
database_mysql_log = mysql_log
database_ansible = ansible_ui

; 服务器配置
[SERVER]
; 服务器端口，默认为5000
port = 5000
; 是否开启调试模式
debug = true

; 安全配置
[SECURITY]
; 会话密钥（生产环境应通过环境变量设置）
secret_key =
; 密码验证相关
edit_password_hash =
; JWT密钥（如果使用）
jwt_secret =

; Ansible配置
[ANSIBLE]
; Ansible主机配置
ansible_host = ***********
jump_host = ************
jump_port = 6233
ansible_port = 22
