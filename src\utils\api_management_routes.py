"""
API管理模块
提供系统所有API接口的统一管理和展示功能
"""

from flask import Blueprint, render_template, jsonify, request
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
api_management_bp = Blueprint('api_management', __name__, url_prefix='/api-management')

# 系统API配置
SYSTEM_APIS = {
    "核心功能": {
        "description": "系统核心功能API",
        "apis": [
            {
                "name": "首页",
                "url": "/",
                "method": "GET",
                "description": "系统主页面",
                "type": "page"
            },
            {
                "name": "版本信息",
                "url": "/api/version",
                "method": "GET", 
                "description": "获取系统版本信息",
                "type": "api"
            }
        ]
    },
    "数据中台": {
        "description": "数据中台相关功能",
        "apis": [
            {
                "name": "数据中台页面",
                "url": "/data_center",
                "method": "GET",
                "description": "数据中台主页面",
                "type": "page"
            },
            {
                "name": "获取所有表行数",
                "url": "/api/data_center/get_all_tables_count",
                "method": "GET",
                "description": "获取所有数据库表的行数统计",
                "type": "api"
            },
            {
                "name": "缓存统计",
                "url": "/api/data_center/cache_stats",
                "method": "GET",
                "description": "查看缓存表统计信息",
                "type": "api"
            },
            {
                "name": "清理缓存",
                "url": "/api/data_center/clear_cache",
                "method": "POST",
                "description": "清理缓存表",
                "type": "api"
            }
        ]
    },
    "区县数据管理": {
        "description": "区县数据管理相关功能",
        "apis": [
            {
                "name": "数据导入页面",
                "url": "/county/data/data_import",
                "method": "GET",
                "description": "数据导入管理页面",
                "type": "page"
            },
            {
                "name": "数据管理",
                "url": "/api/county_data/records",
                "method": "GET/POST",
                "description": "数据记录的增删改查",
                "type": "api"
            },
            {
                "name": "数据导出",
                "url": "/api/county_data/export",
                "method": "GET",
                "description": "导出数据为Excel文件",
                "type": "api"
            },
            {
                "name": "数据搜索",
                "url": "/api/county_data/search",
                "method": "GET",
                "description": "搜索数据记录",
                "type": "api"
            }
        ]
    },
    "数据展示": {
        "description": "数据可视化和展示功能",
        "apis": [
            {
                "name": "数据展示页面",
                "url": "/data_display",
                "method": "GET",
                "description": "数据展示主页面",
                "type": "page"
            },
            {
                "name": "可视化页面",
                "url": "/visualization",
                "method": "GET",
                "description": "数据可视化页面",
                "type": "page"
            },
            {
                "name": "指标统计页面",
                "url": "/metrics_stats",
                "method": "GET",
                "description": "指标统计分析页面",
                "type": "page"
            },
            {
                "name": "库表挂接率页面",
                "url": "/table_connection_rate",
                "method": "GET",
                "description": "库表挂接率统计页面",
                "type": "page"
            }
        ]
    },
    "MySQL审计": {
        "description": "MySQL数据库审计功能",
        "apis": [
            {
                "name": "MySQL审计页面",
                "url": "/mysql_audit/",
                "method": "GET",
                "description": "MySQL审计主页面",
                "type": "page"
            },
            {
                "name": "用户活动",
                "url": "/mysql_audit/api/user_activity",
                "method": "GET",
                "description": "获取用户活动记录",
                "type": "api"
            },
            {
                "name": "服务器管理",
                "url": "/mysql_audit/api/servers",
                "method": "GET/POST",
                "description": "服务器配置管理",
                "type": "api"
            }
        ]
    },
    "Ansible自动化": {
        "description": "Ansible自动化运维功能",
        "apis": [
            {
                "name": "Ansible中台页面",
                "url": "/ansible/",
                "method": "GET",
                "description": "Ansible自动化运维主页面",
                "type": "page"
            },
            {
                "name": "服务器管理",
                "url": "/ansible/api/servers",
                "method": "GET/POST",
                "description": "服务器管理API",
                "type": "api"
            },
            {
                "name": "任务管理",
                "url": "/ansible/api/tasks",
                "method": "GET/POST",
                "description": "Ansible任务管理",
                "type": "api"
            },
            {
                "name": "Playbook管理",
                "url": "/ansible/api/playbooks",
                "method": "GET/POST",
                "description": "Playbook管理API",
                "type": "api"
            }
        ]
    },
    "错误处理与监控": {
        "description": "系统错误处理和监控功能",
        "apis": [
            {
                "name": "错误统计页面",
                "url": "/error-stats",
                "method": "GET",
                "description": "错误统计监控页面",
                "type": "page"
            },
            {
                "name": "性能监控页面",
                "url": "/performance",
                "method": "GET",
                "description": "系统性能监控页面",
                "type": "page"
            },
            {
                "name": "错误统计API",
                "url": "/api/error-stats",
                "method": "GET",
                "description": "获取错误统计数据",
                "type": "api"
            },
            {
                "name": "性能监控API",
                "url": "/api/performance/stats",
                "method": "GET",
                "description": "获取性能监控数据",
                "type": "api"
            }
        ]
    },
    "网络工具": {
        "description": "网络诊断工具",
        "apis": [
            {
                "name": "网络工具页面",
                "url": "/tools/",
                "method": "GET",
                "description": "网络诊断工具页面",
                "type": "page"
            },
            {
                "name": "Ping测试",
                "url": "/tools/ping",
                "method": "POST",
                "description": "Ping连通性测试",
                "type": "api"
            },
            {
                "name": "端口测试",
                "url": "/tools/telnet",
                "method": "POST",
                "description": "端口连通性测试",
                "type": "api"
            }
        ]
    },
    "系统管理": {
        "description": "系统管理和维护功能",
        "apis": [
            {
                "name": "API管理中心",
                "url": "/api-management/",
                "method": "GET",
                "description": "系统API统一管理页面",
                "type": "page"
            },
            {
                "name": "服务器管理页面",
                "url": "/server_management",
                "method": "GET",
                "description": "服务器管理主页面",
                "type": "page"
            },
            {
                "name": "数据备份",
                "url": "/api/county_data/backup",
                "method": "POST",
                "description": "数据库备份功能",
                "type": "api"
            }
        ]
    },
    "搜索与查询": {
        "description": "数据搜索和查询功能",
        "apis": [
            {
                "name": "表格搜索",
                "url": "/lookup/tables",
                "method": "GET",
                "description": "搜索数据表",
                "type": "api"
            },
            {
                "name": "表格数据查询",
                "url": "/lookup/table_data",
                "method": "GET",
                "description": "查询表格数据",
                "type": "api"
            },
            {
                "name": "多表数据查询",
                "url": "/get_multiple_tables_data",
                "method": "GET",
                "description": "查询多个表的数据",
                "type": "api"
            }
        ]
    }
}

@api_management_bp.route('/')
def api_management_page():
    """API管理主页面"""
    return render_template('api_management.html', apis=SYSTEM_APIS)

@api_management_bp.route('/api/list')
def get_api_list():
    """获取API列表"""
    return jsonify({
        'success': True,
        'data': SYSTEM_APIS
    })

@api_management_bp.route('/api/search')
def search_apis():
    """搜索API"""
    query = request.args.get('q', '').lower()
    if not query:
        return jsonify({'success': True, 'data': SYSTEM_APIS})
    
    filtered_apis = {}
    for category, category_data in SYSTEM_APIS.items():
        filtered_category = {
            'description': category_data['description'],
            'apis': []
        }
        
        for api in category_data['apis']:
            if (query in api['name'].lower() or 
                query in api['url'].lower() or 
                query in api['description'].lower()):
                filtered_category['apis'].append(api)
        
        if filtered_category['apis']:
            filtered_apis[category] = filtered_category
    
    return jsonify({
        'success': True,
        'data': filtered_apis
    })
