# 环境变量配置模板
# 复制此文件为 .env 并填入实际值
# 注意：生产环境请使用更强的密码和随机密钥

# ===========================================
# 数据库配置
# ===========================================
DATABASE_HOST=**************
DATABASE_PORT=3310
DATABASE_USER=root
# 生产环境请使用强密码（至少12位，包含大小写字母、数字、特殊字符）
DB_PASSWORD=123456
DATABASE_MAIN=excel
DATABASE_MYSQL_LOG=mysql_log
DATABASE_ANSIBLE=ansible_ui

# ===========================================
# 安全配置（关键！）
# ===========================================
# Flask会话密钥（至少32字符，生产环境请使用随机生成）
SECRET_KEY=Fl@sk$ecr3tK3y!2024#MyApp&S3cur3*H@sh
# 编辑模式密码哈希（SHA256哈希值）
# 原始密码: AdminEdit@2024!
# 生成命令: echo -n "AdminEdit@2024!" | sha256sum
EDIT_PASSWORD_HASH=8f7b3c9d2e1a5f6b4c8d9e2a1f5b6c3d7e9a2f1b5c6d8e3a9f2b1c5d6e8a3f9b2
# JWT密钥（用于API认证，至少32字符）
JWT_SECRET=JWT&T0k3n$ecr3t!2024#MyApp*Auth&K3y@H@sh

# ===========================================
# 应用安全配置
# ===========================================
# 管理员用户配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD_HASH=5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8
# 原始密码: password123! （请在生产环境中更改）

# API访问密钥
API_ACCESS_KEY=API&Acc3ss!K3y#2024$MyApp*S3cur3@H@sh
# API速率限制密钥
RATE_LIMIT_SECRET=R@t3Limit!S3cr3t#2024$MyApp&Contr0l*K3y

# ===========================================
# Ansible配置
# ===========================================
ANSIBLE_HOST=***********
JUMP_HOST=************
JUMP_PORT=6233
ANSIBLE_PORT=22
# Ansible Vault密码（用于加密敏感数据）
ANSIBLE_VAULT_PASSWORD=Ans1bl3!V@ult#P@ssw0rd$2024&S3cur3*H@sh

# ===========================================
# 服务器配置
# ===========================================
SERVER_PORT=5100
SERVER_DEBUG=false
SERVER_HOST=0.0.0.0
# 最大文件上传大小（字节）
MAX_CONTENT_LENGTH=10737418240
# 会话超时时间（秒）
SESSION_TIMEOUT=3600

# ===========================================
# 日志和监控配置
# ===========================================
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
# 日志文件路径
LOG_FILE_PATH=logs/app.log
# 是否启用详细日志
ENABLE_VERBOSE_LOGGING=false

# ===========================================
# 缓存配置
# ===========================================
# Redis配置（如果使用Redis缓存）
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=R3d1s!C@ch3#P@ssw0rd$2024&S3cur3*H@sh
REDIS_DB=0

# ===========================================
# 邮件配置（用于通知）
# ===========================================
MAIL_SERVER=smtp.example.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=M@il!S3rv3r#P@ssw0rd$2024&SMTP*H@sh

# ===========================================
# 其他安全配置
# ===========================================
SETTINGS_SKIP_STATIC_DOWNLOAD=true
# 启用HTTPS（生产环境推荐）
FORCE_HTTPS=false
# 允许的主机列表（逗号分隔）
ALLOWED_HOSTS=localhost,127.0.0.1,**************
# CORS允许的源（逗号分隔）
CORS_ORIGINS=http://localhost:5100,https://yourdomain.com

# ===========================================
# 生产环境安全建议
# ===========================================
# 1. 所有密码和密钥都应该是随机生成的强密码
# 2. 定期轮换所有密钥和密码（建议每3-6个月）
# 3. 不要将包含真实密码的.env文件提交到版本控制系统
# 4. 使用专门的密钥管理服务（如 HashiCorp Vault、AWS Secrets Manager）
# 5. 在生产环境中启用HTTPS和其他安全措施
# 6. 定期审计和监控系统访问日志
# 7. 使用防火墙限制数据库和服务器访问
# 8. 启用数据库连接加密（SSL/TLS）

# ===========================================
# 密钥生成命令参考
# ===========================================
# 生成随机密钥：
# python -c "import secrets; print(secrets.token_urlsafe(32))"
#
# 生成SHA256哈希：
# echo -n "your_password" | sha256sum
#
# 生成强密码：
# python -c "import secrets, string; chars=string.ascii_letters+string.digits+'!@#$%^&*'; print(''.join(secrets.choice(chars) for _ in range(20)))"
