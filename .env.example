# 系统配置模板
# 复制此文件为 .env 并填入实际配置

# 数据库配置
DB_HOST=**************
DB_PORT=3310
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME_MAIN=excel
DB_NAME_MYSQL_LOG=mysql_log
DB_NAME_ANSIBLE=ansible_ui

# 安全密钥配置（请使用复杂密码）
SECRET_KEY=your_complex_secret_key_at_least_32_chars
EDIT_PASSWORD_HASH=your_edit_password_hash_sha256
JWT_SECRET=your_jwt_secret_key_complex_password

# Ansible配置
ANSIBLE_HOST=***********
JUMP_HOST=************
JUMP_PORT=6233
ANSIBLE_PORT=22

# 注意：不要将包含真实密码的 .env 文件提交到版本控制
