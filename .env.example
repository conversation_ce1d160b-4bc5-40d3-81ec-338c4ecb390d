# 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# 数据库配置
DATABASE_HOST=**************
DATABASE_PORT=3310
DATABASE_USER=root
DB_PASSWORD=your_actual_password_here
DATABASE_DATABASE_MAIN=excel
DATABASE_DATABASE_MYSQL_LOG=mysql_log
DATABASE_DATABASE_ANSIBLE=ansible_ui

# 安全配置
SECRET_KEY=your_secret_key_here_32_chars_minimum
EDIT_PASSWORD_HASH=your_edit_password_hash_here
JWT_SECRET=your_jwt_secret_here

# Ansible配置
ANSIBLE_ANSIBLE_HOST=***********
ANSIBLE_JUMP_HOST=************
ANSIBLE_JUMP_PORT=6233
ANSIBLE_ANSIBLE_PORT=22

# 服务器配置
SERVER_PORT=5000
SERVER_DEBUG=false

# 其他配置
SETTINGS_SKIP_STATIC_DOWNLOAD=true

# 生产环境建议
# 1. 使用强密码和随机密钥
# 2. 定期轮换密钥
# 3. 不要将此文件提交到版本控制系统
# 4. 使用专门的密钥管理服务（如 HashiCorp Vault）
