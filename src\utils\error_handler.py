# -*- coding: utf-8 -*-
"""
统一错误处理器
提供全局错误处理和响应格式化功能
"""

import logging
import traceback
import functools
from typing import Dict, Any, Optional, Tuple
from flask import jsonify, request, current_app
from sqlalchemy.exc import SQLAlchemyError
from werkzeug.exceptions import HTTPException

from .exceptions import (
    BaseAppException, ValidationError, DatabaseError, 
    AuthenticationError, AuthorizationError, ConfigurationError,
    FileProcessingError, ExternalServiceError, BusinessLogicError,
    RateLimitError, SecurityError
)

logger = logging.getLogger(__name__)

class ErrorHandler:
    """统一错误处理器"""
    
    # 错误码映射
    ERROR_CODE_MAPPING = {
        ValidationError: 400,
        AuthenticationError: 401,
        AuthorizationError: 403,
        FileProcessingError: 400,
        BusinessLogicError: 400,
        RateLimitError: 429,
        SecurityError: 403,
        DatabaseError: 500,
        ConfigurationError: 500,
        ExternalServiceError: 502,
    }
    
    @staticmethod
    def handle_exception(error: Exception) -> Tuple[Dict[str, Any], int]:
        """
        处理异常并返回标准化响应
        
        Args:
            error: 异常对象
            
        Returns:
            tuple: (响应数据, HTTP状态码)
        """
        # 生成请求ID用于追踪
        request_id = getattr(request, 'request_id', 'unknown')
        
        # 处理自定义应用异常
        if isinstance(error, BaseAppException):
            status_code = ErrorHandler.ERROR_CODE_MAPPING.get(type(error), 500)
            
            # 记录错误日志
            logger.error(f"应用异常 [请求ID: {request_id}]: {error.message}", 
                        extra={'error_code': error.error_code, 'details': error.details})
            
            response_data = {
                'success': False,
                'error_code': error.error_code,
                'message': error.message,
                'request_id': request_id
            }
            
            # 开发模式下包含详细信息
            if current_app.debug and error.details:
                response_data['details'] = error.details
                
            return response_data, status_code
        
        # 处理SQLAlchemy数据库异常
        elif isinstance(error, SQLAlchemyError):
            logger.error(f"数据库异常 [请求ID: {request_id}]: {str(error)}", 
                        exc_info=True)
            
            response_data = {
                'success': False,
                'error_code': 'DATABASE_ERROR',
                'message': '数据库操作失败',
                'request_id': request_id
            }
            
            # 开发模式下显示详细错误
            if current_app.debug:
                response_data['details'] = str(error)
                
            return response_data, 500
        
        # 处理HTTP异常
        elif isinstance(error, HTTPException):
            logger.warning(f"HTTP异常 [请求ID: {request_id}]: {error.code} - {error.description}")
            
            response_data = {
                'success': False,
                'error_code': f'HTTP_{error.code}',
                'message': error.description or '请求处理失败',
                'request_id': request_id
            }
            
            return response_data, error.code
        
        # 处理其他未知异常
        else:
            logger.error(f"未知异常 [请求ID: {request_id}]: {str(error)}", 
                        exc_info=True)
            
            response_data = {
                'success': False,
                'error_code': 'INTERNAL_ERROR',
                'message': '系统内部错误',
                'request_id': request_id
            }
            
            # 开发模式下显示详细错误
            if current_app.debug:
                response_data['details'] = {
                    'error_type': type(error).__name__,
                    'error_message': str(error),
                    'traceback': traceback.format_exc()
                }
                
            return response_data, 500
    
    @staticmethod
    def register_error_handlers(app):
        """注册全局错误处理器"""
        
        @app.errorhandler(BaseAppException)
        def handle_app_exception(error):
            response_data, status_code = ErrorHandler.handle_exception(error)
            return jsonify(response_data), status_code
        
        @app.errorhandler(SQLAlchemyError)
        def handle_database_error(error):
            response_data, status_code = ErrorHandler.handle_exception(error)
            return jsonify(response_data), status_code
        
        @app.errorhandler(HTTPException)
        def handle_http_exception(error):
            response_data, status_code = ErrorHandler.handle_exception(error)
            return jsonify(response_data), status_code
        
        @app.errorhandler(Exception)
        def handle_generic_exception(error):
            response_data, status_code = ErrorHandler.handle_exception(error)
            return jsonify(response_data), status_code
    
    @staticmethod
    def success_response(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """
        创建成功响应
        
        Args:
            data: 响应数据
            message: 成功消息
            
        Returns:
            dict: 标准化成功响应
        """
        response = {
            'success': True,
            'message': message,
            'request_id': getattr(request, 'request_id', 'unknown')
        }
        
        if data is not None:
            response['data'] = data
            
        return response

def handle_errors(func):
    """
    错误处理装饰器
    自动捕获和处理函数中的异常
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            # 如果返回的是字典且包含success字段，直接返回
            if isinstance(result, dict) and 'success' in result:
                return jsonify(result)
            
            # 如果返回的是元组（通常是Flask视图函数的返回值）
            if isinstance(result, tuple):
                return result
            
            # 其他情况，包装为成功响应
            return jsonify(ErrorHandler.success_response(result))
            
        except Exception as e:
            response_data, status_code = ErrorHandler.handle_exception(e)
            return jsonify(response_data), status_code
    
    return wrapper

def validate_json_input(required_fields: list = None, optional_fields: list = None):
    """
    JSON输入验证装饰器
    
    Args:
        required_fields: 必需字段列表
        optional_fields: 可选字段列表
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not request.is_json:
                raise ValidationError("请求必须是JSON格式")
            
            data = request.get_json()
            if not data:
                raise ValidationError("请求体不能为空")
            
            # 检查必需字段
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    raise ValidationError(f"缺少必需字段: {', '.join(missing_fields)}")
            
            # 检查未知字段
            if required_fields or optional_fields:
                allowed_fields = set(required_fields or []) | set(optional_fields or [])
                unknown_fields = set(data.keys()) - allowed_fields
                if unknown_fields:
                    logger.warning(f"收到未知字段: {', '.join(unknown_fields)}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

# 全局错误处理器实例
error_handler = ErrorHandler()
