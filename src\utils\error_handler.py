#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一错误处理机制
提供系统级的错误处理、日志记录、异常管理功能
"""

import logging
import traceback
import functools
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, Callable, Union
from flask import jsonify, request, current_app
from enum import Enum

class ErrorLevel(Enum):
    """错误级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """错误分类枚举"""
    DATABASE = "database"
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    BUSINESS = "business"
    SYSTEM = "system"
    EXTERNAL_API = "external_api"
    FILE_OPERATION = "file_operation"

class SystemError(Exception):
    """系统自定义异常基类"""
    def __init__(self, message: str, error_code: str = None, 
                 category: ErrorCategory = ErrorCategory.SYSTEM,
                 level: ErrorLevel = ErrorLevel.MEDIUM,
                 details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self._generate_error_code()
        self.category = category
        self.level = level
        self.details = details or {}
        self.timestamp = datetime.now()
    
    def _generate_error_code(self) -> str:
        """生成错误代码"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"SYS_{timestamp}_{id(self) % 10000:04d}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code,
            'message': self.message,
            'category': self.category.value,
            'level': self.level.value,
            'details': self.details,
            'timestamp': self.timestamp.isoformat()
        }

class DatabaseError(SystemError):
    """数据库相关错误"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.DATABASE, **kwargs)

class NetworkError(SystemError):
    """网络相关错误"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.NETWORK, **kwargs)

class ValidationError(SystemError):
    """数据验证错误"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.VALIDATION, level=ErrorLevel.LOW, **kwargs)

class BusinessError(SystemError):
    """业务逻辑错误"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.BUSINESS, **kwargs)

class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_error(self, error: Union[Exception, SystemError], 
                  context: Dict[str, Any] = None):
        """记录错误日志"""
        context = context or {}
        
        if isinstance(error, SystemError):
            error_info = error.to_dict()
            error_info.update(context)
            
            # 根据错误级别选择日志级别
            if error.level == ErrorLevel.CRITICAL:
                self.logger.critical(f"CRITICAL ERROR: {error.message}", extra=error_info)
            elif error.level == ErrorLevel.HIGH:
                self.logger.error(f"HIGH ERROR: {error.message}", extra=error_info)
            elif error.level == ErrorLevel.MEDIUM:
                self.logger.warning(f"MEDIUM ERROR: {error.message}", extra=error_info)
            else:
                self.logger.info(f"LOW ERROR: {error.message}", extra=error_info)
        else:
            # 处理标准异常
            error_info = {
                'error_type': type(error).__name__,
                'message': str(error),
                'traceback': traceback.format_exc(),
                'timestamp': datetime.now().isoformat()
            }
            error_info.update(context)
            self.logger.error(f"UNHANDLED ERROR: {str(error)}", extra=error_info)
    
    def handle_error(self, error: Exception, 
                     context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理错误并返回标准化响应"""
        context = context or {}
        
        # 添加请求上下文信息
        if request:
            context.update({
                'url': request.url,
                'method': request.method,
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')
            })
        
        # 记录错误
        self.log_error(error, context)
        
        # 生成响应
        if isinstance(error, SystemError):
            return {
                'success': False,
                'error_code': error.error_code,
                'message': error.message,
                'category': error.category.value,
                'timestamp': error.timestamp.isoformat()
            }
        else:
            return {
                'success': False,
                'error_code': 'UNKNOWN_ERROR',
                'message': '系统发生未知错误，请联系管理员',
                'category': ErrorCategory.SYSTEM.value,
                'timestamp': datetime.now().isoformat()
            }

# 全局错误处理器实例
error_handler = ErrorHandler()

def handle_exceptions(category: ErrorCategory = ErrorCategory.SYSTEM,
                     level: ErrorLevel = ErrorLevel.MEDIUM,
                     return_json: bool = True):
    """装饰器：统一异常处理"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except SystemError as e:
                # 系统自定义异常
                response = error_handler.handle_error(e)
                if return_json:
                    status_code = 400 if e.level in [ErrorLevel.LOW, ErrorLevel.MEDIUM] else 500
                    return jsonify(response), status_code
                else:
                    return response
            except Exception as e:
                # 未处理的异常
                system_error = SystemError(
                    message=f"函数 {func.__name__} 执行失败: {str(e)}",
                    category=category,
                    level=level,
                    details={'function': func.__name__, 'args': str(args), 'kwargs': str(kwargs)}
                )
                response = error_handler.handle_error(system_error)
                if return_json:
                    return jsonify(response), 500
                else:
                    return response
        return wrapper
    return decorator

def safe_execute(func: Callable, *args, 
                default_return=None, 
                error_category: ErrorCategory = ErrorCategory.SYSTEM,
                **kwargs) -> Any:
    """安全执行函数，捕获异常并返回默认值"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_handler.log_error(e, {
            'function': func.__name__,
            'category': error_category.value
        })
        return default_return

def create_error_response(message: str, 
                         error_code: str = None,
                         status_code: int = 400,
                         details: Dict[str, Any] = None) -> tuple:
    """创建标准化错误响应"""
    response = {
        'success': False,
        'message': message,
        'error_code': error_code or 'GENERIC_ERROR',
        'timestamp': datetime.now().isoformat()
    }
    
    if details:
        response['details'] = details
    
    return jsonify(response), status_code

def create_success_response(data: Any = None, 
                           message: str = None) -> Dict[str, Any]:
    """创建标准化成功响应"""
    response = {
        'success': True,
        'timestamp': datetime.now().isoformat()
    }
    
    if message:
        response['message'] = message
    
    if data is not None:
        response['data'] = data
    
    return jsonify(response)

# Flask错误处理器注册函数
def register_error_handlers(app):
    """注册Flask应用的错误处理器"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return create_error_response("请求的资源不存在", "NOT_FOUND", 404)
    
    @app.errorhandler(500)
    def internal_error(error):
        error_handler.log_error(error, {'type': 'internal_server_error'})
        return create_error_response("服务器内部错误", "INTERNAL_ERROR", 500)
    
    @app.errorhandler(SystemError)
    def handle_system_error(error):
        response = error_handler.handle_error(error)
        status_code = 400 if error.level in [ErrorLevel.LOW, ErrorLevel.MEDIUM] else 500
        return jsonify(response), status_code
    
    @app.errorhandler(Exception)
    def handle_generic_error(error):
        system_error = SystemError(
            message="系统发生未知错误",
            level=ErrorLevel.HIGH,
            details={'original_error': str(error)}
        )
        response = error_handler.handle_error(system_error)
        return jsonify(response), 500

# 重试机制装饰器
def retry_on_error(max_attempts: int = 3,
                   delay: float = 1.0,
                   backoff_factor: float = 2.0,
                   max_delay: float = 60.0,
                   retry_on: tuple = (Exception,)):
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            current_delay = delay

            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except retry_on as e:
                    attempt += 1
                    if attempt >= max_attempts:
                        raise e

                    error_handler.log_error(e, {
                        'function': func.__name__,
                        'attempt': attempt,
                        'max_attempts': max_attempts
                    })

                    time.sleep(min(current_delay, max_delay))
                    current_delay *= backoff_factor

            return func(*args, **kwargs)
        return wrapper
    return decorator
