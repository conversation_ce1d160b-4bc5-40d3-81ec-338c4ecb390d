<!-- 版本更新日志内容 - 所有页面共用 -->
<!-- 此文件只包含更新日志的具体内容，不包含模态框结构 -->
<!-- 当需要更新版本日志时，只需修改此文件 -->


<!-- 版本更新内容 -->
<div class="version-notes">
    <div class="version-header">
        <h4 id="current_version">v4.8.5</h4>
        <span class="release-date">2024年6月03日更新</span>
    </div>
    
    <div class="version-content">
        <h5>优化bug</h5>
        <ul>
            <li class="update-item">
                <span class="update-text">优化年度新增统计在内网不显示的问题</span>
            </li>
            <li class="update-item">
                <span class="update-text">优化以及模块化相关代码</span>
            </li>
        </ul>
    </div>
    <div class="version-content">
        <h5>新增功能</h5>
        <ul>
            <li class="update-item">
                <span class="update-text">数据展示页面新增上周入湖数据统计</span>
            </li>
            <li class="update-item">
                <span class="update-text">数据展示页面新增县区数据统计</span>
            </li>
            <li class="update-item">
                <span class="update-text">新增库表资源挂接率统计页面功能</span>
            </li>
            <li class="update-item">
                <span class="update-text">指标统计页面新增指标变化摘要</span>
            </li>
            <li class="update-item">
                <span class="update-text">下线年度新增统计页面</span>
            </li>
            <li class="update-item">
                <span class="update-text">优化ansible中台的整体代码组织架构</span>
            </li>
            <li class="update-item">
                <span class="update-text">数据中台新增查询缓存功能</span>
            </li>
            <li class="update-item">
                <span class="update-text">新增网络测试工具</span>
            </li>
            <li class="update-item">
                <span class="update-text">优化数据中台大表查询为0de问题</span>
            </li>
        </ul>
    </div>
</div>

<style>
.version-notes {
    padding: 15px;
}

.version-header {
    margin-bottom: 20px;
}

.version-header h4 {
    color: #4a5568;
    margin-bottom: 5px;
    font-size: 1.25rem;
    font-weight: 600;
}

.release-date {
    color: #718096;
    font-size: 0.9em;
}

.version-content h5 {
    color: #2d3748;
    margin-top: 15px;
    margin-bottom: 10px;
    font-weight: 600;
}

.version-content ul {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: 15px;
}

.update-item {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.update-item:before {
    content: "•";
    color: #4299e1;
    position: absolute;
    left: 0;
}

.update-text {
    color: #4a5568;
}
</style> 