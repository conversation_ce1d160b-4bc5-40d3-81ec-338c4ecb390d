@echo off
echo 🚀 设置开发环境...

REM 设置开发环境变量
set DB_PASSWORD=123456
set SECRET_KEY=dev_secret_key_for_development_only_32chars
set EDIT_PASSWORD_HASH=dev_edit_password_hash
set JWT_SECRET=dev_jwt_secret_for_development_only_32chars
set FLASK_ENV=development
set FLASK_DEBUG=1

echo ✅ 环境变量设置完成
echo.
echo ⚠️  注意: 这些是开发环境的默认配置
echo    生产环境请使用安全的密码和密钥!
echo.
echo 🔧 启动应用...
echo.

REM 启动Python应用
python app.py

pause
