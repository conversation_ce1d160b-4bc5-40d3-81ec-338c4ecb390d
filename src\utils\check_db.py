import logging
from app import app # Potential circular import issue?
from src.mysql_audit.models import get_all_servers, get_user_activities, db
from src.mysql_audit.config import DB_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    logger.info("数据库配置信息：")
    logger.info(f"Host: {DB_CONFIG['host']}")
    logger.info(f"Port: {DB_CONFIG['port']}")
    logger.info(f"Database: {DB_CONFIG['database']}")
    
    with app.app_context():
        # 测试数据库连接
        logger.info("\n测试数据库连接...")
        try:
            db.session.execute(text("SELECT 1")) # Use text() for raw SQL
            logger.info("数据库连接成功！")
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            
        # 检查服务器配置
        logger.info("\n检查服务器配置表...")
        try:
            servers = get_all_servers()
            logger.info(f"找到 {len(servers) if servers else 0} 个服务器配置")
            if servers:
                for server in servers:
                    logger.info(f"服务器: {server}")
        except Exception as e:
            logger.error(f"获取服务器配置失败: {str(e)}")
        
        # 检查用户活动
        logger.info("\n检查用户活动表...")
        try:
            activities, total = get_user_activities()
            logger.info(f"总记录数：{total}")
            if activities:
                logger.info("最新的5条记录：")
                for activity in activities[:5]:
                    logger.info(activity)
        except Exception as e:
            logger.error(f"获取用户活动失败: {str(e)}")
            
except Exception as e:
    logger.error(f"执行检查时发生错误: {str(e)}") 