# -*- coding: utf-8 -*-
"""
输入验证工具类
提供各种输入数据的验证功能
"""

import re
import ipaddress
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """验证错误异常"""
    pass

class InputValidator:
    """输入验证器"""
    
    # 常用正则表达式
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    PHONE_PATTERN = re.compile(r'^1[3-9]\d{9}$')  # 中国手机号
    USERNAME_PATTERN = re.compile(r'^[a-zA-Z0-9_]{3,20}$')
    
    @staticmethod
    def validate_string(value: Any, min_length: int = 0, max_length: int = 255, 
                       required: bool = True, pattern: str = None) -> str:
        """
        验证字符串
        
        Args:
            value: 要验证的值
            min_length: 最小长度
            max_length: 最大长度
            required: 是否必需
            pattern: 正则表达式模式
            
        Returns:
            str: 验证后的字符串
            
        Raises:
            ValidationError: 验证失败
        """
        if value is None or value == "":
            if required:
                raise ValidationError("字段不能为空")
            return ""
        
        if not isinstance(value, str):
            value = str(value)
        
        # 长度检查
        if len(value) < min_length:
            raise ValidationError(f"字符串长度不能少于 {min_length} 个字符")
        
        if len(value) > max_length:
            raise ValidationError(f"字符串长度不能超过 {max_length} 个字符")
        
        # 模式检查
        if pattern and not re.match(pattern, value):
            raise ValidationError("字符串格式不正确")
        
        return value.strip()
    
    @staticmethod
    def validate_integer(value: Any, min_value: int = None, max_value: int = None, 
                        required: bool = True) -> Optional[int]:
        """
        验证整数
        
        Args:
            value: 要验证的值
            min_value: 最小值
            max_value: 最大值
            required: 是否必需
            
        Returns:
            int: 验证后的整数
            
        Raises:
            ValidationError: 验证失败
        """
        if value is None or value == "":
            if required:
                raise ValidationError("整数字段不能为空")
            return None
        
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            raise ValidationError("必须是有效的整数")
        
        if min_value is not None and int_value < min_value:
            raise ValidationError(f"数值不能小于 {min_value}")
        
        if max_value is not None and int_value > max_value:
            raise ValidationError(f"数值不能大于 {max_value}")
        
        return int_value
    
    @staticmethod
    def validate_float(value: Any, min_value: float = None, max_value: float = None, 
                      required: bool = True) -> Optional[float]:
        """验证浮点数"""
        if value is None or value == "":
            if required:
                raise ValidationError("浮点数字段不能为空")
            return None
        
        try:
            float_value = float(value)
        except (ValueError, TypeError):
            raise ValidationError("必须是有效的数字")
        
        if min_value is not None and float_value < min_value:
            raise ValidationError(f"数值不能小于 {min_value}")
        
        if max_value is not None and float_value > max_value:
            raise ValidationError(f"数值不能大于 {max_value}")
        
        return float_value
    
    @staticmethod
    def validate_email(email: str, required: bool = True) -> Optional[str]:
        """验证邮箱地址"""
        if not email:
            if required:
                raise ValidationError("邮箱地址不能为空")
            return None
        
        if not InputValidator.EMAIL_PATTERN.match(email):
            raise ValidationError("邮箱地址格式不正确")
        
        return email.lower().strip()
    
    @staticmethod
    def validate_ip_address(ip: str, required: bool = True) -> Optional[str]:
        """验证IP地址"""
        if not ip:
            if required:
                raise ValidationError("IP地址不能为空")
            return None
        
        try:
            ipaddress.ip_address(ip)
            return ip.strip()
        except ValueError:
            raise ValidationError("IP地址格式不正确")
    
    @staticmethod
    def validate_port(port: Any, required: bool = True) -> Optional[int]:
        """验证端口号"""
        if port is None or port == "":
            if required:
                raise ValidationError("端口号不能为空")
            return None
        
        port_int = InputValidator.validate_integer(port, min_value=1, max_value=65535, required=required)
        return port_int
    
    @staticmethod
    def validate_date(date_str: str, date_format: str = "%Y-%m-%d", 
                     required: bool = True) -> Optional[datetime]:
        """验证日期"""
        if not date_str:
            if required:
                raise ValidationError("日期不能为空")
            return None
        
        try:
            return datetime.strptime(date_str, date_format)
        except ValueError:
            raise ValidationError(f"日期格式不正确，应为 {date_format}")
    
    @staticmethod
    def validate_json(json_str: str, required: bool = True) -> Optional[Dict]:
        """验证JSON字符串"""
        if not json_str:
            if required:
                raise ValidationError("JSON数据不能为空")
            return None
        
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            raise ValidationError(f"JSON格式不正确: {str(e)}")
    
    @staticmethod
    def validate_choice(value: Any, choices: List[Any], required: bool = True) -> Any:
        """验证选择项"""
        if value is None or value == "":
            if required:
                raise ValidationError("必须选择一个选项")
            return None
        
        if value not in choices:
            raise ValidationError(f"无效的选择，可选项: {choices}")
        
        return value
    
    @staticmethod
    def validate_file_extension(filename: str, allowed_extensions: List[str]) -> str:
        """验证文件扩展名"""
        if not filename:
            raise ValidationError("文件名不能为空")
        
        if '.' not in filename:
            raise ValidationError("文件必须有扩展名")
        
        extension = filename.rsplit('.', 1)[1].lower()
        if extension not in [ext.lower() for ext in allowed_extensions]:
            raise ValidationError(f"不支持的文件类型，支持的类型: {allowed_extensions}")
        
        return filename
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除危险字符"""
        if not filename:
            raise ValidationError("文件名不能为空")
        
        # 移除路径分隔符和其他危险字符
        dangerous_chars = ['/', '\\', '..', '<', '>', ':', '"', '|', '?', '*']
        sanitized = filename
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '_')
        
        # 移除前后空格和点
        sanitized = sanitized.strip('. ')
        
        if not sanitized:
            raise ValidationError("文件名无效")
        
        return sanitized
    
    @staticmethod
    def validate_pagination(page: Any, per_page: Any, max_per_page: int = 100) -> tuple:
        """验证分页参数"""
        try:
            page = int(page) if page else 1
            per_page = int(per_page) if per_page else 20
        except (ValueError, TypeError):
            raise ValidationError("分页参数必须是整数")
        
        if page < 1:
            page = 1
        
        if per_page < 1:
            per_page = 20
        elif per_page > max_per_page:
            per_page = max_per_page
        
        return page, per_page

# 全局验证器实例
input_validator = InputValidator()
