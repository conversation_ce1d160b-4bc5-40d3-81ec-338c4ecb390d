# -*- coding: utf-8 -*-
"""
SQL安全工具类
提供SQL注入防护、输入验证等安全功能
"""

import re
import logging
from typing import List, Dict, Any, Optional, Union
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

class SQLSecurityError(Exception):
    """SQL安全相关异常"""
    pass

class SQLSecurity:
    """SQL安全工具类"""
    
    # 危险的SQL关键词
    DANGEROUS_KEYWORDS = [
        'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE',
        'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', 'JAVASCRIPT', 'VBSCRIPT',
        'ONLOAD', 'ONERROR', 'ONCLICK', 'EVAL', 'EXPRESSION'
    ]
    
    # 允许的标识符字符（表名、列名等）
    IDENTIFIER_PATTERN = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$')
    
    # 允许的数据库名、表名、列名字符
    DB_NAME_PATTERN = re.compile(r'^[a-zA-Z0-9_]+$')
    
    @staticmethod
    def validate_identifier(identifier: str, max_length: int = 64) -> bool:
        """
        验证数据库标识符（表名、列名等）是否安全
        
        Args:
            identifier: 要验证的标识符
            max_length: 最大长度限制
            
        Returns:
            bool: 是否安全
        """
        if not identifier or len(identifier) > max_length:
            return False
            
        # 检查是否符合标识符规范
        if not SQLSecurity.IDENTIFIER_PATTERN.match(identifier):
            return False
            
        # 检查是否包含危险关键词
        identifier_upper = identifier.upper()
        for keyword in SQLSecurity.DANGEROUS_KEYWORDS:
            if keyword in identifier_upper:
                return False
                
        return True
    
    @staticmethod
    def validate_database_name(db_name: str) -> bool:
        """验证数据库名是否安全"""
        if not db_name or len(db_name) > 64:
            return False
        return SQLSecurity.DB_NAME_PATTERN.match(db_name) is not None
    
    @staticmethod
    def escape_identifier(identifier: str) -> str:
        """
        转义数据库标识符
        
        Args:
            identifier: 要转义的标识符
            
        Returns:
            str: 转义后的标识符
            
        Raises:
            SQLSecurityError: 如果标识符不安全
        """
        if not SQLSecurity.validate_identifier(identifier):
            raise SQLSecurityError(f"不安全的标识符: {identifier}")
        
        # 使用反引号转义
        return f"`{identifier}`"
    
    @staticmethod
    def build_safe_query(base_query: str, params: Dict[str, Any] = None) -> text:
        """
        构建安全的SQL查询
        
        Args:
            base_query: 基础查询语句
            params: 查询参数
            
        Returns:
            text: SQLAlchemy text对象
        """
        try:
            # 创建参数化查询
            query = text(base_query)
            
            # 记录查询日志（不包含敏感参数）
            safe_params = {k: '***' if 'password' in k.lower() else v 
                          for k, v in (params or {}).items()}
            logger.debug(f"执行SQL查询: {base_query}, 参数: {safe_params}")
            
            return query
        except Exception as e:
            logger.error(f"构建SQL查询失败: {str(e)}")
            raise SQLSecurityError(f"SQL查询构建失败: {str(e)}")
    
    @staticmethod
    def validate_search_input(search_term: str, max_length: int = 100) -> str:
        """
        验证和清理搜索输入
        
        Args:
            search_term: 搜索词
            max_length: 最大长度
            
        Returns:
            str: 清理后的搜索词
            
        Raises:
            SQLSecurityError: 如果输入不安全
        """
        if not search_term:
            return ""
        
        # 长度检查
        if len(search_term) > max_length:
            raise SQLSecurityError(f"搜索词过长，最大长度: {max_length}")
        
        # 移除危险字符
        cleaned = re.sub(r'[<>"\';\\]', '', search_term)
        
        # 检查是否包含SQL注入模式
        injection_patterns = [
            r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b)',
            r'(--|/\*|\*/)',
            r'(\bUNION\b.*\bSELECT\b)',
            r'(\bOR\b.*=.*\bOR\b)',
            r'(\bAND\b.*=.*\bAND\b)'
        ]
        
        for pattern in injection_patterns:
            if re.search(pattern, cleaned, re.IGNORECASE):
                raise SQLSecurityError("检测到潜在的SQL注入攻击")
        
        return cleaned.strip()
    
    @staticmethod
    def build_where_clause(conditions: List[Dict[str, Any]]) -> tuple:
        """
        安全地构建WHERE子句
        
        Args:
            conditions: 条件列表，每个条件包含 field, operator, value
            
        Returns:
            tuple: (where_clause, params)
        """
        if not conditions:
            return "", {}
        
        where_parts = []
        params = {}
        param_counter = 0
        
        allowed_operators = ['=', '!=', '>', '<', '>=', '<=', 'LIKE', 'IN', 'NOT IN']
        
        for condition in conditions:
            field = condition.get('field')
            operator = condition.get('operator', '=')
            value = condition.get('value')
            
            # 验证字段名
            if not SQLSecurity.validate_identifier(field):
                raise SQLSecurityError(f"不安全的字段名: {field}")
            
            # 验证操作符
            if operator.upper() not in allowed_operators:
                raise SQLSecurityError(f"不支持的操作符: {operator}")
            
            # 生成参数名
            param_name = f"param_{param_counter}"
            param_counter += 1
            
            # 构建条件
            escaped_field = SQLSecurity.escape_identifier(field)
            if operator.upper() == 'LIKE':
                where_parts.append(f"{escaped_field} LIKE :{param_name}")
                params[param_name] = f"%{value}%"
            elif operator.upper() in ['IN', 'NOT IN']:
                if isinstance(value, (list, tuple)):
                    placeholders = []
                    for i, v in enumerate(value):
                        sub_param = f"{param_name}_{i}"
                        placeholders.append(f":{sub_param}")
                        params[sub_param] = v
                    where_parts.append(f"{escaped_field} {operator} ({','.join(placeholders)})")
                else:
                    raise SQLSecurityError("IN/NOT IN 操作符需要列表类型的值")
            else:
                where_parts.append(f"{escaped_field} {operator} :{param_name}")
                params[param_name] = value
        
        where_clause = " AND ".join(where_parts)
        return where_clause, params
    
    @staticmethod
    def log_sql_execution(query: str, params: Dict[str, Any], execution_time: float, 
                         result_count: int = None, error: Exception = None):
        """
        记录SQL执行日志
        
        Args:
            query: SQL查询
            params: 查询参数
            execution_time: 执行时间
            result_count: 结果数量
            error: 错误信息
        """
        # 过滤敏感参数
        safe_params = {}
        for k, v in (params or {}).items():
            if any(sensitive in k.lower() for sensitive in ['password', 'secret', 'token']):
                safe_params[k] = '***'
            else:
                safe_params[k] = v
        
        log_data = {
            'query': query[:200] + '...' if len(query) > 200 else query,
            'params': safe_params,
            'execution_time': f"{execution_time:.3f}s",
            'result_count': result_count
        }
        
        if error:
            logger.error(f"SQL执行失败: {log_data}, 错误: {str(error)}")
        else:
            if execution_time > 1.0:  # 慢查询
                logger.warning(f"慢查询检测: {log_data}")
            else:
                logger.debug(f"SQL执行成功: {log_data}")

# 全局SQL安全实例
sql_security = SQLSecurity()
