"""
数据中台模块路由
"""
from flask import Blueprint, request, jsonify, render_template, session, send_file
import sys
import os
import json
import pymysql
import pandas as pd
import io
from datetime import datetime
from sqlalchemy import text
from functools import wraps
from contextlib import contextmanager
from typing import List, Dict, Any, Union, Optional, Tuple
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('data_center')

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入加密工具
from src.utils.crypto_util import CryptoUtil

# 创建蓝图
data_center = Blueprint('data_center', __name__)

# 类型定义
Response = Tuple[Dict[str, Any], int]
SuccessResponse = Dict[str, Any]

# 线程安全的ansible_ui数据库engine获取函数
_ansible_ui_engine = None
_engine_lock = threading.Lock()

def get_ansible_ui_engine():
    global _ansible_ui_engine
    
    # 首先检查是否已经初始化，避免每次都需要获取锁
    if _ansible_ui_engine is not None:
        return _ansible_ui_engine
    
    # 使用线程锁确保只有一个线程能创建连接
    with _engine_lock:
        # 双重检查锁定模式，确保在获得锁后再次检查是否已经初始化
        if _ansible_ui_engine is not None:
            return _ansible_ui_engine
            
        try:
            logger.info("开始创建ansible_ui数据库引擎...")
            # 尝试从Flask app配置获取连接字符串
            try:
                from app import AnsibleConfig
                uri = AnsibleConfig.SQLALCHEMY_DATABASE_URI
                logger.info(f"从app模块获取到数据库URI: {uri.split('@')[0].split(':')[0]}:***@***")
            except (ImportError, AttributeError):
                # 如果从app导入失败，直接使用配置模块
                try:
                    from src.ansible_work.config import Config as AnsibleConfig
                    uri = AnsibleConfig.SQLALCHEMY_DATABASE_URI
                    logger.info(f"从config模块获取到数据库URI: {uri.split('@')[0].split(':')[0]}:***@***")
                except (ImportError, AttributeError) as e:
                    logger.error(f"获取数据库配置失败: {str(e)}")
                    raise ValueError("无法获取数据库配置")
            
            # 创建SQLAlchemy引擎，设置较短的超时时间
            from sqlalchemy import create_engine
            _ansible_ui_engine = create_engine(
                uri,
                pool_size=5,  # 减小连接池大小
                max_overflow=10,
                pool_timeout=10,  # 减少超时时间
                pool_recycle=1800,
                connect_args={"connect_timeout": 5}  # 设置MySQL连接超时
            )
            logger.info("成功创建ansible_ui数据库引擎连接池")
            
            # 测试连接是否正常
            try:
                with _ansible_ui_engine.connect() as conn:
                    result = conn.execute(text("SELECT 1")).fetchone()
                    logger.info(f"数据库连接测试成功: {result}")
            except Exception as conn_error:
                logger.error(f"数据库连接测试失败: {str(conn_error)}")
                # 即使测试失败也继续，因为可能是临时问题
                
        except Exception as e:
            logger.error(f"创建ansible_ui数据库引擎失败: {str(e)}", exc_info=True)
            # 重新抛出异常，让调用者处理
            raise
            
    return _ansible_ui_engine

# 初始化缓存表函数
def init_cache_tables(skip_create=False):
    """初始化缓存表，skip_create=True时仅检查表是否存在，不创建新表"""
    max_retries = 2
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 获取数据库引擎
            logger.info("正在获取数据库引擎...")
            engine = get_ansible_ui_engine()
            logger.info("成功获取数据库引擎")
            
            if skip_create:
                logger.info("跳过创建表步骤，仅检查表是否存在")
                with engine.connect() as conn:
                    result = conn.execute(text("SHOW TABLES LIKE 'table_row_count_cache'")).fetchone()
                    if result:
                        logger.info("表行数缓存表已存在，无需创建")
                    else:
                        logger.warning("表行数缓存表不存在，但由于设置了skip_create=True，不会自动创建")
            else:
                # 创建表行数缓存表
                logger.info("检查并创建缓存表...")
                table_created = check_or_create_table_row_count_cache(engine)
                if table_created:
                    logger.info("表行数缓存表成功创建")
                else:
                    logger.info("表行数缓存表已存在，无需创建")
            
            # 检查表中是否有数据
            try:
                logger.info("检查缓存表中的记录数...")
                with engine.connect() as conn:
                    count = conn.execute(text("SELECT COUNT(*) FROM table_row_count_cache")).scalar()
                    logger.info(f"表行数缓存表中共有 {count} 条记录")
            except Exception as count_err:
                logger.error(f"检查缓存表记录数失败: {str(count_err)}")
            
            logger.info("缓存表初始化完成")
            return True
            
        except Exception as e:
            retry_count += 1
            logger.error(f"初始化缓存表失败(第{retry_count}次尝试): {str(e)}", exc_info=True)
            
            if retry_count >= max_retries:
                logger.error(f"初始化缓存表失败，已达到最大重试次数({max_retries})")
                break
                
            # 等待一段时间后重试
            time.sleep(1)  # 等待1秒后重试
    
    return False

# 创建表行数缓存表
def check_or_create_table_row_count_cache(engine=None):
    """
    检查并创建表行数缓存表
    该表存储各个数据库表的行数缓存，避免重复计算
    """
    try:
        if engine is None:
            engine = get_ansible_ui_engine()
            
        with engine.connect() as conn:
            # 先检查表是否已存在
            logger.info("检查表行数缓存表是否存在...")
            result = conn.execute(text("SHOW TABLES LIKE 'table_row_count_cache'")).fetchone()
            if not result:
                logger.info("表行数缓存表不存在，即将创建")
                
                # 使用事务创建表
                try:
                    with engine.begin() as trans_conn:
                        logger.info("开始创建表行数缓存表...")
                        trans_conn.execute(text("""
                        CREATE TABLE IF NOT EXISTS `table_row_count_cache` (
                            `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
                            `database_name` VARCHAR(64) NOT NULL COMMENT '目标数据库的名称',
                            `table_name` VARCHAR(64) NOT NULL COMMENT '目标数据表的名称',
                            `row_count` BIGINT COMMENT '数据表中的记录行数',
                            `source_table_update_time` TIMESTAMP NULL DEFAULT NULL COMMENT '源数据表的最后更新时间（来自INFORMATION_SCHEMA.TABLES的UPDATE_TIME）',
                            `last_calculated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '本条缓存记录的最后计算或更新时间',
                            UNIQUE KEY `idx_db_table` (`database_name`, `table_name`) COMMENT '数据库名和表名的唯一索引，确保每个表只有一条缓存记录'
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '用于缓存数据表行数及其元数据的表';
                        """))
                    logger.info("表行数缓存表创建成功")
                except Exception as create_err:
                    logger.error(f"创建表行数缓存表失败: {str(create_err)}", exc_info=True)
                    # 创建表失败后，再次检查表是否存在(可能是其他进程同时创建了表)
                    result = conn.execute(text("SHOW TABLES LIKE 'table_row_count_cache'")).fetchone()
                    if result:
                        logger.info("表行数缓存表现在存在(可能其他进程创建了它)")
                        return False
                    else:
                        raise
                
                return True
            else:
                logger.info("表行数缓存表已存在")
                return False
    except Exception as e:
        logger.error(f"检查或创建表行数缓存表失败: {str(e)}", exc_info=True)
        return False

# 辅助函数和装饰器
def validate_params(required_params):
    """验证请求中是否包含所有必需参数的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                data = request.json if request.method == 'POST' else request.args
                
                # 如果没有数据
                if not data:
                    print(f"请求没有提供任何数据: {request.path}")
                    return error_response('请求未提供任何数据')
                
                # 验证必要参数
                missing_params = [param for param in required_params if param not in data]
                if missing_params:
                    print(f"缺少必要参数: {missing_params}, 路径: {request.path}")
                    return error_response(f'缺少必要参数: {", ".join(missing_params)}')
                
                # 验证参数值不为空
                empty_params = [param for param in required_params if param in data and not data[param] and not isinstance(data[param], (int, float))]
                if empty_params:
                    print(f"参数值为空: {empty_params}, 路径: {request.path}")
                    return error_response(f'参数值不能为空: {", ".join(empty_params)}')
                
                # 如果port在必要参数中，确保它是整数
                if 'port' in required_params and 'port' in data:
                    try:
                        data['port'] = int(data['port'])
                    except (ValueError, TypeError):
                        print(f"端口参数无效: {data.get('port')}, 路径: {request.path}")
                        return error_response(f'端口必须是数字: {data.get("port")}')
                        
                return func(*args, **kwargs)
            except Exception as e:
                print(f"参数验证过程中出错: {str(e)}, 路径: {request.path}")
                return error_response(f'参数验证失败: {str(e)}')
        return wrapper
    return decorator

@contextmanager
def db_connection(ip, port, account, password, database=None):
    """创建和管理数据库连接的上下文管理器"""
    conn = None
    try:
        conn_params = {
            'host': ip,
            'port': port,
            'user': account,
            'password': password,
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        if database:
            conn_params['db'] = database
            
        conn = pymysql.connect(**conn_params)
        logger.info(f"已成功连接到数据库: {ip}:{port}/{database if database else '(none)'}")
        yield conn
    except pymysql.MySQLError as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"创建数据库连接时发生异常: {str(e)}")
        raise
    finally:
        if conn:
            try:
                conn.close()
                logger.info(f"数据库连接已关闭: {ip}:{port}/{database if database else '(none)'}")
            except Exception as e:
                logger.error(f"关闭数据库连接时发生错误: {str(e)}")

def success_response(data=None, message=None) -> SuccessResponse:
    """创建成功响应"""
    response = {'success': True}
    if message:
        response['message'] = message
    if data is not None:
        if isinstance(data, dict):
            response.update(data)
        else:
            response['data'] = data
    return jsonify(response)

def error_response(message, status_code=400) -> Response:
    """创建错误响应"""
    logger.error(f"错误响应: {message}, 状态码: {status_code}")
    return jsonify({
        'success': False,
        'message': message
    }), status_code

def execute_sql_fetch_all(cursor, sql, params=None):
    """执行SQL查询并返回所有结果"""
    try:
        logger.debug(f"执行SQL查询(fetchall): {sql}, 参数: {params}")
        cursor.execute(sql, params or {})
        results = cursor.fetchall()
        logger.debug(f"SQL查询结果行数: {len(results)}")
        return results
    except Exception as e:
        logger.error(f"SQL执行错误 (fetchall): {sql}, 参数: {params}, 错误: {str(e)}")
        return []

def execute_sql_fetch_one(cursor, sql, params=None):
    """执行SQL查询并返回一个结果"""
    try:
        logger.debug(f"执行SQL查询(fetchone): {sql}, 参数: {params}")
        cursor.execute(sql, params or {})
        result = cursor.fetchone()
        logger.debug(f"SQL查询结果: {result is not None}")
        return result
    except Exception as e:
        logger.error(f"SQL执行错误 (fetchone): {sql}, 参数: {params}, 错误: {str(e)}")
        return None

def count_large_table(cursor, table_name):
    """对大表进行分批计数，避免连接超时"""
    try:
        # 尝试获取主键或索引列
        primary_key_query = """
        SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = %s AND COLUMN_KEY = 'PRI' LIMIT 1
        """
        key_result = execute_sql_fetch_one(cursor, primary_key_query, (table_name,))
        key_column = key_result.get('COLUMN_NAME') if key_result else None
        
        if not key_column:
            # 没有主键，尝试使用索引列
            index_query = """
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = %s ORDER BY SEQ_IN_INDEX LIMIT 1
            """
            index_result = execute_sql_fetch_one(cursor, index_query, (table_name,))
            key_column = index_result.get('COLUMN_NAME') if index_result else None
            
        if not key_column:
            # 没有找到合适的列，退回到普通COUNT，但设置较短的超时时间
            logger.warning(f"表 {table_name} 没有找到主键或索引列，使用普通COUNT但可能超时")
            count_query = f"SELECT COUNT(*) as count FROM `{table_name}`"
            count_result = execute_sql_fetch_one(cursor, count_query)
            return int(count_result['count']) if count_result and 'count' in count_result else 0
        
        # 获取键范围
        min_query = f"SELECT MIN(`{key_column}`) as min_val FROM `{table_name}`"
        min_result = execute_sql_fetch_one(cursor, min_query)
        min_val = min_result.get('min_val') if min_result else 0
        
        max_query = f"SELECT MAX(`{key_column}`) as max_val FROM `{table_name}`"
        max_result = execute_sql_fetch_one(cursor, max_query)
        max_val = max_result.get('max_val') if max_result else 0
        
        if min_val is None or max_val is None:
            logger.warning(f"表 {table_name} 获取键范围失败，使用普通COUNT")
            count_query = f"SELECT COUNT(*) as count FROM `{table_name}`"
            count_result = execute_sql_fetch_one(cursor, count_query)
            return int(count_result['count']) if count_result and 'count' in count_result else 0
        
        # 分批计数
        total_count = 0
        batch_size = 50000000  # 每批5000万条
        logger.info(f"开始分批计数表 {table_name}，范围从 {min_val} 到 {max_val}，批次大小 {batch_size}")
        
        for start in range(int(min_val), int(max_val) + 1, batch_size):
            end = start + batch_size - 1
            query = f"SELECT COUNT(*) as count FROM `{table_name}` WHERE `{key_column}` BETWEEN {start} AND {end}"
            result = execute_sql_fetch_one(cursor, query)
            if result and 'count' in result:
                batch_count = int(result['count'])
                total_count += batch_count
                logger.info(f"表 {table_name} 批次 {start}-{end} 计数完成: {batch_count}")
        
        logger.info(f"表 {table_name} 分批计数完成，总行数: {total_count}")
        return total_count
    
    except Exception as e:
        logger.error(f"分批计数表 {table_name} 失败: {str(e)}")
        # 出现异常时尝试常规计数
        try:
            count_query = f"SELECT COUNT(*) as count FROM `{table_name}`"
            count_result = execute_sql_fetch_one(cursor, count_query)
            return int(count_result['count']) if count_result and 'count' in count_result else 0
        except:
            return 0

def get_table_stats(cursor, table_name, database_name=None):
    """获取表的记录数、注释和更新时间，优先使用缓存"""
    if not table_name:
        logger.warning("获取表信息失败: 表名为空")
        return 0, '', None
        
    try:
        # 首先检查表是否存在
        table_exists_query = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = %s"
        exists_result = execute_sql_fetch_one(cursor, table_exists_query, (table_name,))
        
        if not exists_result or exists_result.get('count', 0) == 0:
            logger.warning(f"表 {table_name} 不存在于当前数据库中")
            return 0, '', None

        # 获取表的信息，判断是否为大表
        table_status_query = f"SHOW TABLE STATUS WHERE Name = %s"
        table_status = execute_sql_fetch_one(cursor, table_status_query, (table_name,))
        estimated_rows = table_status.get('Rows', 0) if table_status else 0
        is_large_table = estimated_rows > 100000000  # 1亿行以上视为大表
        comment = table_status.get('Comment', '') if table_status else ''

        # 获取表的更新时间
        update_time = None
        try:
            update_time = table_status.get('Update_time') if table_status else None
            if not update_time:
                update_time_query = """
                SELECT UPDATE_TIME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
                """
                update_time_result = execute_sql_fetch_one(cursor, update_time_query, (table_name,))
                update_time = update_time_result.get('UPDATE_TIME') if update_time_result else None
            logger.debug(f"表 {table_name} 更新时间: {update_time}")
        except Exception as time_error:
            logger.error(f"获取表 {table_name} 更新时间失败: {str(time_error)}")
            update_time = None

        # 如果数据库名参数提供了，尝试从缓存中获取行数
        cached_row_count = None
        cached_update_time = None
        cached_at = None
        cache_valid = False
        
        if database_name:
            try:
                # 直接使用ansible_ui引擎，不依赖Flask应用上下文
                engine = get_ansible_ui_engine()
                
                with engine.connect() as conn:
                    # 不再检查缓存表是否存在，假设它已经创建好了
                    
                    # 查询缓存
                    cache_query = text("""
                    SELECT row_count, source_table_update_time, last_calculated_at
                    FROM table_row_count_cache
                    WHERE database_name = :db_name AND table_name = :table_name
                    """)
                    
                    cache_result = conn.execute(cache_query, {"db_name": database_name, "table_name": table_name}).fetchone()
                    
                    if cache_result:
                        cached_row_count = cache_result[0]
                        cached_update_time = cache_result[1]
                        cached_at = cache_result[2]
                        
                        # 检查缓存是否有效
                        
                        # 情况1: 两者都为NULL，缓存有效
                        if cached_update_time is None and update_time is None:
                            logger.debug(f"表 {table_name} 的缓存有效 (两者都为NULL)")
                            cache_valid = True
                        # 情况2: 两者不为NULL且相等，缓存有效
                        elif cached_update_time is not None and update_time is not None:
                            # 比较两个时间戳
                            if cached_update_time >= update_time:
                                logger.debug(f"表 {table_name} 的缓存有效 (缓存时间 >= 当前时间)")
                                cache_valid = True
                            else:
                                logger.debug(f"表 {table_name} 的缓存已过期 (缓存时间 < 当前时间)")
                        else:
                            logger.debug(f"表 {table_name} 的缓存状态已变更 (一个为NULL，另一个不为NULL)")
                        
                        if cache_valid:
                            logger.info(f"命中缓存! 表 {table_name} 的行数: {cached_row_count}, 缓存于: {cached_at}")
                            # 命中缓存后立即返回，不执行后续代码
                            return cached_row_count, comment, update_time
            except Exception as cache_error:
                logger.error(f"查询缓存表时出错: {str(cache_error)}")
                # 出错时继续使用原始方法计算
            
        # 获取表的行数（实时计算）
        count = 0
        try:
            if not is_large_table:
                # 普通表使用常规COUNT(*)
                count_query = f"SELECT COUNT(*) as count FROM `{table_name}`"
                count_result = execute_sql_fetch_one(cursor, count_query)
                count = int(count_result['count']) if count_result and 'count' in count_result else 0
                logger.debug(f"实时计算表 {table_name} 的行数: {count}")
            else:
                # 大表使用分批计数
                logger.info(f"表 {table_name} 是大表（估计行数: {estimated_rows}），使用分批计数方法")
                count = count_large_table(cursor, table_name)
                logger.info(f"分批计算表 {table_name} 的行数: {count}")
        except Exception as count_error:
            logger.error(f"获取表 {table_name} 记录数失败: {str(count_error)}")
            count = 0
        
        # 如果数据库名提供了，将结果更新到缓存表
        if database_name:
            try:
                # 使用ansible_ui数据库连接
                engine = get_ansible_ui_engine()
                
                # 使用事务确保写入成功
                with engine.begin() as conn:
                    # 更新或插入缓存记录
                    upsert_query = text("""
                    INSERT INTO table_row_count_cache 
                    (database_name, table_name, row_count, source_table_update_time)
                    VALUES (:db_name, :table_name, :row_count, :update_time)
                    ON DUPLICATE KEY UPDATE 
                    row_count = :row_count,
                    source_table_update_time = :update_time,
                    last_calculated_at = NOW()
                    """)
                    
                    result = conn.execute(upsert_query, {
                        "db_name": database_name,
                        "table_name": table_name,
                        "row_count": count,
                        "update_time": update_time
                    })
                    
                    if result.rowcount > 0:
                        logger.info(f"已成功更新表 {table_name} 的缓存记录, 行数: {count}")
                    else:
                        logger.warning(f"尝试更新表 {table_name} 的缓存记录，但未影响任何行")
            except Exception as cache_update_error:
                logger.error(f"更新缓存记录失败: {str(cache_update_error)}", exc_info=True)
        
        return count, comment, update_time
    except Exception as e:
        # 出现异常时返回0和空字符串，确保不影响主流程
        logger.error(f"获取表 {table_name} 信息失败 (全局): {str(e)}")
        return 0, '', None

def get_table_stats_worker(db_params: Dict[str, Any], table_name: str) -> Tuple[str, int, str, datetime]:
    """
    Worker函数，用于在单独的线程中获取单个表的统计信息。
    它会建立自己的数据库连接，并利用缓存机制避免重复计算。
    
    返回: (表名, 行数, 表注释, 表更新时间)
    """
    try:
        # logger.info(f"Worker starting for table: {table_name} in db: {db_params.get('database')}")
        with db_connection(
            db_params['ip'], 
            db_params['port'], 
            db_params['account'], 
            db_params['password'], 
            db_params['database']
        ) as conn:
            with conn.cursor() as cursor:
                # 调用get_table_stats，传递数据库名以启用缓存
                count, comment, update_time = get_table_stats(cursor, table_name, db_params['database'])
                # logger.info(f"Worker finished for table: {table_name}, count: {count}")
                return table_name, int(count) if count is not None else 0, comment, update_time
    except Exception as e:
        logger.error(f"线程中获取表 {table_name} 统计信息失败: {str(e)}", exc_info=True)
        return table_name, 0, f"错误: {str(e)}", None

def check_or_create_metadata_table(conn):
    """检查元数据表是否存在，不存在则创建"""
    if not conn.execute(text("SHOW TABLES LIKE 'data_center_ddl_metadata'")).fetchone():
        conn.execute(text("""
        CREATE TABLE `data_center_ddl_metadata` (
          `id` int NOT NULL AUTO_INCREMENT,
          `server_ip` varchar(100) NOT NULL COMMENT '服务器IP',
          `server_port` varchar(10) NOT NULL COMMENT '服务器端口',
          `server_account` varchar(100) NOT NULL COMMENT '服务器账号',
          `server_password` varchar(255) NOT NULL COMMENT '服务器密码',
          `database_name` varchar(100) DEFAULT NULL COMMENT '数据库名称',
          `table_name` varchar(100) DEFAULT NULL COMMENT '表名称',
          `table_comment` text DEFAULT NULL COMMENT '表注释',
          `table_structure` longtext DEFAULT NULL COMMENT '表结构JSON',
          `record_count` int DEFAULT 0 COMMENT '记录数量',
          `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `idx_server_database` (`server_ip`(50),`server_port`,`server_account`(50))
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据中台配置和表结构元数据';
        """))
        return True
    return False

def safe_get_param(data, key, default=None, convert_func=None):
    """安全获取参数并可选地进行类型转换
    
    Args:
        data: 请求数据字典
        key: 要获取的参数键名
        default: 如果参数不存在时的默认值
        convert_func: 可选的转换函数，如int, str等
        
    Returns:
        处理后的参数值
    """
    value = data.get(key, default)
    if value is not None and convert_func is not None:
        try:
            return convert_func(value)
        except (ValueError, TypeError):
            print(f"参数 {key} 值 '{value}' 转换失败")
            return default
    return value

# 数据中台视图函数
@data_center.route('/data_center')
def data_center_view():
    """数据中台页面视图"""
    return render_template('data_center.html')

# 测试数据库连接
@data_center.route('/api/data_center/test_connection', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'password'])
def test_connection():
    try:
        data = request.json
        
        with db_connection(data['ip'], data['port'], data['account'], data['password']) as conn:
            with conn.cursor() as cursor:
                databases = execute_sql_fetch_all(cursor, "SHOW DATABASES")
        
        return success_response(
            {'databases': [db['Database'] for db in databases]}, 
            message='连接成功'
        )
        
    except Exception as e:
        return error_response(f'连接失败: {str(e)}')

# 保存数据库配置
@data_center.route('/api/data_center/save_config', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'password'])
def save_config():
    try:
        data = request.json
        crypto_util = CryptoUtil()
        encrypted_password = crypto_util.encrypt(data['password'])
        
        try:
            from app import get_db_connection
            engine = get_db_connection()
            
            with engine.begin() as conn:  
                # 检查表是否存在，不存在则创建
                check_or_create_metadata_table(conn)
                
                # 检查是否已存在相同的配置
                params = {"ip": data['ip'], "port": data['port'], "account": data['account']}
                existing_config = conn.execute(
                    text("SELECT id FROM data_center_ddl_metadata WHERE server_ip = :ip AND server_port = :port AND server_account = :account LIMIT 1"),
                    params
                ).fetchone()
                
                if existing_config:
                    # 更新现有配置
                    conn.execute(
                        text("UPDATE data_center_ddl_metadata SET server_password = :password, updated_at = NOW() WHERE id = :id"),
                        {"password": encrypted_password, "id": existing_config[0]}
                    )
                else:
                    # 插入新配置
                    params["password"] = encrypted_password
                    conn.execute(
                        text("INSERT INTO data_center_ddl_metadata (server_ip, server_port, server_account, server_password) VALUES (:ip, :port, :account, :password)"),
                        params
                    )
            
            return success_response(message='配置保存成功')
            
        except Exception as e:
            return error_response(f'保存配置失败: {str(e)}')
            
    except Exception as e:
        return error_response(str(e))

# 获取数据库列表
@data_center.route('/api/data_center/get_databases', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'password'])
def get_databases():
    try:
        data = request.json
        
        with db_connection(data['ip'], data['port'], data['account'], data['password']) as conn:
            with conn.cursor() as cursor:
                databases = execute_sql_fetch_all(cursor, "SHOW DATABASES")
            
        return success_response({'databases': [db['Database'] for db in databases]})
            
    except Exception as e:
        return error_response(f'获取数据库列表失败: {str(e)}')

# 获取表列表
@data_center.route('/api/data_center/get_tables', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'password', 'database'])
def get_tables():
    try:
        data = request.json
        
        with db_connection(data['ip'], data['port'], data['account'], data['password'], data['database']) as conn:
            with conn.cursor() as cursor:
                tables_result = execute_sql_fetch_all(cursor, "SHOW TABLES")
                
                table_list = []
                for table in tables_result:
                    table_name = list(table.values())[0]
                    table_status = execute_sql_fetch_one(cursor, f"SHOW TABLE STATUS WHERE Name = %s", (table_name,))
                    comment = table_status.get('Comment', '') if table_status else ''
                    
                    table_list.append({
                        'name': table_name,
                        'comment': comment
                    })
                
        return success_response({'tables': table_list})
            
    except Exception as e:
        return error_response(f'获取表列表失败: {str(e)}')

# 获取表结构
@data_center.route('/api/data_center/get_table_info', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'password', 'database'])
def get_table_info():
    try:
        data = request.json
        
        with db_connection(data['ip'], data['port'], data['account'], data['password'], data['database']) as conn:
            with conn.cursor() as cursor:
                if 'table' in data and data['table'] and data['table'] != 'all':
                    # 获取指定表的结构和信息
                    structure = execute_sql_fetch_all(cursor, f"DESCRIBE `{data['table']}`")
                    count, comment, update_time = get_table_stats(cursor, data['table'], data['database'])
                
                    return success_response({
                        'data': {
                            'type': 'table',
                            'structure': structure,
                            'count': count,
                            'comment': comment,
                            'update_time': update_time.strftime('%Y-%m-%d %H:%M:%S') if update_time else None
                        }
                    })
                else:
                    # 获取所有表，但不计算记录数
                    tables_result = execute_sql_fetch_all(cursor, "SHOW TABLES")
                    table_column_name = f"Tables_in_{data['database']}"
                    tables = [table[table_column_name] for table in tables_result if table_column_name in table]
                
                    return success_response({
                        'data': {
                            'type': 'database',
                            'tables': tables,
                            'count': 0  # 不再计算总记录数
                        }
                    })
            
    except Exception as e:
        return error_response(f'获取表结构失败: {str(e)}')

# 获取单个表的记录数
@data_center.route('/api/data_center/get_table_count', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'password', 'database', 'table'])
def get_table_count():
    try:
        data = request.json
            
        # 确保表名不为空且不是'all'
        if not data['table'] or data['table'] == 'all':
            return error_response('请选择一个具体的表')
        
        # 先获取缓存信息
        cache_info = None
        try:
            # 使用ansible_ui数据库连接
            engine = get_ansible_ui_engine()
            
            with engine.connect() as conn:
                # 不再检查缓存表是否存在，假设它已经手动创建好了
                
                # 查询该表的缓存记录
                cache_query = text("""
                SELECT source_table_update_time, last_calculated_at, row_count
                FROM table_row_count_cache
                WHERE database_name = :db_name AND table_name = :table_name
                """)
                
                cache_result = conn.execute(cache_query, {
                    "db_name": data['database'], 
                    "table_name": data['table']
                }).fetchone()
                
                if cache_result:
                    cache_info = {
                        'cached_update_time': cache_result[0],
                        'last_calculated_at': cache_result[1],
                        'cached_row_count': cache_result[2]
                    }
        except Exception as e:
            logger.error(f"获取缓存信息失败: {str(e)}")
        
        # 获取当前表信息
        with db_connection(data['ip'], data['port'], data['account'], data['password'], data['database']) as conn:
            with conn.cursor() as cursor:
                count, comment, update_time = get_table_stats(cursor, data['table'], data['database'])
                # 确保count是整数
                count = int(count) if count is not None else 0
        
        # 判断缓存状态
        cache_status = "未缓存"
        cache_time = None
        
        if cache_info:
            cache_time = cache_info['last_calculated_at']
            
            # 判断缓存是否有效
            if cache_info['cached_update_time'] is None and update_time is None:
                cache_status = "有效缓存"
            elif cache_info['cached_update_time'] is not None and update_time is not None:
                if cache_info['cached_update_time'] >= update_time:
                    cache_status = "有效缓存"
                else:
                    cache_status = "已更新"
            else:
                cache_status = "状态变更"
        
        response_data = {
            'data': {
                'count': count,
                'comment': comment,
                'update_time': update_time.strftime('%Y-%m-%d %H:%M:%S') if update_time else None,
                'cache_status': cache_status,
                'cache_time': cache_time.strftime('%Y-%m-%d %H:%M:%S') if cache_time else None
            }
        }
        
        return success_response(response_data)
            
    except Exception as e:
        return error_response(f'获取表记录数失败: {str(e)}')

# 获取数据库中所有表的记录数
@data_center.route('/api/data_center/get_all_tables_count', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'password', 'database'])
def get_all_tables_count():
    try:
        data = request.json
        
        db_params_dict = {
            'ip': safe_get_param(data, 'ip', '', str),
            'port': safe_get_param(data, 'port', 3306, int),
            'account': safe_get_param(data, 'account', '', str),
            'password': safe_get_param(data, 'password', '', str),
            'database': safe_get_param(data, 'database', '', str)
        }

        if not all([db_params_dict['ip'], db_params_dict['account'], db_params_dict['password'], db_params_dict['database']]):
            logger.warning(f"参数不完整或无效: {db_params_dict}")
            return error_response('参数不完整或无效')

        # 日志记录开始计算
        logger.info(f"开始获取数据库 '{db_params_dict['database']}' 的表统计信息 (启用缓存机制)")

        # 获取每个表的缓存时间信息
        cache_info = {}
        try:
            # 使用ansible_ui数据库连接
            engine = get_ansible_ui_engine()
            
            # 检查是否正确连接到ansible_ui数据库
            try:
                with engine.connect() as conn:
                    db_name = conn.execute(text("SELECT DATABASE() as db")).fetchone()
                    logger.info(f"当前连接的数据库是: {db_name[0] if db_name else 'Unknown'}")
            except Exception as db_error:
                logger.error(f"获取当前数据库名称失败: {str(db_error)}")
            
            with engine.connect() as conn:
                # 不再检查缓存表是否存在，假设它已手动创建好了
                
                # 查询所有该数据库表的缓存信息
                cache_query = text("""
                SELECT table_name, source_table_update_time, last_calculated_at
                FROM table_row_count_cache
                WHERE database_name = :db_name
                """)
                
                cache_results = conn.execute(cache_query, {"db_name": db_params_dict['database']}).fetchall()
                
                for row in cache_results:
                    cache_info[row[0]] = {
                        'cached_update_time': row[1],
                        'last_calculated_at': row[2]
                    }
                logger.info(f"从缓存中读取到 {len(cache_info)} 条记录")
        except Exception as e:
            logger.error(f"获取缓存信息失败: {str(e)}", exc_info=True)

        table_names = []
        try:
            # 首先，使用单个连接获取所有表名
            with db_connection(
                db_params_dict['ip'], 
                db_params_dict['port'], 
                db_params_dict['account'], 
                db_params_dict['password'], 
                db_params_dict['database']
            ) as conn:
                with conn.cursor() as cursor:
                    tables_result = execute_sql_fetch_all(cursor, "SHOW TABLES")
                    table_column_name = f"Tables_in_{db_params_dict['database']}"
                    for table_row in tables_result:
                        if table_column_name in table_row:
                            table_name = table_row[table_column_name]
                            if table_name:
                                table_names.append(table_name)
            logger.info(f"在数据库 '{db_params_dict['database']}' 中找到 {len(table_names)} 个表")
        except Exception as e:
            logger.error(f"为并发计数列出表失败: {str(e)}", exc_info=True)
            return error_response(f"获取表列表失败: {str(e)}")

        if not table_names:
            return success_response({
                'data': {
                    'tables': [],
                    'total_count': 0
                }
            }, message="数据库中没有找到表")

        tables_info = []
        total_count = 0
        cache_hits = 0
        
        # 使用ThreadPoolExecutor并发获取计数
        logger.info(f"开始对 {len(table_names)} 个表进行并发计数 (使用缓存机制)...")
        start_time = datetime.now()
        
        with ThreadPoolExecutor(max_workers=None) as executor:
            future_to_table_name = {
                executor.submit(get_table_stats_worker, db_params_dict, name): name for name in table_names
            }
            
            for future in as_completed(future_to_table_name):
                original_table_name = future_to_table_name[future]
                try:
                    # 返回结果是 (table_name, count, comment, update_time)
                    processed_table_name, count, comment, update_time = future.result()
                    
                    # 确保返回的名称与预期一致 (主要用于调试或复杂场景)
                    if original_table_name != processed_table_name:
                        logger.warning(f"线程返回的表名 ({processed_table_name}) 与预期 ({original_table_name}) 不符。")
                    
                    # 判断是否使用了缓存
                    cache_status = "未缓存"
                    last_calculated_at = None
                    
                    # 计算缓存状态
                    if processed_table_name in cache_info:
                        info = cache_info[processed_table_name]
                        last_calculated_at = info['last_calculated_at']
                        
                        # 缓存时间与表更新时间比较
                        if info['cached_update_time'] is None and update_time is None:
                            cache_status = "有效缓存"
                            cache_hits += 1
                        elif info['cached_update_time'] is not None and update_time is not None:
                            if info['cached_update_time'] >= update_time:
                                cache_status = "有效缓存"
                                cache_hits += 1
                            else:
                                cache_status = "已更新"
                        else:
                            cache_status = "状态变更"
                    
                    total_count += count
                    table_info = {
                        'name': processed_table_name,
                        'comment': comment,
                        'count': count,
                        'update_time': update_time.strftime('%Y-%m-%d %H:%M:%S') if update_time else None,
                        'cache_status': cache_status,
                        'cache_time': last_calculated_at.strftime('%Y-%m-%d %H:%M:%S') if last_calculated_at else None
                    }
                    tables_info.append(table_info)
                except Exception as exc:
                    # 这个异常通常由 future.result() 抛出，如果worker内部有未捕获的异常
                    logger.error(f"处理表 {original_table_name} 的future时发生异常: {exc}", exc_info=True)
                    tables_info.append({
                        'name': original_table_name,
                        'comment': f"并发处理错误: {str(exc)}",
                        'count': 0,
                        'update_time': None,
                        'cache_status': '处理错误',
                        'cache_time': None
                    })
        
        # 计算执行时间
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 可选: 对结果按表名排序，以便输出顺序一致
        tables_info.sort(key=lambda x: x['name'])
        
        # 记录缓存使用情况
        logger.info(f"所有表计数完成，总记录数: {total_count}，执行时间: {execution_time:.2f}秒，缓存命中: {cache_hits}/{len(table_names)} 表")

        return success_response({
            'data': {
                'tables': tables_info,
                'total_count': total_count,
                'execution_time': f"{execution_time:.2f}秒",
                'cache_hits': cache_hits,
                'total_tables': len(table_names)
            }
        })
            
    except Exception as e:
        logger.error(f"获取所有表记录数失败 (最外层捕获): {str(e)}", exc_info=True)
        return error_response(f'获取所有表记录数失败: {str(e)}')

# 获取保存的数据库配置
@data_center.route('/api/data_center/get_saved_configs', methods=['GET'])
def get_saved_configs():
    try:
        from app import get_db_connection
        engine = get_db_connection()
        
        with engine.connect() as conn:
            # 检查表是否存在
            if not conn.execute(text("SHOW TABLES LIKE 'data_center_ddl_metadata'")).fetchone():
                return success_response({'configs': []})
            
            # 获取所有配置
            result = conn.execute(
                text("""
                SELECT 
                id, server_ip, server_port, server_account, server_password, database_name,
                    DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i:%s') as updated_at
                FROM data_center_ddl_metadata 
                GROUP BY server_ip, server_port, server_account, server_password, id, database_name, updated_at
                ORDER BY updated_at DESC
                """)
            )
            
            crypto_util = CryptoUtil()
            configs = [{
                'id': str(row._mapping['id']),
                'server_ip': str(row._mapping['server_ip']),
                'server_port': str(row._mapping['server_port']),
                'server_account': str(row._mapping['server_account']),
                'server_password': str(crypto_util.decrypt(row._mapping['server_password'])),
                'database_name': str(row._mapping['database_name'] or ''),
                'updated_at': str(row._mapping['updated_at'])
            } for row in result]
            
        return success_response({'configs': configs})
            
    except Exception as e:
        return error_response(f'获取配置失败: {str(e)}')

# 更新数据库配置中的数据库名称
@data_center.route('/api/data_center/update_database_name', methods=['POST'])
@validate_params(['ip', 'port', 'account', 'database'])
def update_database_name():
    try:
        data = request.json
        
        from app import get_db_connection
        engine = get_db_connection()
        
        with engine.begin() as conn:
            # 检查表是否存在
            if not conn.execute(text("SHOW TABLES LIKE 'data_center_ddl_metadata'")).fetchone():
                return error_response('配置表不存在')
            
            # 查找匹配的配置
            params = {"ip": data['ip'], "port": str(data['port']), "account": data['account']}
            
            update_stmt = text("""
                UPDATE data_center_ddl_metadata 
                SET database_name = :database, updated_at = NOW() 
                WHERE server_ip = :ip AND server_port = :port AND server_account = :account
            """)
            update_params = {
                "database": data['database'],
                "ip": data['ip'],
                "port": str(data['port']),
                "account": data['account']
            }
            
            conn.execute(update_stmt, update_params)
            
        return success_response(message='数据库名称已更新')
            
    except Exception as e:
        return error_response(f'更新数据库名称失败: {str(e)}')

# 获取行数缓存统计信息
@data_center.route('/api/data_center/cache_stats', methods=['GET'])
def get_cache_stats():
    """获取表行数缓存的统计信息"""
    try:
        # 使用ansible_ui数据库连接
        engine = get_ansible_ui_engine()
        
        # 不再检查或创建缓存表，假设它已经手动创建好了
        
        with engine.connect() as conn:
            # 获取总缓存记录数
            total_query = text("SELECT COUNT(*) as count FROM table_row_count_cache")
            total_result = conn.execute(total_query).fetchone()
            total_count = total_result[0] if total_result else 0
            
            # 如果总记录为0，直接返回
            if total_count == 0:
                return success_response({
                    'total_cached_tables': 0,
                    'updated_last_24h': 0,
                    'database_distribution': [],
                    'oldest_cache': None,
                    'newest_cache': None,
                    'estimated_space': "0 B"
                }, message="缓存表中尚无记录")
            
            # 获取最近24小时更新的记录数
            recent_query = text("SELECT COUNT(*) as count FROM table_row_count_cache WHERE last_calculated_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)")
            recent_result = conn.execute(recent_query).fetchone()
            recent_count = recent_result[0] if recent_result else 0
            
            # 获取数据库分布统计
            db_stats_query = text("""
            SELECT database_name, COUNT(*) as table_count 
            FROM table_row_count_cache 
            GROUP BY database_name 
            ORDER BY table_count DESC
            """)
            db_stats_result = conn.execute(db_stats_query).fetchall()
            db_stats = [{
                'database_name': row[0],
                'table_count': row[1]
            } for row in db_stats_result]
            
            # 获取最旧和最新的缓存记录时间
            time_query = text("""
            SELECT 
                MIN(last_calculated_at) as oldest, 
                MAX(last_calculated_at) as newest 
            FROM table_row_count_cache
            """)
            time_result = conn.execute(time_query).fetchone()
            oldest = time_result[0] if time_result and time_result[0] else None
            newest = time_result[1] if time_result and time_result[1] else None
            
            # 获取占用空间（估算）
            space_query = text("""
            SELECT 
                data_length + index_length as total_size 
            FROM information_schema.tables
            WHERE table_schema = DATABASE() 
            AND table_name = 'table_row_count_cache'
            """)
            space_result = conn.execute(space_query).fetchone()
            space_bytes = space_result[0] if space_result else 0
            
            # 转换为可读格式
            if space_bytes < 1024:
                space_usage = f"{space_bytes} B"
            elif space_bytes < 1024 * 1024:
                space_usage = f"{space_bytes/1024:.2f} KB"
            else:
                space_usage = f"{space_bytes/(1024*1024):.2f} MB"
            
            return success_response({
                'total_cached_tables': total_count,
                'updated_last_24h': recent_count,
                'database_distribution': db_stats,
                'oldest_cache': oldest.strftime('%Y-%m-%d %H:%M:%S') if oldest else None,
                'newest_cache': newest.strftime('%Y-%m-%d %H:%M:%S') if newest else None,
                'estimated_space': space_usage
            })
    
    except Exception as e:
        logger.error(f"获取缓存统计信息失败: {str(e)}", exc_info=True)
        return error_response(f'获取缓存统计信息失败: {str(e)}')

# 清理表行数缓存
@data_center.route('/api/data_center/clear_cache', methods=['POST'])
def clear_cache():
    """清理表行数缓存"""
    try:
        data = request.json or {}
        database = data.get('database', None)  # 可选参数，指定清理特定数据库的缓存
        older_than_days = data.get('older_than_days', None)  # 可选参数，清理特定天数以前的缓存
        
        # 使用ansible_ui数据库连接
        engine = get_ansible_ui_engine()
        
        with engine.connect() as conn:
            # 检查表是否存在
            if not conn.execute(text("SHOW TABLES LIKE 'table_row_count_cache'")).fetchone():
                return success_response(message='缓存表不存在，无需清理')
            
        # 使用事务进行清理操作
        with engine.begin() as conn:
            # 构建删除语句
            if database and older_than_days:
                # 删除特定数据库中超过指定天数的记录
                del_query = text("""
                DELETE FROM table_row_count_cache 
                WHERE database_name = :database 
                AND last_calculated_at < DATE_SUB(NOW(), INTERVAL :days DAY)
                """)
                result = conn.execute(del_query, {"database": database, "days": older_than_days})
                affected_rows = result.rowcount
                logger.info(f"已清理数据库 {database} 中 {older_than_days} 天前的缓存记录，影响 {affected_rows} 行")
                
            elif database:
                # 删除特定数据库的所有记录
                del_query = text("DELETE FROM table_row_count_cache WHERE database_name = :database")
                result = conn.execute(del_query, {"database": database})
                affected_rows = result.rowcount
                logger.info(f"已清理数据库 {database} 的所有缓存记录，影响 {affected_rows} 行")
                
            elif older_than_days:
                # 删除所有超过指定天数的记录
                del_query = text("DELETE FROM table_row_count_cache WHERE last_calculated_at < DATE_SUB(NOW(), INTERVAL :days DAY)")
                result = conn.execute(del_query, {"days": older_than_days})
                affected_rows = result.rowcount
                logger.info(f"已清理所有 {older_than_days} 天前的缓存记录，影响 {affected_rows} 行")
                
            else:
                # 清空整个表
                result = conn.execute(text("TRUNCATE TABLE table_row_count_cache"))
                logger.info("已清空所有缓存记录")
            
            # 事务会自动提交
        
        return success_response(message='缓存已成功清理')
            
    except Exception as e:
        logger.error(f"清理缓存失败: {str(e)}", exc_info=True)
        return error_response(f'清理缓存失败: {str(e)}')

# 不再进行自动初始化，因为表已手动创建好
logger.info("表行数缓存已手动创建，跳过自动初始化步骤")
