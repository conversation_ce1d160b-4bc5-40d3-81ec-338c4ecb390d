# -*- coding: utf-8 -*-
"""
MySQL数据库操作日志审计系统配置文件
"""

# MySQL 审计模块专用配置

# 从全局配置导入数据库配置
# 注意：确保 db_config.py 在 Python 路径中，或者调整导入
# 如果 db_config.py 也在根目录，此导入可能需要调整，取决于运行方式
# 假设可以直接导入
try:
    from db_config import DB_CONFIG as MYSQL_AUDIT_DB_CONFIG
except ImportError:
    print("警告：无法从根目录的 db_config.py 导入数据库配置。请确保 PYTHONPATH 设置正确或调整导入路径。")
    # 提供一个备用或默认配置，或者让应用在缺少配置时失败
    MYSQL_AUDIT_DB_CONFIG = {
        'host': '127.0.0.1', 
        'port': 3306, 
        'user': 'your_db_user', 
        'password': 'your_db_password', 
        'database': 'mysql_audit_db' # 示例数据库名
    }

# MySQL审计模块数据库配置
DB_CONFIG = MYSQL_AUDIT_DB_CONFIG

# MySQL审计模块应用配置
APP_CONFIG = {
    'default_days': 3,  # 默认显示最近3天的数据
    
    # 默认风险操作定义
    'RISK_OPERATIONS': {
        'High': [
            {'type': 'DDL'},
            {'type': 'DCL'},
            {'type': 'DELETE'}
        ],
        'Medium': [
            {'type': 'UPDATE'}
        ],
        'Low': [
            {'type': 'SELECT'},
            {'type': 'INSERT'},
            {'type': 'SHOW'}
        ]
    },
    
    # 默认写入风险级别
    'WRITE_RISK_LEVELS': ['High', 'Medium']
}
