#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单启动脚本
"""

import os

# 设置基本环境变量
os.environ['DB_PASSWORD'] = '123456'
os.environ['SECRET_KEY'] = 'dev_secret_key_for_development_only_32chars'
os.environ['EDIT_PASSWORD_HASH'] = 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'
os.environ['JWT_SECRET'] = 'dev_jwt_secret_for_development_only_32chars'

print("启动应用...")

# 启动应用
from app import app
app.run(host='0.0.0.0', port=5100, debug=True)
