#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单启动脚本 - 自动加载 .env 文件
"""

import os

def load_env_file():
    """加载 .env 文件"""
    if os.path.exists('.env'):
        print("📁 加载 .env 配置文件...")
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print("✅ .env 文件加载完成")
    else:
        print("⚠️  .env 文件不存在，使用默认配置")
        # 设置默认环境变量
        os.environ['DB_PASSWORD'] = '123456'
        os.environ['SECRET_KEY'] = 'dev_secret_key_for_development_only_32chars'

print("🚀 启动应用...")
load_env_file()

# 验证关键配置是否已加载
required_vars = ['DB_PASSWORD', 'SECRET_KEY']
missing_vars = [var for var in required_vars if not os.environ.get(var)]
if missing_vars:
    print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
    print("请检查 .env 文件是否包含所有必需的配置")
    exit(1)

print("✅ 配置验证通过")

# 启动应用
from app import app
app.run(host='0.0.0.0', port=5100, debug=True)
