#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速启动脚本
自动设置环境变量并启动应用
"""

import os
import sys

def set_environment():
    """设置开发环境变量"""
    env_vars = {
        'DB_PASSWORD': '123456',
        'SECRET_KEY': 'dev_secret_key_for_development_only_32chars',
        'DATABASE_HOST': '**************',
        'DATABASE_PORT': '3310',
        'DATABASE_USER': 'root',
        'DATABASE_MAIN': 'excel',
        'DATABASE_MYSQL_LOG': 'mysql_log',
        'DATABASE_ANSIBLE': 'ansible_ui',
        'ANSIBLE_HOST': '***********',
        'JUMP_HOST': '************',
        'JUMP_PORT': '6233',
        'ANSIBLE_PORT': '22'
    }
    
    print("🚀 设置开发环境变量...")
    for key, value in env_vars.items():
        os.environ[key] = value
        if 'PASSWORD' in key:
            print(f"   {key}: ***")
        else:
            print(f"   {key}: {value}")
    
    print("\n✅ 环境变量设置完成")
    print("⚠️  注意: 这些是开发环境的默认配置")

def main():
    """主函数"""
    try:
        # 设置环境变量
        set_environment()
        
        print("\n🔧 启动应用...")
        
        # 导入并启动应用
        from app import app
        app.run(host='0.0.0.0', port=5100, debug=True)
        
    except ImportError as e:
        print(f"\n❌ 导入错误: {str(e)}")
        print("请确保所有依赖已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
