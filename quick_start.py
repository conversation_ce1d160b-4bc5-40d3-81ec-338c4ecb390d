#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速启动脚本
自动加载环境变量并启动应用
"""

import os
import sys

def load_env_file(env_file='.env'):
    """加载 .env 文件"""
    if not os.path.exists(env_file):
        print(f"⚠️  {env_file} 文件不存在")
        return False

    print(f"📁 加载环境变量文件: {env_file}")
    loaded_count = 0

    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()

                # 跳过空行和注释
                if not line or line.startswith('#'):
                    continue

                # 解析环境变量
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()

                    # 设置环境变量
                    os.environ[key] = value
                    loaded_count += 1

                    # 显示加载的变量（隐藏敏感信息）
                    if any(sensitive in key.upper() for sensitive in ['PASSWORD', 'SECRET', 'KEY', 'HASH']):
                        print(f"   {key}: ***")
                    else:
                        print(f"   {key}: {value}")

        print(f"✅ 成功加载 {loaded_count} 个环境变量")
        return True

    except Exception as e:
        print(f"❌ 加载环境变量文件失败: {str(e)}")
        return False

def set_fallback_environment():
    """设置备用环境变量"""
    fallback_vars = {
        'DB_PASSWORD': '123456',
        'SECRET_KEY': 'dev_secret_key_for_development_only_32chars',
        'DATABASE_HOST': '**************',
        'DATABASE_PORT': '3310',
        'DATABASE_USER': 'root',
        'DATABASE_MAIN': 'excel',
        'DATABASE_MYSQL_LOG': 'mysql_log',
        'DATABASE_ANSIBLE': 'ansible_ui',
        'ANSIBLE_HOST': '***********',
        'JUMP_HOST': '************',
        'JUMP_PORT': '6233',
        'ANSIBLE_PORT': '22'
    }

    print("🔄 使用备用环境变量...")
    for key, value in fallback_vars.items():
        if key not in os.environ:
            os.environ[key] = value
            if 'PASSWORD' in key:
                print(f"   {key}: ***")
            else:
                print(f"   {key}: {value}")

    print("⚠️  注意: 使用的是开发环境默认配置")

def main():
    """主函数"""
    try:
        print("🚀 启动应用...")

        # 尝试加载 .env 文件
        if not load_env_file('.env'):
            # 如果加载失败，使用备用配置
            set_fallback_environment()

        print("\n🔧 启动Flask应用...")

        # 导入并启动应用
        from app import app
        app.run(host='0.0.0.0', port=5100, debug=True)

    except ImportError as e:
        print(f"\n❌ 导入错误: {str(e)}")
        print("请确保所有依赖已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
