# 配置文件结构说明

## 配置文件分工

### `config.ini` - 主要配置文件
**用途：** 存储非敏感的系统配置
**内容：**
- 数据库连接信息（主机、端口、用户名、数据库名）
- 服务器配置（端口、调试模式）
- Ansible配置（主机地址、端口）
- 其他系统设置

### `.env` - 敏感信息配置文件
**用途：** 存储敏感信息（密码、密钥）
**内容：**
- `DB_PASSWORD` - 数据库密码
- `SECRET_KEY` - Flask会话密钥
- `EDIT_PASSWORD_HASH` - 编辑模式密码哈希
- `JWT_SECRET` - JWT认证密钥

### `.env.example` - 敏感信息模板
**用途：** 提供 `.env` 文件的模板
**注意：** 不包含真实密码，可以安全地提交到版本控制

## 配置优先级

1. **环境变量** - 最高优先级
2. **`.env` 文件** - 敏感信息
3. **`config.ini` 文件** - 默认配置
4. **代码中的默认值** - 最低优先级

## 使用方式

### 开发环境
1. 复制 `.env.example` 为 `.env`
2. 在 `.env` 中填入您的复杂密码
3. `config.ini` 保持默认配置即可

### 生产环境
1. 设置系统环境变量（推荐）
2. 或者在 `.env` 中设置生产环境的密码
3. 根据需要调整 `config.ini` 中的配置

## 安全建议

- ✅ `.env.example` 可以提交到版本控制
- ❌ `.env` 不要提交到版本控制
- ✅ `config.ini` 可以提交到版本控制（不含敏感信息）
- 🔄 定期更换 `.env` 中的密码和密钥

## 当前配置状态

您的系统现在使用：
- **数据库密码**: 123456（开发环境）
- **会话密钥**: 复杂密钥（已设置）
- **JWT密钥**: 复杂密钥（已设置）
- **编辑密码**: AdminEdit@2024!（对应的哈希已设置）

系统可以正常启动：`python start.py`
