# 配置文件结构说明

## 🎯 统一配置管理

现在所有配置都统一在 `.env` 文件中管理，便于维护和更改。

### 📁 配置文件说明

**`.env`** - 唯一配置文件
- 数据库配置（主机、端口、用户、密码、数据库名）
- 安全密钥（Flask密钥、JWT密钥、编辑密码哈希）
- Ansible配置（主机地址、端口）

**`.env.example`** - 配置模板
- 提供配置示例
- 可以安全提交到版本控制

**`config.ini`** - 简化配置
- 只保留少量非敏感设置
- 主要配置已迁移到 `.env`

## 🔧 使用方式

### 开发环境
```bash
# 1. 复制模板（如果需要）
cp .env.example .env

# 2. 编辑 .env 文件，填入您的配置
# 3. 启动应用
python start.py
```

### 生产环境
1. 在 `.env` 文件中设置生产环境的密码和配置
2. 或者设置系统环境变量

## 📋 当前 .env 配置内容

```
# 数据库配置
DB_HOST=**************
DB_PORT=3310
DB_USER=root
DB_PASSWORD=123456

# 安全密钥配置
SECRET_KEY=您的复杂密钥
JWT_SECRET=您的JWT密钥
EDIT_PASSWORD_HASH=编辑密码哈希

# Ansible配置
ANSIBLE_HOST=***********
JUMP_HOST=************
...
```

## ✅ 优势

1. **统一管理**: 所有配置在一个文件中
2. **易于维护**: 修改密码只需编辑一个文件
3. **安全性**: 敏感信息不会分散在多个文件中
4. **简洁性**: 避免配置重复和冲突

## 🚀 启动测试

```bash
python start.py
```

现在您只需要维护 `.env` 文件即可！
