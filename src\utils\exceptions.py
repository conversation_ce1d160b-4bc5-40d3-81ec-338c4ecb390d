# -*- coding: utf-8 -*-
"""
自定义异常类
定义系统中使用的各种异常类型
"""

class BaseAppException(Exception):
    """应用基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details
        }

class ValidationError(BaseAppException):
    """数据验证错误"""
    pass

class DatabaseError(BaseAppException):
    """数据库操作错误"""
    pass

class AuthenticationError(BaseAppException):
    """认证错误"""
    pass

class AuthorizationError(BaseAppException):
    """授权错误"""
    pass

class ConfigurationError(BaseAppException):
    """配置错误"""
    pass

class FileProcessingError(BaseAppException):
    """文件处理错误"""
    pass

class ExternalServiceError(BaseAppException):
    """外部服务错误"""
    pass

class BusinessLogicError(BaseAppException):
    """业务逻辑错误"""
    pass

class RateLimitError(BaseAppException):
    """频率限制错误"""
    pass

class SecurityError(BaseAppException):
    """安全相关错误"""
    pass
