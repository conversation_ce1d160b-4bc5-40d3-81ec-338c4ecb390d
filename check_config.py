#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置检查工具
检查系统配置是否正确
"""

import os
import sys

def check_database_config():
    """检查数据库配置"""
    print("📊 检查数据库配置...")

    try:
        # 直接检查环境变量和默认值
        db_host = os.environ.get('DATABASE_HOST', '**************')
        db_port = os.environ.get('DATABASE_PORT', '3310')
        db_user = os.environ.get('DATABASE_USER', 'root')
        db_password = os.environ.get('DB_PASSWORD', '123456')
        db_main = os.environ.get('DATABASE_MAIN', 'excel')
        db_mysql_log = os.environ.get('DATABASE_MYSQL_LOG', 'mysql_log')
        db_ansible = os.environ.get('DATABASE_ANSIBLE', 'ansible_ui')

        print(f"   主机: {db_host}")
        print(f"   端口: {db_port}")
        print(f"   用户: {db_user}")
        print(f"   密码: {'已设置' if db_password else '未设置'}")
        print(f"   主数据库: {db_main}")
        print(f"   审计数据库: {db_mysql_log}")
        print(f"   Ansible数据库: {db_ansible}")

        if not db_password:
            print("   ⚠️  警告: 数据库密码未设置")
            return False
        else:
            print("   ✅ 数据库配置正常")
            return True

    except Exception as e:
        print(f"   ❌ 数据库配置检查失败: {str(e)}")
        return False

def check_security_config():
    """检查安全配置"""
    print("\n🔒 检查安全配置...")

    try:
        secret_key = os.environ.get('SECRET_KEY', 'dev_secret_key_for_development_only_32chars')
        edit_password_hash = os.environ.get('EDIT_PASSWORD_HASH', '')
        jwt_secret = os.environ.get('JWT_SECRET', '')

        print(f"   会话密钥: {'已设置' if secret_key else '未设置'}")
        print(f"   编辑密码哈希: {'已设置' if edit_password_hash else '未设置'}")
        print(f"   JWT密钥: {'已设置' if jwt_secret else '未设置'}")

        issues = []
        if len(secret_key) < 32:
            issues.append("会话密钥长度不足32字符")

        if not edit_password_hash:
            issues.append("编辑密码哈希未设置")

        if issues:
            for issue in issues:
                print(f"   ⚠️  警告: {issue}")
            return False
        else:
            print("   ✅ 安全配置正常")
            return True

    except Exception as e:
        print(f"   ❌ 安全配置检查失败: {str(e)}")
        return False

def check_ansible_config():
    """检查Ansible配置"""
    print("\n🤖 检查Ansible配置...")

    try:
        ansible_host = os.environ.get('ANSIBLE_HOST', '***********')
        jump_host = os.environ.get('JUMP_HOST', '************')
        jump_port = os.environ.get('JUMP_PORT', '6233')
        ansible_port = os.environ.get('ANSIBLE_PORT', '22')

        print(f"   Ansible主机: {ansible_host}")
        print(f"   跳板机: {jump_host}")
        print(f"   跳板机端口: {jump_port}")
        print(f"   Ansible端口: {ansible_port}")

        print("   ✅ Ansible配置正常")
        return True

    except Exception as e:
        print(f"   ❌ Ansible配置检查失败: {str(e)}")
        return False

def check_environment_variables():
    """检查环境变量"""
    print("\n🌍 检查环境变量...")
    
    important_vars = [
        'DB_PASSWORD',
        'SECRET_KEY',
        'EDIT_PASSWORD_HASH',
        'JWT_SECRET'
    ]
    
    set_vars = []
    missing_vars = []
    
    for var in important_vars:
        if os.environ.get(var):
            set_vars.append(var)
            print(f"   ✅ {var}: 已设置")
        else:
            missing_vars.append(var)
            print(f"   ⚠️  {var}: 未设置")
    
    if missing_vars:
        print(f"\n   📝 建议设置以下环境变量: {', '.join(missing_vars)}")
        return False
    else:
        print("   ✅ 所有重要环境变量已设置")
        return True

def test_database_connection():
    """测试数据库连接"""
    print("\n🔌 测试数据库连接...")
    
    try:
        from db_config import DB_CONFIG, MAIN_DB_URI, ANSIBLE_DB_URI
        import pymysql
        
        # 测试主数据库连接
        print("   测试主数据库连接...")
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database']
        )
        connection.close()
        print("   ✅ 主数据库连接成功")
        
        # 检查连接字符串
        if MAIN_DB_URI and ANSIBLE_DB_URI:
            print("   ✅ 数据库连接字符串构建成功")
            return True
        else:
            print("   ❌ 数据库连接字符串构建失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 系统配置检查工具")
    print("=" * 50)
    
    checks = [
        check_environment_variables,
        check_database_config,
        check_security_config,
        check_ansible_config,
        test_database_connection
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
        except Exception as e:
            print(f"   ❌ 检查过程中出错: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📋 检查结果汇总:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"   ✅ 所有检查通过 ({passed}/{total})")
        print("   🚀 系统配置正常，可以启动应用")
        return True
    else:
        print(f"   ⚠️  部分检查未通过 ({passed}/{total})")
        print("   🔧 请根据上述提示修复配置问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
