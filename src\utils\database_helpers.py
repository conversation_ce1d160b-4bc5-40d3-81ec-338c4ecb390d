from sqlalchemy import create_engine, text, inspect
from sqlalchemy.pool import QueuePool
from datetime import datetime
import time
# Attempt to import db_config, assuming it's in the project root or accessible via PYTHONPATH
# If this causes an issue, we might need to adjust how DB_CONFIG is accessed,
# e.g., by passing it as an argument or using Flask's app.config
try:
    from db_config import MAIN_DB_URI, DB_SCHEMA_MAIN
except ImportError:
    # Fallback or placeholder if db_config is not found directly
    # This will likely cause runtime errors if not configured correctly
    # Consider using Flask app config for these values
    MAIN_DB_URI = None 
    DB_SCHEMA_MAIN = None
    print("Warning: db_config.py not found or MAIN_DB_URI/DB_SCHEMA_MAIN not defined. Database functions may fail.")


_engine = None

def get_db_connection():
    """获取数据库连接池"""
    global _engine
    
    if MAIN_DB_URI is None:
        raise ValueError("MAIN_DB_URI is not configured. Please check db_config.py or Flask app configuration.")

    if _engine is None:
        _engine = create_engine(
            MAIN_DB_URI,
            poolclass=QueuePool,
            pool_size=10,
            max_overflow=20,
            pool_timeout=30,
            pool_recycle=1800  # 30分钟重新连接
        )
    return _engine

def get_table_display_name(table_name):
    """获取表的显示名称"""
    # 移除excel_data_前缀
    display_name = table_name.replace('excel_data_', '')
    # 将下划线替换为空格
    return display_name.replace('_', ' ')

def get_all_tables(engine):
    """获取所有非metadata的数据表"""
    inspector = inspect(engine)
    tables = inspector.get_table_names()
    # 过滤掉metadata表
    return sorted([table for table in tables if not table.endswith('_metadata')])

def execute_query(query, engine, params=None):
    """执行SQL查询并返回结果"""
    try:
        with engine.connect() as conn:
            # 确保query是SQLAlchemy text对象
            if isinstance(query, str):
                query = text(query)
                
            if params:
                result = conn.execute(query, params)
            else:
                result = conn.execute(query)
            
            # 获取列名
            columns = result.keys()
            # 获取所有行数据
            rows = result.fetchall()
            
            return columns, rows
    except Exception as e:
        print(f"Query execution error: {str(e)}")
        raise  # 重新抛出异常，让调用者处理

def get_table_comments(engine):
    """获取所有表的注释"""
    if DB_SCHEMA_MAIN is None:
        print("Warning: DB_SCHEMA_MAIN is not configured. Cannot get table comments.")
        return {}

    query = text(f"""
    SELECT TABLE_NAME, TABLE_COMMENT
    FROM information_schema.TABLES
    WHERE TABLE_SCHEMA = '{DB_SCHEMA_MAIN}'
    """)
    
    try:
        with engine.connect() as conn:
            result = conn.execute(query)
            return {row[0]: row[1] for row in result}
    except Exception as e:
        print(f"Error in get_table_comments: {str(e)}")
        return {}

def get_column_comments(table_name, engine):
    """获取表的列注释"""
    if DB_SCHEMA_MAIN is None:
        print("Warning: DB_SCHEMA_MAIN is not configured. Cannot get column comments.")
        return {}
    try:
        if not table_name:
            return {}
            
        query = text(f"""
        SELECT 
            COLUMN_NAME, 
            COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = '{DB_SCHEMA_MAIN}'
        AND TABLE_NAME = :table_name
        """)
        
        with engine.connect() as conn:
            result = conn.execute(query, {"table_name": table_name})
            comments = {}
            for row in result:
                comments[row[0]] = row[1] if row[1] else row[0]
            return comments
            
    except Exception as e:
        print(f"Error in get_column_comments: {str(e)}")
        return {}

def get_chinese_headers(table_name, engine):
    """获取表的中文列名，基于列注释"""
    try:
        if not table_name:
            return {}
            
        # 获取列注释作为中文表头
        column_comments = get_column_comments(table_name, engine)
        
        # 创建表头映射
        headers = {}
        for column, comment in column_comments.items():
            headers[column] = comment if comment else column
            
        return headers
            
    except Exception as e:
        print(f"Error in get_chinese_headers: {str(e)}")
        return {}

def get_table_stats(table_name, engine, provider=None, org_code=None):
    """获取表的统计信息"""
    if DB_SCHEMA_MAIN is None:
        print("Warning: DB_SCHEMA_MAIN is not configured. Cannot get table stats.")
        return {
            'is_provider_view': provider is not None, 'current_county': '', 'total_records': 0,
            'provider_count': 0, 'yearly_increment': 0, 'per_capita': 0,
            'total_population': 0, 'is_city_unit': False, 'table_stats': True
        }
    try:
        start_time = time.time()
        current_year = datetime.now().year
        year_start = f"{current_year}-01-01"
        
        # 获取表注释（区县信息）
        with engine.connect() as conn:
            table_comment_result = conn.execute(text(f"""
                SELECT TABLE_COMMENT
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = '{DB_SCHEMA_MAIN}'
                AND TABLE_NAME = :table_name
            """), {"table_name": table_name}).scalar()
            
            table_comment = table_comment_result if table_comment_result else ''
            # 从表注释中提取区县名称
            county_name = table_comment.split('_')[1] if table_comment and '_' in table_comment else ''
        
        # 如果provider为空字符串，将其视为None
        if provider:
            # 获取单位统计（直接使用表中的数据）
            with engine.connect() as conn:
                # 优化：使用单个查询获取所有需要的数据
                if org_code:
                    stats_query = f"""
                    SELECT 
                        t.org_code,
                        COALESCE(m.org_name, t.data_provider) as provider_name,
                        SUM(CAST(record_count AS DECIMAL(20,0))) as total_records,
                        SUM(CASE WHEN provide_time >= :year_start THEN CAST(record_count AS DECIMAL(20,0)) ELSE 0 END) as yearly_increment,
                        (SELECT SUM(CAST(record_count AS DECIMAL(20,0))) FROM {table_name}) as county_total
                    FROM {table_name} t
                    LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code
                    WHERE t.org_code = :org_code
                    GROUP BY t.org_code, provider_name
                    """
                    stats_result = conn.execute(text(stats_query), {
                        "org_code": org_code,
                        "year_start": year_start
                    }).fetchone()
                else:
                    # 先获取org_code
                    org_code_query = f"""
                    SELECT DISTINCT t.org_code
                    FROM {table_name} t
                    LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code
                    WHERE t.data_provider = :provider OR m.org_name = :provider
                    LIMIT 1
                    """
                    org_code_result = conn.execute(text(org_code_query), {"provider": provider}).fetchone()
                    
                    if not org_code_result:
                        return {
                            'is_provider_view': True,
                            'current_county': county_name,
                            'current_provider': provider,
                            'total_records': 0,
                            'percentage': 0,
                            'yearly_increment': 0,
                            'table_stats': True
                        }
                    
                    org_code = org_code_result[0]
                    
                    # 然后获取统计数据
                    stats_query = f"""
                    SELECT 
                        t.org_code,
                        COALESCE(m.org_name, t.data_provider) as provider_name,
                        SUM(CAST(record_count AS DECIMAL(20,0))) as total_records,
                        SUM(CASE WHEN provide_time >= :year_start THEN CAST(record_count AS DECIMAL(20,0)) ELSE 0 END) as yearly_increment,
                        (SELECT SUM(CAST(record_count AS DECIMAL(20,0))) FROM {table_name}) as county_total
                    FROM {table_name} t
                    LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code
                    WHERE t.org_code = :org_code
                    GROUP BY t.org_code, provider_name
                    """
                    stats_result = conn.execute(text(stats_query), {
                        "org_code": org_code,
                        "year_start": year_start
                    }).fetchone()
                
                if not stats_result:
                    return {
                        'is_provider_view': True,
                        'current_county': county_name,
                        'current_provider': provider,
                        'total_records': 0,
                        'percentage': 0,
                        'yearly_increment': 0,
                        'table_stats': True
                    }
                
                org_code_val = stats_result[0] # Renamed to avoid conflict
                display_provider = stats_result[1] if stats_result[1] else provider
                total_result = stats_result[2] or 0
                yearly_result = stats_result[3] or 0
                county_total = stats_result[4] or 0
                
                # 计算百分比
                percentage = round((total_result / county_total * 100 if county_total > 0 else 0), 2)
                
                print(f"get_table_stats (provider) took {time.time() - start_time:.2f} seconds")
                return {
                    'is_provider_view': True,
                    'current_county': county_name,
                    'current_provider': display_provider,
                    'total_records': int(total_result),
                    'percentage': percentage,
                    'yearly_increment': int(yearly_result),
                    'table_stats': True
                }
        else:
            # 获取全部统计
            with engine.connect() as conn:
                # 获取区县英文名称（用于关联人口数据）
                english_provider = table_name.replace('excel_data_', '')
                
                # 优化：使用单个查询获取所有需要的数据
                stats_query = f"""
                SELECT 
                    COALESCE(SUM(CAST(record_count AS DECIMAL(20,0))), 0) as total_records,
                    COUNT(DISTINCT org_code) as provider_count,
                    SUM(CASE WHEN YEAR(provide_time) = :year THEN CAST(record_count AS DECIMAL(20,0)) ELSE 0 END) as yearly_increment,
                    (
                        SELECT population 
                        FROM country_people_metadata 
                        WHERE englinsh_provider = :english_provider
                    ) as total_population
                FROM {table_name}
                """
                stats_result = conn.execute(text(stats_query), {
                    "year": current_year,
                    "english_provider": english_provider
                }).fetchone()
                
                total_records = stats_result[0] or 0
                provider_count = stats_result[1] or 0
                yearly_increment_val = stats_result[2] or 0 # Renamed
                total_population = stats_result[3] or 0
                
                # 计算人均汇聚量
                per_capita = round(total_records / total_population, 2) if total_population > 0 else 0
                
                # 判断是否是市直单位
                is_city_unit = table_comment.endswith('市直单位') if table_comment else False
            
            print(f"get_table_stats (all) took {time.time() - start_time:.2f} seconds")
            return {
                'is_provider_view': False,
                'current_county': county_name if county_name else table_comment,
                'total_records': int(total_records),
                'provider_count': provider_count,
                'yearly_increment': int(yearly_increment_val),
                'per_capita': per_capita,
                'total_population': total_population,
                'is_city_unit': is_city_unit,
                'table_stats': True
            }
            
    except Exception as e:
        print(f"Error in get_table_stats: {str(e)}")
        # Ensure table_comment is defined in this scope for the error return
        tc_for_error = locals().get('table_comment', '')
        return {
            'is_provider_view': provider is not None,
            'current_county': tc_for_error,
            'total_records': 0,
            'provider_count': 0,
            'yearly_increment': 0,
            'per_capita': 0,
            'total_population': 0,
            'is_city_unit': False,
            'table_stats': True
        }

def get_providers(table_name, engine):
    """获取表的所有数据提供者，通过组织机构代码关联获取单位名称"""
    try:
        start_time = time.time()
        if not table_name:
            return []
            
        # 优化查询，使用DISTINCT确保不返回重复的单位
        query = f"""
        SELECT DISTINCT
            m.org_name as provider_name,
            t.org_code,
            t.data_provider,
            MAX(t.provide_time) as latest_time
        FROM `{table_name}` t
        LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code
        WHERE t.org_code IS NOT NULL
        GROUP BY t.org_code, m.org_name, t.data_provider
        ORDER BY latest_time DESC
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(query))
            providers = []
            seen_org_codes = set()  # 用于跟踪已经添加的组织机构代码
            
            for row in result:
                provider_name = row.provider_name if row.provider_name else row.data_provider
                org_code_val = row.org_code # Renamed
                
                # 只有当这个组织机构代码还没有被添加过时，才添加到结果列表中
                if org_code_val not in seen_org_codes:
                    providers.append({
                        'name': provider_name,
                        'org_code': org_code_val,
                        'original_name': row.data_provider
                    })
                    seen_org_codes.add(org_code_val)
            
            print(f"get_providers took {time.time() - start_time:.2f} seconds")
            return providers
            
    except Exception as e:
        print(f"Error in get_providers: {str(e)}")
        return []

def get_yearly_increment_stats(table_name, engine, provider=None):
    """获取年度新增统计信息"""
    try:
        current_year = datetime.now().year
        year_start = f"{current_year}-01-01"
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        # 构建基础查询，将字符串类型转换为正确的数据类型
        if provider:
            # 获取年初数据（去年最后一条记录）
            year_start_query = f"""
            SELECT CAST(record_count AS DECIMAL(20,2)) as total
            FROM `{table_name}`
            WHERE data_provider = '{provider}'
            AND DATE(STR_TO_DATE(provide_time, '%Y-%m-%d')) < '{year_start}'
            ORDER BY provide_time DESC
            LIMIT 1
            """
            
            # 获取当前数据（最新一条记录）
            current_query = f"""
            SELECT CAST(record_count AS DECIMAL(20,2)) as total
            FROM `{table_name}`
            WHERE data_provider = '{provider}'
            ORDER BY provide_time DESC
            LIMIT 1
            """
            
            # 获取年度新增数据（当前记录 - 年初记录）
            increment_query = f"""
            SELECT 
                (
                    SELECT CAST(record_count AS DECIMAL(20,2))
                    FROM `{table_name}`
                    WHERE data_provider = '{provider}'
                    AND DATE(STR_TO_DATE(provide_time, '%Y-%m-%d')) <= '{current_date}'
                    ORDER BY provide_time DESC
                    LIMIT 1
                ) - COALESCE(
                    (
                        SELECT CAST(record_count AS DECIMAL(20,2))
                        FROM `{table_name}`
                        WHERE data_provider = '{provider}'
                        AND DATE(STR_TO_DATE(provide_time, '%Y-%m-%d')) < '{year_start}'
                        ORDER BY provide_time DESC
                        LIMIT 1
                    ), 0
                ) as increment
            """
        else:
            # 获取年初数据（去年最后一条记录）
            year_start_query = f"""
            SELECT CAST(record_count AS DECIMAL(20,2)) as total
            FROM `{table_name}`
            WHERE DATE(STR_TO_DATE(provide_time, '%Y-%m-%d')) < '{year_start}'
            ORDER BY provide_time DESC
            LIMIT 1
            """
            
            # 获取当前数据（最新一条记录）
            current_query = f"""
            SELECT CAST(record_count AS DECIMAL(20,2)) as total
            FROM `{table_name}`
            ORDER BY provide_time DESC
            LIMIT 1
            """
            
            # 获取年度新增数据（当前记录 - 年初记录）
            increment_query = f"""
            SELECT 
                (
                    SELECT CAST(record_count AS DECIMAL(20,2))
                    FROM `{table_name}`
                    WHERE DATE(STR_TO_DATE(provide_time, '%Y-%m-%d')) <= '{current_date}'
                    ORDER BY provide_time DESC
                    LIMIT 1
                ) - COALESCE(
                    (
                        SELECT CAST(record_count AS DECIMAL(20,2))
                        FROM `{table_name}`
                        WHERE DATE(STR_TO_DATE(provide_time, '%Y-%m-%d')) < '{year_start}'
                        ORDER BY provide_time DESC
                        LIMIT 1
                    ), 0
                ) as increment
            """
        
        with engine.connect() as conn:
            year_start_total = conn.execute(text(year_start_query)).scalar() or 0
            current_total = conn.execute(text(current_query)).scalar() or 0
            increment = conn.execute(text(increment_query)).scalar() or 0
            
            return {
                'year_start_total': year_start_total,
                'total': current_total,
                'increment': increment
            }
    except Exception as e:
        print(f"Error getting yearly increment stats for table {table_name}, provider {provider}: {e}")
        return None 