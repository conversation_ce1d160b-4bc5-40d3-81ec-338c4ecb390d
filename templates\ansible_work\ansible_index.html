{% extends "ansible_work/ansible_base.html" %}

{% block title %}Ansible 中台 - 概览{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-3 stats-container" id="dashboard-stats">
    <div class="col-lg-3 col-md-4 col-6 mb-2">
        <div class="stats-card">
            <div class="stats-icon"><i class="bi bi-server"></i></div>
            <div class="stats-value" id="server-count">-</div>
            <div class="stats-label">服务器总数</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-4 col-6 mb-2">
        <div class="stats-card">
            <div class="stats-icon"><i class="bi bi-list-task"></i></div>
            <div class="stats-value" id="task-count">-</div>
            <div class="stats-label">任务总数</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-4 col-6 mb-2">
        <div class="stats-card">
            <div class="stats-icon"><i class="bi bi-file-earmark-code"></i></div>
            <div class="stats-value" id="playbook-count">-</div>
            <div class="stats-label">Playbook总数</div>
        </div>
    </div>
</div>

<!-- 服务器管理 -->
<div class="section" id="servers">
    <div class="section-header d-flex justify-content-between align-items-center">
        <h2><i class="bi bi-server me-2"></i>服务器管理</h2>
        <div>
            <button class="btn btn-secondary me-2" onclick="showAddGroupForm()">
                <i class="bi bi-folder-plus me-1"></i>添加分组
            </button>
            <button class="btn btn-primary" onclick="showAddServerForm()">
                <i class="bi bi-plus-circle me-1"></i>添加服务器
            </button>
        </div>
    </div>
    
    <!-- 服务器分组 -->
    <div id="server-groups" class="mb-4"></div>
    
    <!-- 服务器列表 -->
    <div id="server-list" class="table-responsive"></div>
</div>

<!-- 任务管理 -->
<div class="section" id="tasks">
    <div class="section-header d-flex justify-content-between align-items-center">
        <h2><i class="bi bi-list-task me-2"></i>任务管理</h2>
        <button class="btn btn-primary" onclick="showAddTaskForm()">
            <i class="bi bi-plus-circle me-1"></i>创建任务
        </button>
    </div>
    <div id="task-list" class="table-responsive"></div>
</div>

<!-- Playbook管理 -->
<div class="section" id="playbooks">
    <div class="section-header d-flex justify-content-between align-items-center">
        <h2><i class="bi bi-file-earmark-code me-2"></i>Playbook管理</h2>
        <button class="btn btn-primary" onclick="showPlaybookForm()">
            <i class="bi bi-plus-circle me-1"></i>创建Playbook
        </button>
    </div>
    <div id="playbook-list" class="table-responsive"></div>
    <div id="playbook-editor" class="d-none mt-4">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-pencil-square me-2"></i>编辑Playbook
            </div>
            <div class="card-body">
                <form id="playbook-form" class="mb-3">
                    <div class="mb-3">
                        <label class="form-label">Playbook名称</label>
                        <input type="text" class="form-control" id="playbook-name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Playbook内容</label>
                        <textarea class="form-control" id="playbook-content" rows="10" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" id="playbook-description" rows="3"></textarea>
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="cancelPlaybookEdit()">
                            <i class="bi bi-x-circle me-1"></i>取消
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 文件管理 -->
<div class="section" id="file-manager">
    <div class="section-header d-flex justify-content-between align-items-center">
        <h2><i class="bi bi-folder me-2"></i>文件管理</h2>
        <div>
            <button class="btn btn-primary" id="upload-file-btn" disabled>
                <i class="bi bi-upload me-1"></i>上传文件
            </button>
            <button class="btn btn-secondary ms-2" id="batch-download-btn" disabled>
                <i class="bi bi-download me-1"></i>批量下载
            </button>
            <button class="btn btn-info ms-2" id="test-upload-panel-btn" onclick="testUploadPanel()">
                <i class="bi bi-gear me-1"></i>测试上传面板
            </button>
        </div>
    </div>
    
    <!-- 认证提示 -->
    <div id="file-manager-auth-prompt" class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>首次使用文件管理功能需要验证身份。
        <button class="btn btn-sm btn-primary ms-3" onclick="showFileManagerAuthForm()">
            <i class="bi bi-shield-lock me-1"></i>立即验证
        </button>
    </div>
    
    <!-- 文件浏览器 -->
    <div id="file-browser" class="card mt-3 d-none">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-folder me-2"></i><span id="current-path">/</span>
            </div>
            <div class="input-group" style="max-width: 300px;">
                <input type="text" class="form-control" id="path-input" placeholder="输入路径" value="/">
                <button class="btn btn-outline-secondary" type="button" id="go-to-path-btn">
                    <i class="bi bi-arrow-right"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="5%">
                                <input type="checkbox" id="select-all-files" class="form-check-input">
                            </th>
                            <th width="5%"></th>
                            <th width="40%">名称</th>
                            <th width="15%">大小</th>
                            <th width="20%">修改时间</th>
                            <th width="15%">操作</th>
                        </tr>
                    </thead>
                    <tbody id="file-list">
                        <!-- 文件列表将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 可以在这里添加 index.html 特有的 JS，如果需要的话 -->
{% endblock %}