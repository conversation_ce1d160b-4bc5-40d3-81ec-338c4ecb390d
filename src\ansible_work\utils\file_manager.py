import os
import paramiko
import tempfile
import shutil
from flask import session
import logging
import json
from datetime import datetime
import tarfile
import io
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor

class FileManager:
    """文件管理模块，用于处理7.26服务器与7.3服务器之间的文件操作"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.temp_dir = tempfile.mkdtemp()  # 创建临时目录用于文件上传
        self.upload_tasks = {}  # 存储上传任务状态
        self.executor = ThreadPoolExecutor(max_workers=3)  # 限制并发上传数量
        
    def __del__(self):
        """清理临时目录"""
        try:
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            self.logger.error(f"清理临时目录失败: {str(e)}")
    
    def authenticate(self, password):
        """验证7.26服务器密码并建立SSH连接"""
        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接到7.26服务器
            ssh.connect('************', 6233, 'root', password)
            
            # 测试连接到7.3服务器（假设已配置免密登录）
            stdin, stdout, stderr = ssh.exec_command('ssh -p 22 root@*********** "echo 连接成功"')
            result = stdout.read().decode('utf-8')
            
            if '连接成功' in result:
                # 将SSH客户端保存到会话中
                session['file_manager_authenticated'] = True
                session['file_manager_password'] = password  # 安全起见，实际生产环境应避免存储密码
                ssh.close()
                return True, "认证成功"
            else:
                ssh.close()
                return False, "无法连接到7.3服务器，请检查免密配置"
        except Exception as e:
            self.logger.error(f"认证失败: {str(e)}")
            return False, f"认证失败: {str(e)}"
    
    def is_authenticated(self):
        """检查是否已认证"""
        return session.get('file_manager_authenticated', False)
    
    def get_ssh_client(self, password=None):
        """获取SSH客户端连接"""
        if not password and not self.is_authenticated():
            return None

        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 使用提供的密码或会话中的密码连接到7.26服务器
            if not password:
                password = session.get('file_manager_password')
            ssh.connect('************', 6233, 'root', password)
            return ssh
        except Exception as e:
            self.logger.error(f"创建SSH连接失败: {str(e)}")
            return None
    
    def list_directory(self, directory_path):
        """列出指定目录下的文件和子目录"""
        if not self.is_authenticated():
            return False, "未认证，请先登录"
        
        try:
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败"
            
            # 通过SSH连接到7.3服务器并列出目录内容，加上-L参数确保能识别符号链接
            command = f'ssh -p 22 root@*********** "ls -la --time-style=long-iso {directory_path}"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if error and not output:
                ssh.close()
                return False, f"获取目录内容失败: {error}"
            
            # 解析ls命令输出
            lines = output.strip().split('\n')
            items = []
            
            # 记录调试信息
            self.logger.debug(f"目录内容原始输出:\n{output}")
            
            # 跳过前一行（总计行）
            for line in lines[1:]:
                parts = line.split()
                if len(parts) >= 8:
                    permissions = parts[0]
                    size = int(parts[4])
                    date = parts[5]
                    time = parts[6]
                    name = ' '.join(parts[7:])
                    
                    # 判断是文件还是目录或链接
                    is_dir = permissions.startswith('d')
                    is_link = permissions.startswith('l')
                    
                    # 如果是符号链接，检查它指向的是否是目录
                    if is_link:
                        # 提取链接指向
                        if '->' in name:
                            name_parts = name.split(' -> ')
                            name = name_parts[0]
                            link_target = name_parts[1]
                            
                            # 检查链接目标是否是目录
                            check_cmd = f'ssh -p 22 root@*********** "if [ -d \'{os.path.join(directory_path, link_target)}\' ] || [ -d \'{link_target}\' ]; then echo \'IS_DIR\'; fi"'
                            _, stdout, _ = ssh.exec_command(check_cmd)
                            check_result = stdout.read().decode('utf-8').strip()
                            
                            # 如果链接指向目录，也将其标记为目录
                            if check_result == 'IS_DIR':
                                is_dir = True
                    
                    # 构建完整路径用于后续操作
                    full_path = os.path.join(directory_path, name).replace('\\', '/')
                    if full_path.endswith('/') and len(full_path) > 1:
                        full_path = full_path[:-1]  # 移除末尾的斜杠
                    
                    items.append({
                        'name': name,
                        'is_directory': is_dir,
                        'size': size,
                        'modified_time': f"{date} {time}",
                        'permissions': permissions,
                        'path': full_path
                    })
                    
                    # 记录调试信息
                    self.logger.debug(f"解析项目: {name}, 类型: {'目录' if is_dir else '文件'}, 路径: {full_path}")
            
            ssh.close()
            return True, items
        except Exception as e:
            self.logger.error(f"列出目录内容失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"列出目录内容失败: {str(e)}"
    
    def upload_file(self, file, destination_path, overwrite=False):
        """上传文件到7.3服务器
        
        Args:
            file: 要上传的文件对象
            destination_path: 目标路径
            overwrite: 是否覆盖已存在的文件，默认False
        
        Returns:
            (success, message): 成功/失败标志和消息
        """
        if not self.is_authenticated():
            return False, "未认证，请先登录"
        
        if file.filename == '':
            return False, "没有选择文件"
        
        try:
            # 保存文件到临时目录
            temp_file_path = os.path.join(self.temp_dir, file.filename)
            file.save(temp_file_path)
            
            # 获取SSH客户端
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败"
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 上传文件到7.26服务器临时目录
            remote_temp_path = f'/tmp/{file.filename}'
            sftp.put(temp_file_path, remote_temp_path)
            
            # 确保目标目录存在
            # 规范化路径，移除多余的斜杠
            destination_path = os.path.normpath(destination_path).replace('\\', '/')
            ensure_dir_command = f'ssh -p 22 root@*********** "mkdir -p \'{destination_path}\'"'
            stdin, stdout, stderr = ssh.exec_command(ensure_dir_command)
            exit_status = stdout.channel.recv_exit_status()
            
            # 检查目标服务器上是否已存在同名文件
            target_path = os.path.join(destination_path, file.filename).replace('\\', '/')
            check_file_command = f'ssh -p 22 root@*********** "if [ -f \'{target_path}\' ]; then echo \'FILE_EXISTS\'; else echo \'FILE_NOT_EXISTS\'; fi"'
            stdin, stdout, stderr = ssh.exec_command(check_file_command)
            check_result = stdout.read().decode('utf-8').strip()
            
            if "FILE_EXISTS" in check_result and not overwrite:
                sftp.close()
                ssh.close()
                return False, f"上传失败：目标位置已存在同名文件 '{file.filename}'，请重命名后再上传或选择覆盖选项"
            
            # 从7.26服务器传输到7.3服务器
            command = f'scp -P 22 {remote_temp_path} root@***********:"{target_path}"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            error = stderr.read().decode('utf-8')
            
            # 忽略SSH欢迎信息，只检查实际错误
            if error and "You have logged onto a secured server" not in error and exit_status != 0:
                sftp.close()
                ssh.close()
                return False, f"上传文件失败: {error}"
            
            # 验证文件是否成功传输到7.3服务器
            # 使用单引号包裹路径，避免特殊字符问题
            verify_command = f'ssh -p 22 root@*********** "ls -la \'{target_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
            stdin, stdout, stderr = ssh.exec_command(verify_command)
            verify_output = stdout.read().decode('utf-8')
            
            if "FILE_NOT_FOUND" in verify_output or not verify_output:
                # 尝试直接使用SSH命令复制文件
                direct_copy_command = f'ssh -p 22 root@*********** "cat > \'{target_path}\'" < {remote_temp_path}'
                stdin, stdout, stderr = ssh.exec_command(direct_copy_command)
                exit_status = stdout.channel.recv_exit_status()
                
                # 再次验证文件
                verify_command = f'ssh -p 22 root@*********** "ls -la \'{target_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
                stdin, stdout, stderr = ssh.exec_command(verify_command)
                verify_output = stdout.read().decode('utf-8')
                
                if "FILE_NOT_FOUND" in verify_output or not verify_output:
                    sftp.close()
                    ssh.close()
                    return False, "文件上传失败：无法在目标服务器上验证文件"
            
            # 清理临时文件
            ssh.exec_command(f'rm {remote_temp_path}')
            
            sftp.close()
            ssh.close()
            
            # 根据是否覆盖返回不同的成功消息
            if "FILE_EXISTS" in check_result and overwrite:
                return True, f"文件上传成功（覆盖了已有文件）"
            else:
                return True, "文件上传成功"
        except Exception as e:
            self.logger.error(f"上传文件失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"上传文件失败: {str(e)}"
    
    def download_file(self, file_path):
        """从7.3服务器下载文件"""
        if not self.is_authenticated():
            return False, "未认证，请先登录", None
        
        try:
            # 获取SSH客户端
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败", None
            
            # 从7.3服务器复制到7.26服务器
            filename = os.path.basename(file_path)
            remote_temp_path = f'/tmp/{filename}'
            
            # 使用ssh命令而不是scp，避免可能的端口问题
            # 注意：重定向符号(>)需要在远程服务器上执行，所以我们将整个命令包装在一个引号中
            command = f'ssh -p 22 root@*********** "cat \'{file_path}\'" | cat > {remote_temp_path}'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            error = stderr.read().decode('utf-8')
            
            # 检查文件是否成功创建
            _, stdout, _ = ssh.exec_command(f'ls -la {remote_temp_path}')
            file_check = stdout.read().decode('utf-8')
            
            if exit_status != 0 or ('No such file' in error) or not file_check:
                ssh.close()
                return False, f"文件不存在或无法访问: {file_path} (错误: {error})", None
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 创建内存文件对象
            memory_file = io.BytesIO()
            
            # 从7.26服务器下载文件到内存
            sftp.getfo(remote_temp_path, memory_file)
            
            # 清理临时文件
            ssh.exec_command(f'rm {remote_temp_path}')
            
            # 重置文件指针到开始位置
            memory_file.seek(0)
            
            sftp.close()
            ssh.close()
            return True, "文件下载成功", memory_file
        except Exception as e:
            self.logger.error(f"下载文件失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"下载文件失败: {str(e)}", None
    
    def batch_download(self, file_paths):
        """批量下载文件（打包为tar.gz）"""
        if not self.is_authenticated():
            return False, "未认证，请先登录", None
        
        try:
            # 获取SSH客户端
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败", None
            
            # 生成唯一的临时文件名
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            tar_filename = f'batch_download_{timestamp}.tar.gz'
            remote_tar_path = f'/tmp/{tar_filename}'
            
            # 构建tar命令，从7.3服务器打包文件
            file_paths_str = ' '.join([f'"{path}"' for path in file_paths])
            tar_command = f'ssh -p 22 root@*********** "cd / && tar -czf - {file_paths_str}" | cat > {remote_tar_path}'
            stdin, stdout, stderr = ssh.exec_command(tar_command)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            error = stderr.read().decode('utf-8')
            
            # 检查文件是否成功创建
            _, stdout, _ = ssh.exec_command(f'ls -la {remote_tar_path}')
            file_check = stdout.read().decode('utf-8')
            
            if exit_status != 0 or ('No such file' in error) or not file_check:
                ssh.close()
                return False, f"部分文件不存在或无法访问: {error}", None
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 创建内存文件对象
            memory_file = io.BytesIO()
            
            # 从7.26服务器下载打包文件到内存
            sftp.getfo(remote_tar_path, memory_file)
            
            # 清理临时文件
            ssh.exec_command(f'rm {remote_tar_path}')
            
            # 重置文件指针到开始位置
            memory_file.seek(0)
            
            sftp.close()
            ssh.close()
            return True, "批量下载成功", memory_file
        except Exception as e:
            self.logger.error(f"批量下载文件失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"批量下载文件失败: {str(e)}", None
    
    def check_file_exists(self, file_path):
        """检查指定文件是否存在"""
        if not self.is_authenticated():
            return False
        
        try:
            ssh = self.get_ssh_client()
            if not ssh:
                return False
            
            # 使用引号包裹路径，避免特殊字符问题
            command = f'ssh -p 22 root@*********** "[ -f \'{file_path}\' ] && echo \'EXISTS\' || echo \'NOT_EXISTS\'"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            output = stdout.read().decode('utf-8').strip()
            ssh.close()
            
            return output == 'EXISTS'
        except Exception as e:
            self.logger.error(f"检查文件存在失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False

    def get_file_info(self, file_path):
        """获取文件的元数据信息"""
        if not self.is_authenticated():
            return None
        
        try:
            ssh = self.get_ssh_client()
            if not ssh:
                return None
            
            # 执行stat命令获取文件信息
            command = f'ssh -p 22 root@*********** "stat -c \'%s,%Y,%A,%F\' \'{file_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            output = stdout.read().decode('utf-8').strip()
            ssh.close()
            
            if output == 'FILE_NOT_FOUND':
                return None
            
            # 解析输出 (大小,修改时间,权限,文件类型)
            parts = output.split(',')
            if len(parts) >= 4:
                size = int(parts[0])
                mod_time = datetime.fromtimestamp(int(parts[1]))
                permissions = parts[2]
                file_type = parts[3]
                
                return {
                    'path': file_path,
                    'name': os.path.basename(file_path),
                    'size': size,
                    'modified_time': mod_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'permissions': permissions,
                    'is_directory': 'directory' in file_type.lower()
                }
            
            return None
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return None

    def upload_file_async(self, file, destination_path, overwrite=True):
        """快速异步上传文件，默认覆盖重复文件"""
        if not self.is_authenticated():
            return False, "未认证，请先登录", None

        if file.filename == '':
            return False, "没有选择文件", None

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 保存文件到临时目录
        temp_file_path = os.path.join(self.temp_dir, f"{task_id}_{file.filename}")
        file.save(temp_file_path)

        # 获取文件大小
        file_size = os.path.getsize(temp_file_path)

        # 创建任务状态，立即设置为上传中状态
        task_status = {
            'id': task_id,
            'filename': file.filename,
            'destination_path': destination_path,
            'overwrite': True,  # 强制覆盖
            'status': 'uploading',  # 立即设置为上传中
            'progress': 0,
            'speed': 0,
            'start_time': time.time(),
            'error_message': None,
            'file_size': file_size,
            'password': session.get('file_manager_password'),
            'temp_file_path': temp_file_path
        }

        self.upload_tasks[task_id] = task_status

        # 立即提交异步任务，不等待
        future = self.executor.submit(self._fast_upload_worker, task_id)
        task_status['future'] = future

        return True, "快速上传已开始", task_id

    def _fast_upload_worker(self, task_id):
        """快速上传工作线程 - 优化版本"""
        task = self.upload_tasks.get(task_id)
        if not task:
            return

        try:
            # 不使用Flask上下文，直接使用保存的密码
            # 避免 "Working outside of application context" 错误

            # 立即更新进度
            task['progress'] = 5

            # 获取SSH客户端
            ssh = self.get_ssh_client(password=task.get('password'))
            if not ssh:
                task['status'] = 'failed'
                task['error_message'] = "SSH连接失败"
                return

            task['progress'] = 10

            # 创建SFTP客户端
            sftp = ssh.open_sftp()

            # 文件路径
            temp_file_path = task['temp_file_path']
            filename = task['filename']
            destination_path = os.path.normpath(task['destination_path']).replace('\\', '/')

            # 上传到7.26服务器临时目录
            remote_temp_path = f'/tmp/fast_{task_id}_{filename}'

            task['progress'] = 15

            # 快速上传到7.26服务器，带进度回调
            def progress_callback(transferred, total):
                if task_id in self.upload_tasks:
                    # 15-70% 用于上传到7.26
                    progress = 15 + (transferred / total) * 55
                    self.upload_tasks[task_id]['progress'] = progress

                    # 计算实时速度
                    elapsed = time.time() - task['start_time']
                    if elapsed > 0:
                        speed = transferred / elapsed
                        self.upload_tasks[task_id]['speed'] = speed

            sftp.put(temp_file_path, remote_temp_path, callback=progress_callback)
            task['progress'] = 70

            # 确保目标目录存在
            ensure_dir_command = f'ssh -p 22 root@*********** "mkdir -p \'{destination_path}\'"'
            ssh.exec_command(ensure_dir_command)

            task['progress'] = 75

            # 目标文件路径
            target_path = os.path.join(destination_path, filename).replace('\\', '/')

            # 直接传输到7.3服务器（强制覆盖）
            transfer_cmd = f'scp -P 22 {remote_temp_path} root@***********:"{target_path}"'
            _, stdout, _ = ssh.exec_command(transfer_cmd)
            stdout.channel.recv_exit_status()

            task['progress'] = 90

            # 快速验证（不等待详细检查）
            verify_cmd = f'ssh -p 22 root@*********** "[ -f \'{target_path}\' ] && echo OK"'
            _, stdout, _ = ssh.exec_command(verify_cmd)
            verify_result = stdout.read().decode('utf-8').strip()

            if verify_result == 'OK':
                task['progress'] = 100
                task['status'] = 'completed'
            else:
                task['status'] = 'failed'
                task['error_message'] = "文件传输验证失败"

            # 清理临时文件
            ssh.exec_command(f'rm -f {remote_temp_path}')

            sftp.close()
            ssh.close()

        except Exception as e:
            self.logger.error(f"快速上传失败: {str(e)}")
            task['status'] = 'failed'
            task['error_message'] = str(e)
        finally:
            # 清理本地临时文件
            try:
                if os.path.exists(task['temp_file_path']):
                    os.remove(task['temp_file_path'])
            except Exception as e:
                self.logger.error(f"清理临时文件失败: {str(e)}")

    def _upload_file_worker(self, task_id, temp_file_path):
        """异步上传工作线程"""
        from flask import current_app

        task = self.upload_tasks.get(task_id)
        if not task:
            return

        try:
            # 在应用上下文中执行整个上传过程
            with current_app.app_context():
                task['status'] = 'uploading'
                task['progress'] = 10

                # 获取SSH客户端，使用保存的密码
                ssh = self.get_ssh_client(password=task.get('password'))
                if not ssh:
                    task['status'] = 'failed'
                    task['error_message'] = "SSH连接失败"
                    return

                task['progress'] = 20

                # 创建SFTP客户端
                sftp = ssh.open_sftp()

                # 上传文件到7.26服务器临时目录
                remote_temp_path = f'/tmp/{task_id}_{task["filename"]}'

                # 使用回调函数跟踪上传进度
                def progress_callback(transferred, total):
                    if task_id in self.upload_tasks:
                        progress = 20 + (transferred / total) * 50  # 20-70%
                        self.upload_tasks[task_id]['progress'] = progress

                        # 计算速度
                        elapsed = time.time() - task['start_time']
                        if elapsed > 0:
                            speed = transferred / elapsed
                            self.upload_tasks[task_id]['speed'] = speed

                sftp.put(temp_file_path, remote_temp_path, callback=progress_callback)
                task['progress'] = 70

                # 确保目标目录存在
                destination_path = os.path.normpath(task['destination_path']).replace('\\', '/')
                ensure_dir_command = f'ssh -p 22 root@*********** "mkdir -p \'{destination_path}\'"'
                _, stdout, _ = ssh.exec_command(ensure_dir_command)
                stdout.channel.recv_exit_status()

                task['progress'] = 75

                # 检查目标服务器上是否已存在同名文件
                target_path = os.path.join(destination_path, task['filename']).replace('\\', '/')
                check_file_command = f'ssh -p 22 root@*********** "if [ -f \'{target_path}\' ]; then echo \'FILE_EXISTS\'; else echo \'FILE_NOT_EXISTS\'; fi"'
                _, stdout, _ = ssh.exec_command(check_file_command)
                check_result = stdout.read().decode('utf-8').strip()

                if "FILE_EXISTS" in check_result and not task['overwrite']:
                    task['status'] = 'failed'
                    task['error_message'] = f"目标位置已存在同名文件 '{task['filename']}'，请重命名后再上传或选择覆盖选项"
                    sftp.close()
                    ssh.close()
                    return

                task['progress'] = 80

                # 从7.26服务器传输到7.3服务器
                command = f'scp -P 22 {remote_temp_path} root@***********:"{target_path}"'
                _, stdout, stderr = ssh.exec_command(command)

                exit_status = stdout.channel.recv_exit_status()
                error = stderr.read().decode('utf-8')

                if error and "You have logged onto a secured server" not in error and exit_status != 0:
                    task['status'] = 'failed'
                    task['error_message'] = f"传输文件失败: {error}"
                    sftp.close()
                    ssh.close()
                    return

                task['progress'] = 90

                # 验证文件是否成功传输
                verify_command = f'ssh -p 22 root@*********** "ls -la \'{target_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
                _, stdout, _ = ssh.exec_command(verify_command)
                verify_output = stdout.read().decode('utf-8')

                if "FILE_NOT_FOUND" in verify_output or not verify_output:
                    task['status'] = 'failed'
                    task['error_message'] = "文件传输验证失败"
                    sftp.close()
                    ssh.close()
                    return

                # 清理临时文件
                ssh.exec_command(f'rm {remote_temp_path}')

                task['progress'] = 100
                task['status'] = 'completed'

                sftp.close()
                ssh.close()

        except Exception as e:
            self.logger.error(f"异步上传失败: {str(e)}")
            task['status'] = 'failed'
            task['error_message'] = str(e)
        finally:
            # 清理本地临时文件
            try:
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            except Exception as e:
                self.logger.error(f"清理临时文件失败: {str(e)}")

    def get_upload_status(self, task_id):
        """获取上传任务状态"""
        task = self.upload_tasks.get(task_id)
        if not task:
            return None

        # 返回任务状态（不包含future对象）
        return {
            'id': task['id'],
            'filename': task['filename'],
            'status': task['status'],
            'progress': task['progress'],
            'speed': task['speed'],
            'error_message': task['error_message'],
            'file_size': task['file_size']
        }

    def cancel_upload(self, task_id):
        """取消上传任务"""
        task = self.upload_tasks.get(task_id)
        if not task:
            return False, "任务不存在"

        if task['status'] in ['completed', 'failed']:
            return False, "任务已完成，无法取消"

        # 取消future任务
        if 'future' in task:
            task['future'].cancel()

        task['status'] = 'cancelled'
        return True, "任务已取消"

    def cleanup_completed_tasks(self, max_age_hours=24):
        """清理已完成的任务（超过指定时间）"""
        current_time = time.time()
        tasks_to_remove = []

        for task_id, task in self.upload_tasks.items():
            if task['status'] in ['completed', 'failed', 'cancelled']:
                age_hours = (current_time - task['start_time']) / 3600
                if age_hours > max_age_hours:
                    tasks_to_remove.append(task_id)

        for task_id in tasks_to_remove:
            del self.upload_tasks[task_id]

        return len(tasks_to_remove)

    def upload_file_chunked(self, file, destination_path, overwrite=True, chunk_size=10*1024*1024):
        """快速分片上传文件，默认覆盖重复文件

        Args:
            file: 要上传的文件对象
            destination_path: 目标路径
            overwrite: 是否覆盖已存在的文件，默认True
            chunk_size: 分片大小，默认10MB

        Returns:
            (success, message, task_id): 成功/失败标志、消息和任务ID
        """
        if not self.is_authenticated():
            return False, "未认证，请先登录", None

        if file.filename == '':
            return False, "没有选择文件", None

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 保存文件到临时目录
        temp_file_path = os.path.join(self.temp_dir, f"{task_id}_{file.filename}")
        file.save(temp_file_path)

        # 获取文件大小
        file_size = os.path.getsize(temp_file_path)
        total_chunks = (file_size + chunk_size - 1) // chunk_size  # 向上取整

        # 创建任务状态，立即设置为上传中
        task_status = {
            'id': task_id,
            'filename': file.filename,
            'destination_path': destination_path,
            'overwrite': True,  # 强制覆盖
            'status': 'uploading',  # 立即设置为上传中
            'progress': 0,
            'speed': 0,
            'start_time': time.time(),
            'error_message': None,
            'file_size': file_size,
            'chunk_size': chunk_size,
            'total_chunks': total_chunks,
            'uploaded_chunks': 0,
            'failed_chunks': [],
            'upload_id': None,  # 服务器端的上传会话ID
            'temp_file_path': temp_file_path,
            'password': session.get('file_manager_password')  # 保存密码供异步线程使用
        }

        self.upload_tasks[task_id] = task_status

        # 立即提交异步任务
        future = self.executor.submit(self._fast_chunked_upload_worker, task_id)
        task_status['future'] = future

        return True, "快速分片上传已开始", task_id

    def _fast_chunked_upload_worker(self, task_id):
        """快速分片上传工作线程 - 优化版本"""
        task = self.upload_tasks.get(task_id)
        if not task:
            return

        try:
            # 不使用Flask上下文，直接使用保存的密码
            # 避免 "Working outside of application context" 错误

            # 立即更新进度
            task['progress'] = 5

            # 获取SSH客户端
            ssh = self.get_ssh_client(password=task.get('password'))
            if not ssh:
                task['status'] = 'failed'
                task['error_message'] = "SSH连接失败"
                return

            # 创建SFTP客户端
            sftp = ssh.open_sftp()

            # 文件信息
            temp_file_path = task['temp_file_path']
            filename = task['filename']
            destination_path = os.path.normpath(task['destination_path']).replace('\\', '/')
            target_path = os.path.join(destination_path, filename).replace('\\', '/')

            task['progress'] = 10

            # 确保目标目录存在（强制覆盖模式，跳过检查）
            ensure_dir_command = f'ssh -p 22 root@*********** "mkdir -p \'{destination_path}\'"'
            ssh.exec_command(ensure_dir_command)

            task['progress'] = 15

            # 直接上传到7.26服务器，然后传输到7.3
            remote_temp_path = f'/tmp/fast_chunked_{task_id}_{filename}'

            # 快速上传到7.26服务器，带进度回调
            def progress_callback(transferred, total):
                if task_id in self.upload_tasks:
                    # 15-80% 用于上传到7.26
                    progress = 15 + (transferred / total) * 65
                    self.upload_tasks[task_id]['progress'] = progress

                    # 计算实时速度
                    elapsed = time.time() - task['start_time']
                    if elapsed > 0:
                        speed = transferred / elapsed
                        self.upload_tasks[task_id]['speed'] = speed

            sftp.put(temp_file_path, remote_temp_path, callback=progress_callback)
            task['progress'] = 80

            # 直接传输到7.3服务器（强制覆盖）
            transfer_cmd = f'scp -P 22 {remote_temp_path} root@***********:"{target_path}"'
            _, stdout, _ = ssh.exec_command(transfer_cmd)
            stdout.channel.recv_exit_status()

            task['progress'] = 95

            # 快速验证（不等待详细检查）
            verify_cmd = f'ssh -p 22 root@*********** "[ -f \'{target_path}\' ] && echo OK"'
            _, stdout, _ = ssh.exec_command(verify_cmd)
            verify_result = stdout.read().decode('utf-8').strip()

            if verify_result == 'OK':
                task['progress'] = 100
                task['status'] = 'completed'
            else:
                task['status'] = 'failed'
                task['error_message'] = "文件传输验证失败"

            # 清理临时文件
            ssh.exec_command(f'rm -f {remote_temp_path}')

            sftp.close()
            ssh.close()

        except Exception as e:
            self.logger.error(f"快速分片上传失败: {str(e)}")
            task['status'] = 'failed'
            task['error_message'] = str(e)
        finally:
            # 清理本地临时文件
            try:
                if os.path.exists(task['temp_file_path']):
                    os.remove(task['temp_file_path'])
            except Exception as e:
                self.logger.error(f"清理临时文件失败: {str(e)}")

    def _chunked_upload_worker(self, task_id):
        """分片上传工作线程"""
        from flask import current_app

        task = self.upload_tasks.get(task_id)
        if not task:
            return

        try:
            # 在应用上下文中执行整个上传过程
            with current_app.app_context():
                task['status'] = 'uploading'

                # 1. 初始化上传会话
                upload_id = self._init_chunked_upload(task)
                if not upload_id:
                    task['status'] = 'failed'
                    task['error_message'] = "初始化上传会话失败"
                    return

                task['upload_id'] = upload_id
                task['progress'] = 5

                # 2. 上传所有分片
                success = self._upload_all_chunks(task)
                if not success:
                    return

                # 3. 完成上传（合并分片）
                success = self._complete_chunked_upload(task)
                if success:
                    task['status'] = 'completed'
                    task['progress'] = 100
                else:
                    task['status'] = 'failed'
                    task['error_message'] = "合并分片失败"

        except Exception as e:
            self.logger.error(f"分片上传失败: {str(e)}")
            task['status'] = 'failed'
            task['error_message'] = str(e)
        finally:
            # 清理本地临时文件
            try:
                if os.path.exists(task['temp_file_path']):
                    os.remove(task['temp_file_path'])
            except Exception as e:
                self.logger.error(f"清理临时文件失败: {str(e)}")

    def _init_chunked_upload(self, task):
        """初始化分片上传会话"""
        try:
            ssh = self.get_ssh_client(password=task.get('password'))
            if not ssh:
                return None

            # 生成服务器端的上传会话ID
            upload_id = str(uuid.uuid4())

            # 在7.26服务器创建临时目录用于存储分片
            temp_dir = f"/tmp/chunked_upload_{upload_id}"
            create_dir_cmd = f"mkdir -p {temp_dir}"
            _, stdout, stderr = ssh.exec_command(create_dir_cmd)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status != 0:
                error = stderr.read().decode('utf-8')
                self.logger.error(f"创建临时目录失败: {error}")
                ssh.close()
                return None

            # 检查目标服务器上是否已存在同名文件
            destination_path = os.path.normpath(task['destination_path']).replace('\\', '/')
            target_path = os.path.join(destination_path, task['filename']).replace('\\', '/')

            check_file_command = f'ssh -p 22 root@*********** "if [ -f \'{target_path}\' ]; then echo \'FILE_EXISTS\'; else echo \'FILE_NOT_EXISTS\'; fi"'
            _, stdout, _ = ssh.exec_command(check_file_command)
            check_result = stdout.read().decode('utf-8').strip()

            if "FILE_EXISTS" in check_result and not task['overwrite']:
                ssh.close()
                task['error_message'] = f"目标位置已存在同名文件 '{task['filename']}'，请重命名后再上传或选择覆盖选项"
                return None

            # 确保目标目录存在
            ensure_dir_command = f'ssh -p 22 root@*********** "mkdir -p \'{destination_path}\'"'
            _, stdout, _ = ssh.exec_command(ensure_dir_command)
            stdout.channel.recv_exit_status()

            ssh.close()
            return upload_id

        except Exception as e:
            self.logger.error(f"初始化分片上传失败: {str(e)}")
            return None

    def _upload_all_chunks(self, task):
        """上传所有分片"""
        try:
            with open(task['temp_file_path'], 'rb') as f:
                for chunk_index in range(task['total_chunks']):
                    if task['status'] == 'cancelled':
                        return False

                    # 读取分片数据
                    chunk_data = f.read(task['chunk_size'])
                    if not chunk_data:
                        break

                    # 上传分片（支持重试）
                    max_retries = 3
                    for retry in range(max_retries):
                        success = self._upload_single_chunk(task, chunk_index, chunk_data)
                        if success:
                            task['uploaded_chunks'] += 1
                            # 更新进度 (5% 初始化 + 85% 上传 + 10% 合并)
                            progress = 5 + (task['uploaded_chunks'] / task['total_chunks']) * 85
                            task['progress'] = progress

                            # 计算速度
                            elapsed = time.time() - task['start_time']
                            if elapsed > 0:
                                uploaded_bytes = task['uploaded_chunks'] * task['chunk_size']
                                speed = uploaded_bytes / elapsed
                                task['speed'] = speed

                            break
                        else:
                            if retry == max_retries - 1:
                                task['failed_chunks'].append(chunk_index)
                                self.logger.error(f"分片 {chunk_index} 上传失败，已重试 {max_retries} 次")
                                return False
                            else:
                                time.sleep(1)  # 重试前等待1秒

                return len(task['failed_chunks']) == 0

        except Exception as e:
            self.logger.error(f"上传分片过程失败: {str(e)}")
            return False

    def _upload_single_chunk(self, task, chunk_index, chunk_data):
        """上传单个分片"""
        try:
            ssh = self.get_ssh_client(password=task.get('password'))
            if not ssh:
                return False

            sftp = ssh.open_sftp()

            # 分片文件名
            chunk_filename = f"chunk_{chunk_index:06d}"
            temp_dir = f"/tmp/chunked_upload_{task['upload_id']}"
            chunk_path = f"{temp_dir}/{chunk_filename}"

            # 创建临时文件并写入分片数据
            local_chunk_path = os.path.join(self.temp_dir, f"{task['id']}_{chunk_filename}")
            with open(local_chunk_path, 'wb') as f:
                f.write(chunk_data)

            # 上传分片到7.26服务器
            sftp.put(local_chunk_path, chunk_path)

            # 清理本地临时分片文件
            os.remove(local_chunk_path)

            sftp.close()
            ssh.close()
            return True

        except Exception as e:
            self.logger.error(f"上传分片 {chunk_index} 失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False

    def _complete_chunked_upload(self, task):
        """完成分片上传（合并分片）"""
        try:
            ssh = self.get_ssh_client(password=task.get('password'))
            if not ssh:
                return False

            temp_dir = f"/tmp/chunked_upload_{task['upload_id']}"
            destination_path = os.path.normpath(task['destination_path']).replace('\\', '/')
            target_path = os.path.join(destination_path, task['filename']).replace('\\', '/')

            # 在7.26服务器上合并分片
            merge_cmd = f"cd {temp_dir} && cat chunk_* > merged_file"
            _, stdout, stderr = ssh.exec_command(merge_cmd)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status != 0:
                error = stderr.read().decode('utf-8')
                self.logger.error(f"合并分片失败: {error}")
                ssh.close()
                return False

            task['progress'] = 90

            # 传输合并后的文件到7.3服务器
            merged_file_path = f"{temp_dir}/merged_file"
            transfer_cmd = f'scp -P 22 {merged_file_path} root@***********:"{target_path}"'
            _, stdout, stderr = ssh.exec_command(transfer_cmd)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status != 0:
                error = stderr.read().decode('utf-8')
                if "You have logged onto a secured server" not in error:
                    self.logger.error(f"传输文件失败: {error}")
                    ssh.close()
                    return False

            task['progress'] = 95

            # 验证文件传输
            verify_command = f'ssh -p 22 root@*********** "ls -la \'{target_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
            _, stdout, _ = ssh.exec_command(verify_command)
            verify_output = stdout.read().decode('utf-8')

            if "FILE_NOT_FOUND" in verify_output:
                self.logger.error("文件传输验证失败")
                ssh.close()
                return False

            # 清理临时目录
            cleanup_cmd = f"rm -rf {temp_dir}"
            ssh.exec_command(cleanup_cmd)

            ssh.close()
            return True

        except Exception as e:
            self.logger.error(f"完成分片上传失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False

    def pause_chunked_upload(self, task_id):
        """暂停分片上传"""
        task = self.upload_tasks.get(task_id)
        if not task:
            return False, "任务不存在"

        if task['status'] != 'uploading':
            return False, "任务不在上传状态"

        task['status'] = 'paused'
        return True, "任务已暂停"

    def resume_chunked_upload(self, task_id):
        """恢复分片上传"""
        task = self.upload_tasks.get(task_id)
        if not task:
            return False, "任务不存在"

        if task['status'] != 'paused':
            return False, "任务不在暂停状态"

        # 重新提交任务
        future = self.executor.submit(self._resume_chunked_upload_worker, task_id)
        task['future'] = future
        task['status'] = 'uploading'

        return True, "任务已恢复"

    def _resume_chunked_upload_worker(self, task_id):
        """恢复分片上传工作线程"""
        from flask import current_app

        task = self.upload_tasks.get(task_id)
        if not task:
            return

        try:
            # 在应用上下文中执行恢复过程
            with current_app.app_context():
                # 检查已上传的分片
                uploaded_chunks = self._check_uploaded_chunks(task)
                task['uploaded_chunks'] = uploaded_chunks

                # 继续上传剩余分片
                success = self._upload_remaining_chunks(task)
                if not success:
                    return

                # 完成上传
                success = self._complete_chunked_upload(task)
                if success:
                    task['status'] = 'completed'
                    task['progress'] = 100
                else:
                    task['status'] = 'failed'
                    task['error_message'] = "合并分片失败"

        except Exception as e:
            self.logger.error(f"恢复分片上传失败: {str(e)}")
            task['status'] = 'failed'
            task['error_message'] = str(e)

    def _check_uploaded_chunks(self, task):
        """检查已上传的分片数量"""
        try:
            ssh = self.get_ssh_client(password=task.get('password'))
            if not ssh:
                return 0

            temp_dir = f"/tmp/chunked_upload_{task['upload_id']}"
            check_cmd = f"ls {temp_dir}/chunk_* 2>/dev/null | wc -l"
            _, stdout, _ = ssh.exec_command(check_cmd)

            count_str = stdout.read().decode('utf-8').strip()
            ssh.close()

            return int(count_str) if count_str.isdigit() else 0

        except Exception as e:
            self.logger.error(f"检查已上传分片失败: {str(e)}")
            return 0

    def _upload_remaining_chunks(self, task):
        """上传剩余的分片"""
        try:
            with open(task['temp_file_path'], 'rb') as f:
                for chunk_index in range(task['uploaded_chunks'], task['total_chunks']):
                    if task['status'] == 'paused' or task['status'] == 'cancelled':
                        return False

                    # 定位到分片位置
                    f.seek(chunk_index * task['chunk_size'])
                    chunk_data = f.read(task['chunk_size'])

                    if not chunk_data:
                        break

                    # 上传分片
                    success = self._upload_single_chunk(task, chunk_index, chunk_data)
                    if success:
                        task['uploaded_chunks'] += 1
                        progress = 5 + (task['uploaded_chunks'] / task['total_chunks']) * 85
                        task['progress'] = progress

                        # 计算速度
                        elapsed = time.time() - task['start_time']
                        if elapsed > 0:
                            uploaded_bytes = task['uploaded_chunks'] * task['chunk_size']
                            speed = uploaded_bytes / elapsed
                            task['speed'] = speed
                    else:
                        return False

                return True

        except Exception as e:
            self.logger.error(f"上传剩余分片失败: {str(e)}")
            return False
