# -*- coding: utf-8 -*-
"""
数据展示模块 - 提供入湖数据统计展示功能
"""
from flask import Blueprint, render_template, jsonify, request
from datetime import datetime
import logging
from typing import List, Dict, Any, Optional, Tuple

# 导入公共数据计算模块
from src.utils.data_calculation import (
    get_data_tables, get_monthly_stats, add_fixed_value_to_total,
    calculate_city_total_data, calculate_city_yearly_data, calculate_city_weekly_data,
    calculate_city_monthly_data, calculate_date_range, format_large_number,
    get_county_code_from_table, get_county_name_from_code, get_county_population,
    calculate_county_total_data, calculate_county_yearly_data,
    calculate_county_per_capita_total, calculate_county_per_capita_yearly,
    get_county_per_capita_ranking, get_total_records
)

# 创建蓝图
data_display = Blueprint('data_display', __name__)

@data_display.route('/data_display')
def data_display_view():
    """数据展示页面视图"""
    return render_template('data_display.html')

@data_display.route('/api/data_display/total_stats')
def get_total_stats():
    """获取全市入湖总数据统计"""
    try:
        # 获取所有表名
        tables = get_data_tables()
        
        # 从公共计算模块中获取表统计数据
        table_stats = []
        total_records = 0
        
        # 获取数据库连接
        from app import get_db_connection as get_conn
        engine = get_conn()
        
        with engine.connect() as conn:
            for table in tables:
                try:
                    # 获取县区代码和名称
                    county_code = get_county_code_from_table(table)
                    county_name = get_county_name_from_code(county_code)
                    
                    # 获取表的统计数据
                    from src.utils.database_helpers import get_table_stats
                    stats = get_table_stats(table, engine)
                    
                    if stats and 'total_records' in stats:
                        data_count = stats['total_records']
                        count = stats.get('num_records', 0)
                        
                        # 获取表注释
                        table_comment = stats.get('table_comment', '')
                        
                        # 累加到总记录数
                        total_records += data_count
                        
                        # 添加统计信息
                        table_stats.append({
                            'table': table,
                            'name': table_comment or county_name,
                            'display_name': county_name,
                            'count': count,
                            'data_count': data_count
                        })
                except Exception as e:
                    logging.error(f"处理表 {table} 数据时出错: {str(e)}")
                    continue
            
        # 按数据量降序排序
        table_stats.sort(key=lambda x: x['data_count'], reverse=True)
        
        return jsonify({
            'total_records': total_records,
            'table_stats': table_stats
        })
    
    except Exception as e:
        logging.error(f"获取全市数据统计出错: {str(e)}")
        return jsonify({'error': str(e)}), 500

@data_display.route('/api/data_display/monthly_stats')
def get_monthly_stats_api():
    """获取每月入湖数据统计"""
    try:
        # 从请求参数获取年份，默认为当前年份
        try:
            year = int(request.args.get('year', datetime.now().year))
        except (ValueError, TypeError):
            year = datetime.now().year
            logging.warning(f"无效年份参数，使用当前年份: {year}")
        
        # 使用公共模块获取月度统计数据
        monthly_stats = get_monthly_stats(year)
        
        return jsonify({
            'year': year,
            'monthly_stats': monthly_stats
        })
    
    except Exception as e:
        logging.error(f"获取月度数据统计出错: {str(e)}")
        return jsonify({'error': str(e)}), 500

@data_display.route('/api/data_display/weekly_stats')
def get_weekly_stats_api():
    """获取上周入湖数据统计"""
    try:
        # 从请求参数获取年份和参考日期
        try:
            year = int(request.args.get('year', datetime.now().year))
        except (ValueError, TypeError):
            year = datetime.now().year
            logging.warning(f"无效年份参数，使用当前年份: {year}")
        
        # 获取参考日期，默认为当前日期
        specific_date_str = request.args.get('specific_date')
        if specific_date_str:
            try:
                reference_date = datetime.strptime(specific_date_str, '%Y-%m-%d')
            except ValueError:
                reference_date = datetime.now()
                logging.warning(f"无效日期格式，使用当前日期: {reference_date}")
        else:
            # 如果是历史年份，使用该年5月13日作为参考日期
            if year != datetime.now().year:
                reference_date = datetime(year, 5, 13)
            else:
                reference_date = datetime.now()
        
        # 计算上周的日期范围
        start_date, end_date = calculate_date_range(reference_date, 'week')
        
        # 计算上周全市数据量
        total_count = calculate_city_weekly_data(reference_date)
        
        return jsonify({
            'year': year,
            'reference_date': reference_date.strftime('%Y-%m-%d'),
            'start_date': start_date,
            'end_date': end_date,
            'total_count': total_count
        })
    
    except Exception as e:
        logging.error(f"获取上周数据统计出错: {str(e)}")
        return jsonify({'error': str(e)}), 500

@data_display.route('/api/data_display/yearly_total_stats')
def get_yearly_total_stats():
    """获取指定年份截止时的数据总量统计"""
    try:
        try:
            year = int(request.args.get('year', datetime.now().year))
        except (ValueError, TypeError):
            year = datetime.now().year
            logging.warning(f"无效年份参数，使用当前年份: {year}")

        tables = get_data_tables()
        total_records = 0
        for table in tables:
            try:
                total_records += get_total_records(table, f"{year}-12-31")
            except Exception as e:
                logging.error(f"统计表 {table} 数据时出错: {str(e)}")
                continue

        total_with_fixed = add_fixed_value_to_total(total_records, year)
        return jsonify({
            'year': year,
            'total_records': total_with_fixed
        })
    except Exception as e:
        logging.error(f"获取截止{year}年数据总量统计出错: {str(e)}")
        return jsonify({'error': str(e)}), 500 