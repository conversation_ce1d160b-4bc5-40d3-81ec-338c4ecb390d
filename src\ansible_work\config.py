from db_config import ANSIBLE_DB_URI
from src.utils.config_manager import config_manager

class Config:
    # 从配置管理器获取安全配置
    _security_config = config_manager.get_security_config()
    _ansible_config = config_manager.get_ansible_config()

    SECRET_KEY = _security_config['secret_key']
    SQLALCHEMY_DATABASE_URI = ANSIBLE_DB_URI
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Ansible配置（从配置管理器获取）
    ANSIBLE_HOST = _ansible_config['ansible_host']
    JUMP_HOST = _ansible_config['jump_host']
    JUMP_PORT = _ansible_config['jump_port']
    ANSIBLE_PORT = _ansible_config['ansible_port']