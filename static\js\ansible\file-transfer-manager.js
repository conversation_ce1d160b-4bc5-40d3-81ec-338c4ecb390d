/**
 * 文件传输管理器
 * 
 * 负责管理文件上传和下载任务，显示进度，支持后台传输
 */

class FileTransferManager {
    constructor() {
        this.transfers = [];
        this.nextId = 1;
        this.containerSelector = '#file-transfer-container';
        
        this.init();
    }
    
    init() {
        if (!document.querySelector(this.containerSelector)) {
            const container = document.createElement('div');
            container.id = 'file-transfer-container';
            container.className = 'file-transfer-container';
            container.innerHTML = `
                <div class="transfer-header">
                    <span>文件传输管理器</span>
                    <button class="minimize-btn"><i class="fas fa-minus"></i></button>
                </div>
                <div class="transfer-list"></div>
            `;
            document.body.appendChild(container);
            
            // 添加事件监听
            const minimizeBtn = container.querySelector('.minimize-btn');
            minimizeBtn.addEventListener('click', () => {
                container.classList.toggle('minimized');
                minimizeBtn.innerHTML = container.classList.contains('minimized') ? 
                    '<i class="fas fa-plus"></i>' : '<i class="fas fa-minus"></i>';
            });
        }
        
        // 添加样式
        if (!document.getElementById('file-transfer-styles')) {
            const style = document.createElement('style');
            style.id = 'file-transfer-styles';
            style.textContent = `
                .file-transfer-container {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 350px;
                    max-height: 400px;
                    background: #fff;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                    z-index: 9999;
                    overflow: hidden;
                    transition: all 0.3s ease;
                    display: flex;
                    flex-direction: column;
                }
                
                .file-transfer-container.minimized {
                    height: 40px;
                }
                
                .transfer-header {
                    padding: 10px;
                    background: #f5f5f5;
                    border-bottom: 1px solid #ddd;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-weight: bold;
                }
                
                .transfer-list {
                    max-height: 350px;
                    overflow-y: auto;
                    display: block;
                    flex: 1;
                    padding: 0;
                }
                
                .file-transfer-container.minimized .transfer-list {
                    display: none;
                }
                
                .transfer-item {
                    padding: 12px;
                    border-bottom: 1px solid #eee;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                }
                
                .transfer-item:last-child {
                    border-bottom: none;
                }
                
                .transfer-status-icon {
                    margin-right: 10px;
                    font-size: 16px;
                    color: #666;
                    width: 20px;
                    text-align: center;
                }
                
                .transfer-info {
                    flex: 1;
                    overflow: hidden;
                }
                
                .transfer-title {
                    font-weight: bold;
                    margin-bottom: 5px;
                    display: flex;
                    justify-content: space-between;
                    font-size: 14px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                
                .transfer-status-text {
                    font-size: 12px;
                    color: #666;
                    margin-bottom: 5px;
                }
                
                .transfer-progress-container {
                    height: 5px;
                    background: #f0f0f0;
                    border-radius: 2px;
                    margin: 5px 0;
                    overflow: hidden;
                }
                
                .transfer-progress-bar {
                    height: 100%;
                    background: #4CAF50;
                    border-radius: 2px;
                    width: 0%;
                    transition: width 0.3s ease;
                }
                
                .transfer-progress-text {
                    font-size: 11px;
                    color: #666;
                    text-align: right;
                }
                
                .transfer-actions {
                    margin-left: 10px;
                    display: flex;
                    gap: 5px;
                }
                
                .transfer-actions button {
                    padding: 2px 5px;
                    font-size: 12px;
                }
                
                .transfer-completed {
                    background-color: #f9f9f9;
                }
                
                .transfer-failed {
                    background-color: #fff6f6;
                }
                
                .transfer-unknown {
                    background-color: #f5f5f5;
                }
                
                .minimize-btn {
                    background: none;
                    border: none;
                    color: #666;
                    cursor: pointer;
                }
                
                .empty-transfers {
                    padding: 15px;
                    text-align: center;
                    color: #999;
                    font-style: italic;
                }
                
                .file-transfer-notification {
                    position: fixed;
                    bottom: 20px;
                    left: 20px;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 4px;
                    max-width: 300px;
                    z-index: 9999;
                    animation: notification-fade-in 0.3s;
                    transition: opacity 0.3s, transform 0.3s;
                }
                
                .notification-hide {
                    opacity: 0;
                    transform: translateY(10px);
                }
                
                @keyframes notification-fade-in {
                    from {
                        opacity: 0;
                        transform: translateY(10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        // 初始化触发一次更新，清空任何可能的旧传输
        this.updateTransferList();
        
        // 显示传输管理器
        this.showTransferManager();
    }
    
    /**
     * 添加上传任务
     */
    addUpload(file, url, options = {}) {
        const id = this.nextId++;
        const transfer = {
            id,
            type: 'upload',
            file: typeof file === 'string' ? file : file.name,
            size: file.size || 0,
            url,
            status: 'pending',
            progress: 0,
            speed: 0,
            startTime: null,
            xhr: null,
            options
        };
        
        this.transfers.push(transfer);
        this.renderTransfer(transfer);
        this.startUpload(transfer, file);
        
        return id;
    }
    
    /**
     * 添加上传任务
     */
    addUpload(filename, fileSize, destinationPath, overwrite = false) {
        const id = this.nextId++;
        const transfer = {
            id,
            type: 'upload',
            file: filename,
            fileSize: fileSize,
            destinationPath: destinationPath,
            overwrite: overwrite,
            status: 'pending',
            progress: 0,
            speed: 0,
            startTime: Date.now(),
            taskId: null // 后端任务ID
        };

        this.transfers.push(transfer);
        this.renderTransfer(transfer);
        this.updateCounter();

        return id;
    }

    /**
     * 开始上传任务
     */
    startUpload(transfer, file) {
        if (!transfer || !file) {
            console.error('startUpload: 缺少必要参数');
            return;
        }

        console.log('开始上传:', transfer.file);

        // 更新状态为运行中
        transfer.status = 'running';
        transfer.startTime = Date.now();
        this.updateTransferUI(transfer);

        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('path', transfer.destinationPath);
        formData.append('overwrite', 'true'); // 强制覆盖

        console.log('上传参数:', {
            filename: file.name,
            path: transfer.destinationPath,
            overwrite: transfer.overwrite
        });

        // 发起异步上传请求
        fetch('/ansible/api/file-manager/upload-async', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                const taskId = result.task_id;
                transfer.taskId = taskId;

                console.log('上传任务已创建:', taskId);

                // 开始轮询状态
                this.pollUploadStatus(transfer);
            } else {
                transfer.status = 'error';
                transfer.error = result.message || '上传失败';
                this.updateTransferUI(transfer);
                this.showNotification('上传失败', result.message || '上传失败');
            }
        })
        .catch(error => {
            console.error('上传请求失败:', error);
            transfer.status = 'error';
            transfer.error = error.message;
            this.updateTransferUI(transfer);
            this.showNotification('上传失败', error.message);
        });
    }

    /**
     * 轮询上传状态
     */
    pollUploadStatus(transfer) {
        if (!transfer.taskId) {
            console.error('pollUploadStatus: 缺少 taskId');
            return;
        }

        const pollInterval = setInterval(async () => {
            try {
                const response = await fetch(`/ansible/api/file-manager/upload-status/${transfer.taskId}`, {
                    credentials: 'same-origin'
                });

                const result = await response.json();

                if (result.success && result.status) {
                    // 更新传输状态
                    transfer.progress = result.status.progress || 0;
                    transfer.speed = result.status.speed || 0;

                    // 映射状态
                    switch (result.status.status) {
                        case 'pending':
                            transfer.status = 'pending';
                            break;
                        case 'uploading':
                            transfer.status = 'running';
                            break;
                        case 'completed':
                            transfer.status = 'completed';
                            clearInterval(pollInterval);
                            this.showNotification('上传完成', `文件 ${transfer.file} 上传完成`);
                            // 刷新文件列表
                            if (typeof loadFileList === 'function') {
                                loadFileList(currentPath);
                            }
                            break;
                        case 'failed':
                            transfer.status = 'error';
                            transfer.error = result.status.error_message || '上传失败';
                            clearInterval(pollInterval);
                            this.showNotification('上传失败', `文件 ${transfer.file} 上传失败: ${transfer.error}`);
                            break;
                        case 'cancelled':
                            transfer.status = 'cancelled';
                            clearInterval(pollInterval);
                            break;
                    }

                    this.updateTransferUI(transfer);
                }
            } catch (error) {
                console.error('轮询上传状态失败:', error);
                clearInterval(pollInterval);
                transfer.status = 'error';
                transfer.error = '状态查询失败';
                this.updateTransferUI(transfer);
            }
        }, 2000); // 每2秒轮询一次

        // 保存轮询定时器以便取消
        transfer.pollTimer = pollInterval;
    }

    /**
     * 添加下载任务
     */
    addDownload(url, filename, options = {}) {
        const id = this.nextId++;
        const transfer = {
            id,
            type: 'download',
            file: filename,
            url,
            status: 'pending',
            progress: 0,
            speed: 0,
            startTime: null,
            xhr: null,
            options
        };

        this.transfers.push(transfer);
        this.renderTransfer(transfer);
        this.updateCounter();

        // 检查是否需要使用POST请求或其他自定义选项
        const hasCustomRequestOptions = options.method || options.headers || options.body;

        if (hasCustomRequestOptions) {
            // 使用fetch API进行自定义请求
            this.startCustomDownload(transfer);
        } else {
            // 使用默认的iframe下载方式
            this.directDownload(transfer);
        }

        return id;
    }
    
    /**
     * 直接下载文件（使用iframe方式）
     */
    directDownload(transfer) {
        // 更新状态
        transfer.status = 'running';
        transfer.startTime = Date.now();
        this.updateTransferUI(transfer);
        
        // 创建隐藏的iframe来处理下载
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = transfer.url;
        document.body.appendChild(iframe);
        
        // 短暂延迟后移除iframe
        setTimeout(() => {
            document.body.removeChild(iframe);
            
            // 由于iframe下载无法实时追踪进度，我们在一定时间后将状态设置为完成
            setTimeout(() => {
                this.completeTransfer(transfer);
            }, 2000);
        }, 1000);
        
        // 显示通知
        this.showNotification('下载开始', `文件 ${transfer.file} 开始下载`);
    }
    
    /**
     * 启动自定义下载请求（支持POST等方法）
     */
    startCustomDownload(transfer) {
        const options = transfer.options || {};
        const requestOptions = {
            method: options.method || 'GET',
            headers: options.headers || {},
            body: options.body || null,
            credentials: 'same-origin'
        };
        
        transfer.status = 'running';
        transfer.startTime = Date.now();
        this.updateTransferUI(transfer);
        
        // 显示通知
        this.showNotification(`${transfer.file} 开始下载`, '下载任务已加入队列');
        
        fetch(transfer.url, requestOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`下载失败: ${response.status} ${response.statusText}`);
                }
                
                return response.blob();
            })
            .then(blob => {
                // 创建下载链接
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = transfer.file;
                document.body.appendChild(a);
                a.click();
                URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                // 完成下载
                this.completeTransfer(transfer);
            })
            .catch(error => {
                console.error('下载失败:', error);
                this.failTransfer(transfer, error.message);
            });
    }
    
    /**
     * 开始上传文件
     */
    startUpload(transfer, file) {
        const xhr = new XMLHttpRequest();
        transfer.xhr = xhr;
        transfer.startTime = Date.now();
        transfer.status = 'running';
        
        xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
                this.updateProgress(transfer, event.loaded, event.total);
            }
        });
        
        xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                this.completeTransfer(transfer);
            } else {
                this.failTransfer(transfer, `上传失败: ${xhr.status}`);
            }
        });
        
        xhr.addEventListener('error', () => {
            this.failTransfer(transfer, '网络错误');
        });
        
        xhr.addEventListener('abort', () => {
            transfer.status = 'cancelled';
            this.updateTransferUI(transfer);
        });
        
        xhr.open('POST', transfer.url);
        
        // 设置自定义请求头
        if (transfer.options.headers) {
            Object.keys(transfer.options.headers).forEach(key => {
                xhr.setRequestHeader(key, transfer.options.headers[key]);
            });
        }
        
        // 默认不设置 Content-Type，让浏览器自动设置以包含 boundary
        
        const formData = new FormData();
        
        // 添加其他表单字段
        if (transfer.options.data) {
            Object.keys(transfer.options.data).forEach(key => {
                formData.append(key, transfer.options.data[key]);
            });
        }
        
        // 添加文件
        formData.append(transfer.options.fileFieldName || 'file', file);
        
        xhr.send(formData);
        this.updateTransferUI(transfer);
    }
    
    /**
     * 更新进度信息
     */
    updateProgress(transfer, loaded, total) {
        const now = Date.now();
        const timeElapsed = (now - transfer.startTime) / 1000; // 转换为秒
        
        if (timeElapsed > 0) {
            transfer.speed = loaded / timeElapsed; // 字节/秒
        }
        
        transfer.progress = (loaded / total) * 100;
        this.updateTransferUI(transfer);
    }
    
    /**
     * 完成传输
     */
    completeTransfer(transfer) {
        transfer.status = 'completed';
        transfer.progress = 100;
        this.updateTransferUI(transfer);
        
        // 安全地调用回调函数
        if (transfer.options && transfer.options.onComplete) {
            transfer.options.onComplete(transfer);
        }
        
        // 显示通知
        this.showNotification(`${transfer.type === 'upload' ? '上传' : '下载'}完成`, 
            `文件 ${transfer.file} 已${transfer.type === 'upload' ? '上传' : '下载'}完成`);
    }
    
    /**
     * 传输失败
     */
    failTransfer(transfer, error) {
        transfer.status = 'error';
        transfer.error = error;
        this.updateTransferUI(transfer);
        
        // 安全地调用回调函数
        if (transfer.options && transfer.options.onError) {
            transfer.options.onError(transfer, error);
        }
        
        // 显示通知
        this.showNotification(`${transfer.type === 'upload' ? '上传' : '下载'}失败`, 
            `文件 ${transfer.file} ${transfer.type === 'upload' ? '上传' : '下载'}失败: ${error}`);
    }
    
    /**
     * 取消传输
     */
    cancelTransfer(id) {
        const transfer = this.transfers.find(t => t.id === id);
        if (!transfer) return;

        if (transfer.type === 'upload') {
            // 取消上传任务
            if (transfer.taskId) {
                fetch(`/ansible/api/file-manager/upload-cancel/${transfer.taskId}`, {
                    method: 'POST',
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        console.log('上传任务已取消:', transfer.taskId);
                    }
                })
                .catch(error => {
                    console.error('取消上传任务失败:', error);
                });
            }

            // 停止轮询
            if (transfer.pollTimer) {
                clearInterval(transfer.pollTimer);
                transfer.pollTimer = null;
            }

            transfer.status = 'cancelled';
            this.updateTransferUI(transfer);
            this.showNotification('上传已取消', `文件 ${transfer.file} 的上传已取消`);
        } else {
            // 取消下载任务
            if (transfer.status === 'running' && transfer.xhr) {
                transfer.xhr.abort();
                transfer.status = 'cancelled';
                this.updateTransferUI(transfer);

                // 安全地调用回调函数
                if (transfer.options && transfer.options.onCancel) {
                    transfer.options.onCancel(transfer);
                }
            }
        }
    }
    
    /**
     * 渲染传输项
     */
    renderTransfer(transfer) {
        const container = document.querySelector(`${this.containerSelector} .transfer-list`);
        if (!container) return;
        
        const existing = document.getElementById(`transfer-${transfer.id}`);
        if (existing) {
            // 更新已有元素
            this.updateTransferUI(transfer);
            return;
        }
        
        // 创建新元素
        const item = document.createElement('div');
        item.id = `transfer-${transfer.id}`;
        item.className = `transfer-item transfer-${transfer.status}`;

        // 根据类型显示不同的图标
        const typeIcon = transfer.type === 'upload' ? 'bi-cloud-upload' : 'bi-cloud-download';
        const typeText = transfer.type === 'upload' ? '上传' : '下载';

        // 格式化文件大小
        const formatFileSize = (bytes) => {
            if (!bytes) return '';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        item.innerHTML = `
            <div class="transfer-title">
                <span>
                    <i class="bi ${typeIcon} me-1"></i>
                    ${transfer.file}
                    ${transfer.fileSize ? `<small class="text-muted ms-2">(${formatFileSize(transfer.fileSize)})</small>` : ''}
                </span>
                <span class="transfer-action" data-action="cancel">
                    <i class="fas fa-times"></i>
                </span>
            </div>
            <div class="transfer-progress">
                <div class="transfer-progress-bar" style="width: ${transfer.progress || 0}%"></div>
            </div>
            <div class="transfer-info">
                <span class="transfer-status">${typeText} - ${this.getStatusText(transfer)}</span>
                <span class="transfer-speed">${this.formatSpeed(transfer.speed)}</span>
            </div>
        `;
        
        // 添加点击事件
        const cancelBtn = item.querySelector('[data-action="cancel"]');
        cancelBtn.addEventListener('click', () => this.cancelTransfer(transfer.id));
        
        container.appendChild(item);
    }
    
    /**
     * 更新传输项UI
     */
    updateTransferUI(transfer) {
        if (!transfer) {
            console.error('尝试更新未定义的传输对象');
            return;
        }

        const item = document.getElementById(`transfer-${transfer.id}`);
        if (!item) {
            this.renderTransfer(transfer);
            return;
        }
        
        // 更新类名
        item.className = `transfer-item transfer-${transfer.status || 'unknown'}`;
        
        // 更新进度条
        const progressBar = item.querySelector('.transfer-progress-bar');
        if (progressBar) {
            progressBar.style.width = `${transfer.progress || 0}%`;
        }
        
        // 更新状态文本 - 优先使用转移对象上的自定义状态文本
        const statusEl = item.querySelector('.transfer-status');
        if (statusEl) {
            // 如果有自定义状态文本，优先使用
            if (transfer.statusText) {
                statusEl.textContent = transfer.statusText;
            } else {
                statusEl.textContent = this.getStatusText(transfer) || '未知状态';
            }
        }
        
        // 更新速度显示
        const speedEl = item.querySelector('.transfer-speed');
        if (speedEl) {
            speedEl.textContent = (transfer.status === 'running' || transfer.status === 'downloading') ? 
                this.formatSpeed(transfer.speed || 0) : '';
        }
        
        // 更新取消按钮
        const actionBtn = item.querySelector('.transfer-action');
        if (actionBtn) {
            if (transfer.status === 'running' || transfer.status === 'pending' || 
                transfer.status === 'downloading' || transfer.status === 'waiting' ||
                transfer.status === 'waiting_for_save') {
                actionBtn.innerHTML = '<i class="fas fa-times"></i>';
                actionBtn.setAttribute('data-action', 'cancel');
            } else {
                actionBtn.innerHTML = '<i class="fas fa-trash"></i>';
                actionBtn.setAttribute('data-action', 'remove');
            }
        }
    }
    
    /**
     * 获取状态文本
     */
    getStatusText(transfer) {
        if (!transfer || !transfer.status) {
            return '未知状态';
        }
        
        switch (transfer.status) {
            case 'waiting':
                return '等待用户操作...';
            case 'waiting_for_save':
                return '请在弹出对话框中点击"保存"按钮...';
            case 'verifying':
                return '正在验证下载状态...';
            case 'pending_confirmation':
                return '请确认是否已点击"保存"按钮?';
            case 'pending':
                return '等待中...';
            case 'preparing':
                return '准备下载...';
            case 'running':
                if (transfer.type === 'upload') {
                    return `上传中 - ${this.formatSpeed(transfer.speed || 0)}`;
                }
                return `下载中 - ${this.formatSpeed(transfer.speed || 0)}`;
            case 'downloading':
                return `下载中 - ${this.formatSpeed(transfer.speed || 0)}`;
            case 'completed':
                return '已完成';
            case 'error':
                return `失败: ${transfer.error || '未知错误'}`;
            case 'canceled':
            case 'cancelled':
                return '已取消';
            case 'failed':
                return `失败: ${transfer.errorMessage || transfer.error || '未知错误'}`;
            default:
                return '未知状态';
        }
    }
    
    /**
     * 格式化速度
     */
    formatSpeed(bytesPerSecond) {
        if (bytesPerSecond === undefined || bytesPerSecond === null || bytesPerSecond === 0) {
            return '0 B/s';
        }
        
        const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
        let size = bytesPerSecond;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }
    
    /**
     * 显示通知
     */
    showNotification(title, message, silent = false) {
        // 检查是否支持Notification API
        if (!silent && 'Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: '/ansible/static/img/file-icon.png'
            });
            setTimeout(() => notification.close(), 5000);
        }
        
        // 在页面中显示通知
        const container = document.createElement('div');
        container.className = 'file-transfer-notification';
        container.innerHTML = `
            <div class="notification-content">
                <strong>${title}</strong>
                <p>${message}</p>
            </div>
        `;
        document.body.appendChild(container);
        
        // 3秒后自动移除
        setTimeout(() => {
            container.classList.add('notification-hide');
            setTimeout(() => document.body.removeChild(container), 300);
        }, 3000);
    }
    
    /**
     * 等待用户保存文件的操作
     * @param {Object} transfer - 传输记录对象
     * @param {string} downloadUrl - 下载链接URL
     * @returns {Promise<boolean>} - 返回用户是否成功保存
     */
    waitForUserSaveAction(transfer, downloadUrl) {
        return new Promise((resolve) => {
            // 获取返回的Blob链接
            const link = document.createElement('a');
            link.href = downloadUrl;
            const filename = transfer.files && transfer.files.length > 1 
                ? `批量下载_${new Date().getTime()}.tar.gz` 
                : (transfer.files && transfer.files[0].name) || '未知文件.txt';
            link.download = filename;
            
            // 更新传输对象的文件名
            transfer.filename = filename;
            
            // 显示准备下载的通知
            this.showNotification('批量下载准备中', '请在弹出的对话框中选择保存位置', false);
            
            // 更新传输状态为waiting，表示等待用户操作
            transfer.status = 'waiting';
            transfer.progress = 0;
            this.updateTransferList();
            this.updateTransferUI(transfer);
            
            // 点击链接开始下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 更新状态为downloading，表示正在下载
            transfer.status = 'downloading';
            transfer.progress = 5; // 立即显示一点进度
            this.updateTransferList();
            this.updateTransferUI(transfer);
            
            // 设置最大等待时间
            const maxWaitTime = 20000; // 20秒最长等待时间
            const timeoutId = setTimeout(() => {
                // 如果下载仍在准备或下载状态，认为已取消或超时
                if (transfer.status === 'waiting' || transfer.status === 'downloading' && transfer.progress < 90) {
                    transfer.status = 'failed';
                    transfer.error = '下载超时，可能被取消';
                    this.updateTransferList();
                    this.updateTransferUI(transfer);
                    this.showNotification('下载超时', '下载操作超时，可能被取消', false);
                    
                    // 释放URL
                    URL.revokeObjectURL(downloadUrl);
                    
                    resolve(false);
                }
            }, maxWaitTime);
            
            // 快速显示下载进度 - 减少等待时间为3秒
            setTimeout(() => {
                // 只有当状态是downloading时继续
                if (transfer.status === 'downloading' || transfer.status === 'waiting') {
                    // 确保状态为downloading
                    transfer.status = 'downloading';
                    
                    // 立即更新进度到50%，表示已经开始下载
                    transfer.progress = 50;
                    this.updateTransferList();
                    this.updateTransferUI(transfer);
                    
                    // 显示开始下载的通知
                    this.showNotification('批量下载进行中', `文件 "${filename}" 正在下载...`, false);
                    
                    // 强制显示传输管理器
                    this.showTransferManager();
                    
                    // 快速跳到85%进度
                    setTimeout(() => {
                        transfer.progress = 85;
                        this.updateTransferList();
                        this.updateTransferUI(transfer);
                        
                        // 再快速跳到完成
                        setTimeout(() => {
                            transfer.progress = 100;
                            transfer.status = 'completed';
                            
                            // 记录结束时间
                            transfer.endTime = Date.now();
                            
                            // 释放URL
                            URL.revokeObjectURL(downloadUrl);
                            
                            // 清除超时计时器
                            clearTimeout(timeoutId);
                            
                            this.showNotification('下载完成', `文件 "${filename}" 已成功下载`, true);
                            
                            // 最后更新UI
                            this.updateTransferList();
                            this.updateTransferUI(transfer);
                            
                            resolve(true);
                        }, 1000); // 1秒后完成
                    }, 1000); // 1秒后到85%
                }
            }, 3000); // 3秒后显示进度（而不是之前的15秒）
        });
    }
    
    /**
     * 从路径中提取文件名
     * @param {string} path - 文件路径
     * @returns {string} - 文件名
     */
    getFileNameFromPath(path) {
        if (!path) return '未命名文件';
        const parts = path.split(/[\/\\]/);
        return parts[parts.length - 1] || '未命名文件';
    }
    
    /**
     * 清除完成的传输
     */
    clearCompleted() {
        this.transfers = this.transfers.filter(t => 
            t.status !== 'completed' && t.status !== 'error' && t.status !== 'cancelled');
        
        document.querySelectorAll('.transfer-item.transfer-completed, .transfer-item.transfer-error, .transfer-item.transfer-cancelled').forEach(el => {
            el.remove();
        });
    }
    
    /**
     * 添加下载占位符，显示下载准备中的状态
     */
    addPlaceholderDownload(id, filename) {
        const transfer = {
            id,
            type: 'download',
            file: filename,
            status: 'waiting', // 使用waiting状态表示等待用户保存文件
            progress: 0,
            speed: 0,
            startTime: null,
            estimatedSize: Math.floor(Math.random() * 10000000) + 1000000, // 预估文件大小，用于UI显示
            errorMessage: null,
            // 添加files数组，以便waitForUserSaveAction方法可以使用
            files: [{
                name: filename,
                path: filename,
                size: Math.floor(Math.random() * 10000000) + 1000000
            }]
        };
        
        this.transfers.push(transfer);
        this.renderTransfer(transfer);
        
        // 显示通知
        this.showNotification('准备下载', `文件 ${filename} 准备下载中，请在打开的保存对话框中选择保存位置`);
        
        return id;
    }
    
    /**
     * 模拟下载进度
     * @param {string} id 传输ID
     * @param {string} filename 文件名
     */
    async simulateDownloadProgress(id, filename) {
        const transfer = this.transfers.find(t => t.id === id);
        if (!transfer) return;
        
        // 等待用户保存文件操作完成
        const userSaved = await this.waitForUserSaveAction(transfer, transfer.url);
        
        if (!userSaved) {
            // 用户可能取消了下载，不继续模拟进度
            transfer.status = 'failed';
            transfer.errorMessage = '下载已取消或超时';
            this.updateTransferUI(transfer);
            return;
        }
        
        // 更新状态为进行中
        transfer.status = 'running';
        transfer.startTime = Date.now();
        transfer.lastUpdateTime = Date.now();
        transfer.bytesLoaded = 0;
        this.updateTransferUI(transfer);
        
        // 显示下载开始通知
        this.showNotification('下载开始', `文件 ${filename} 开始下载`);
        
        // 随机生成文件大小 (1 MB 到 100 MB)
        const totalSize = transfer.estimatedSize || (Math.floor(Math.random() * 99000000) + 1000000);
        
        // 模拟下载进度更新
        const simulateInterval = setInterval(() => {
            // 计算自上次更新以来的时间（秒）
            const now = Date.now();
            const timeDiff = (now - transfer.lastUpdateTime) / 1000;
            
            // 随机增加进度，模拟网络波动
            const progressIncrement = Math.random() * 0.07 + 0.03; // 3% 到 10% 之间的随机增量
            let newProgress = transfer.progress + progressIncrement;
            
            // 确保进度不超过 99% 直到最后一次更新
            if (newProgress >= 0.99) {
                if (Math.random() < 0.2) { // 20% 的概率完成下载
                    newProgress = 1.0;
                    transfer.progress = 1.0;
                    transfer.status = 'completed';
                    clearInterval(simulateInterval);
                    
                    // 显示下载完成通知
                    this.showNotification('下载完成', `文件 ${filename} 已下载完成`);
                } else {
                    newProgress = 0.99;
                }
            }
            
            // 计算这次增加的字节数
            const previousBytes = transfer.bytesLoaded || 0;
            transfer.bytesLoaded = Math.floor(totalSize * newProgress);
            const bytesIncrement = transfer.bytesLoaded - previousBytes;
            
            // 计算下载速度 (bytes/second)
            const speed = bytesIncrement / timeDiff;
            
            // 更新传输对象
            transfer.progress = newProgress;
            transfer.speed = speed;
            transfer.lastUpdateTime = now;
            
            // 更新UI
            this.updateTransferUI(transfer);
        }, 800);
    }

    /**
     * 批量下载文件
     * @param {Array<Object>} files - 需要下载的文件数组
     */
    batchDownloadFiles(files) {
        try {
            if (!files || files.length === 0) {
                this.showNotification('下载错误', '未选择任何文件', false);
                return;
            }
            
            console.log('开始批量下载文件:', files);
            
            // 创建传输记录
            const transferId = Date.now();
            const transfer = {
                id: transferId,
                type: 'download',
                startTime: new Date(),
                status: 'preparing',
                progress: 0,
                files: files.map(file => ({
                    path: file.path,
                    name: this.getFileNameFromPath(file.path),
                    size: file.size || 0,
                    downloaded: 0
                }))
            };
            
            // 更新文件名
            const fileName = files.length > 1 
                ? `批量下载_${new Date().getTime()}.tar.gz` 
                : files[0].name;
            transfer.file = fileName;
            
            // 添加到传输列表并立即显示
            this.transfers.push(transfer);
            this.showTransferManager();
            this.updateTransferList();
            this.updateTransferUI(transfer);
            
            // 通知用户正在准备下载
            this.showNotification('准备下载', `正在准备 ${files.length} 个文件的批量下载...`, false);
            
            // 记录下载开始时间，用于跟踪经过时间
            transfer.startPreparationTime = Date.now();
            
            // 发送请求准备批量下载 - 但不立即触发浏览器的下载行为
            fetch('/ansible/api/file-manager/batch-download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    paths: files.map(file => file.path)
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误：${response.status}`);
                }
                
                // 记录服务器响应时间
                transfer.serverResponseTime = Date.now();
                
                return response.blob();
            })
            .then(blob => {
                // 创建下载链接但不立即下载
                const url = URL.createObjectURL(blob);
                transfer.blobUrl = url; // 保存URL以便后续清理
                
                // 更新传输状态为"准备完成，等待用户操作"
                transfer.status = 'waiting';
                transfer.progress = 30;  // 显示更高的初始进度，表示已经完成了大部分准备工作
                this.updateTransferList();
                this.updateTransferUI(transfer);
                
                // 显示传输管理器
                this.showTransferManager();
                
                // 监听浏览器下载事件(虽然浏览器安全限制可能使这不太可靠)
                if (navigator.serviceWorker && navigator.serviceWorker.controller) {
                    // 如果有Service Worker，可以尝试通过它监听下载
                    console.log('尝试通过Service Worker监听下载');
                }
                
                // 创建并点击下载链接，弹出保存对话框
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName;
                document.body.appendChild(link);
                
                // 记录下载链接创建时间
                transfer.linkCreatedTime = Date.now();
                
                // 准备点击链接显示保存对话框
                setTimeout(() => {
                    // 显示明确等待用户操作的状态
                    transfer.status = 'waiting_for_save';
                    transfer.statusText = '请点击"保存"按钮开始下载...';
                    this.updateTransferList();
                    this.updateTransferUI(transfer);
                    
                    // 点击链接显示保存对话框
                    link.click();
                    
                    // 记录点击时间
                    transfer.linkClickedTime = Date.now();
                    
                    // 移除链接元素
                    document.body.removeChild(link);
                    
                    // 创建一个下载监听器，尝试检测下载完成
                    try {
                        if (window.navigator && window.navigator.serviceWorker) {
                            console.log('尝试注册下载完成检测');
                        }
                    } catch (e) {
                        console.log('无法注册下载监听:', e);
                    }
                    
                    // 开始监控保存对话框状态
                    setTimeout(() => {
                        this.monitorUserSaveAction(transfer, url);
                    }, 500); // 等待0.5秒，确保保存对话框已显示
                }, 100);
            })
            .catch(error => {
                console.error('批量下载错误:', error);
                transfer.status = 'failed';
                transfer.error = error.message || '下载过程中发生错误';
                
                // 更新UI并显示
                this.updateTransferList();
                this.updateTransferUI(transfer);
                this.showTransferManager();
                
                this.showNotification('下载错误', `批量下载失败: ${error.message || '未知错误'}`, false);
                
                // 尝试释放可能创建的 URL
                if (transfer.blobUrl) {
                    URL.revokeObjectURL(transfer.blobUrl);
                }
            });
        } catch (error) {
            console.error('批量下载过程中发生异常:', error);
            this.showNotification('下载错误', `批量下载失败: ${error.message || '未知错误'}`, false);
        }
    }

    /**
     * 监控用户保存操作
     * @param {Object} transfer - 传输对象
     * @param {string} url - 下载URL
     */
    monitorUserSaveAction(transfer, url) {
        // 记录监控开始时间
        transfer.monitorStartTime = Date.now();
        
        // 更新状态文本，但保持waiting_for_save状态
        transfer.statusText = '正在等待您点击"保存"按钮...';
        this.updateTransferList();
        this.updateTransferUI(transfer);
        
        // 创建一个监听函数，用于检测浏览器焦点变化
        // 当用户在保存对话框中操作完成并返回浏览器时，可能会触发焦点事件
        const focusListener = () => {
            // 记录焦点变化时间
            const focusChangeTime = Date.now();
            
            // 如果焦点变化距离监控开始已经过了足够长的时间（至少1秒），我们假设用户已经处理了保存对话框
            if (focusChangeTime - transfer.monitorStartTime > 1000 && transfer.status === 'waiting_for_save') {
                console.log('检测到窗口焦点返回，可能用户已操作保存对话框');
                
                // 更新状态为正在处理下载
                transfer.status = 'verifying';
                transfer.progress = 60;
                transfer.statusText = '正在开始下载...';
                this.updateTransferList();
                this.updateTransferUI(transfer);
                
                // 几乎立即开始显示下载进度，因为文件实际上已经下载
                setTimeout(() => {
                    // 开始显示下载进度
                    this.startDownloadProgress(transfer, url);
                }, 300);
                
                // 移除监听器，避免重复触发
                window.removeEventListener('focus', focusListener);
            }
        };
        
        // 添加焦点变化监听
        window.addEventListener('focus', focusListener);
        
        // 设置备用计时器 - 如果10秒后用户仍未操作，提供一个选项让用户确认下载状态
        setTimeout(() => {
            // 只有在当前状态仍为等待保存时才继续
            if (transfer.status === 'waiting_for_save') {
                // 移除监听器
                window.removeEventListener('focus', focusListener);
                
                // 更新状态
                transfer.status = 'pending_confirmation';
                transfer.progress = 40;
                transfer.statusText = '您是否已点击了"保存"按钮？';
                this.updateTransferList();
                this.updateTransferUI(transfer);
                
                // 添加用户确认按钮
                this.addUserConfirmationButtons(transfer, url);
            }
        }, 10000); // 10秒后询问用户(缩短了等待时间)
    }

    /**
     * 添加用户确认按钮
     * @param {Object} transfer - 传输对象
     * @param {string} url - 下载URL
     */
    addUserConfirmationButtons(transfer, url) {
        // 查找传输项
        const transferItem = document.getElementById(`transfer-${transfer.id}`);
        if (!transferItem) return;
        
        // 查找或创建操作容器
        let actionsContainer = transferItem.querySelector('.transfer-actions');
        if (!actionsContainer) {
            actionsContainer = document.createElement('div');
            actionsContainer.className = 'transfer-actions';
            transferItem.appendChild(actionsContainer);
        }
        
        // 清空现有内容
        actionsContainer.innerHTML = '';
        
        // 添加"已保存"按钮
        const savedButton = document.createElement('button');
        savedButton.className = 'btn btn-sm btn-success';
        savedButton.innerHTML = '我已点击保存';
        savedButton.addEventListener('click', () => {
            // 开始显示下载进度
            this.startDownloadProgress(transfer, url);
        });
        actionsContainer.appendChild(savedButton);
        
        // 添加"取消下载"按钮
        const cancelButton = document.createElement('button');
        cancelButton.className = 'btn btn-sm btn-danger ml-2';
        cancelButton.innerHTML = '取消下载';
        cancelButton.addEventListener('click', () => {
            transfer.status = 'cancelled';
            transfer.progress = 0;
            transfer.statusText = '下载已取消';
            this.updateTransferList();
            this.updateTransferUI(transfer);
            
            // 释放URL
            if (url) {
                URL.revokeObjectURL(url);
            }
            
            this.showNotification('下载已取消', `文件 "${transfer.file}" 下载已取消`, false);
        });
        actionsContainer.appendChild(cancelButton);
    }

    /**
     * 开始显示下载进度
     * @param {Object} transfer - 传输对象
     * @param {string} url - 下载URL
     */
    startDownloadProgress(transfer, url) {
        // 更新状态为下载中
        transfer.status = 'downloading';
        transfer.progress = 80; // 直接从高进度开始，因为实际上下载已经完成或接近完成
        transfer.statusText = '正在下载...';
        this.updateTransferList();
        this.updateTransferUI(transfer);
        
        // 记录下载开始时间
        transfer.downloadStartTime = Date.now();
        
        // 显示通知
        this.showNotification('正在下载', `文件 "${transfer.file}" 下载处理中`, false);
        
        // 由于浏览器已经开始下载，我们只需模拟一个非常短的下载过程
        setTimeout(() => {
            // 更新到90%
            transfer.progress = 95;
            this.updateTransferList();
            this.updateTransferUI(transfer);
            
            // 再延迟一小段时间后完成
            setTimeout(() => {
                // 完成下载
                transfer.progress = 100;
                transfer.status = 'completed';
                transfer.endTime = new Date();
                transfer.statusText = '下载完成';
                this.updateTransferList();
                this.updateTransferUI(transfer);
                
                // 释放URL
                if (url) {
                    URL.revokeObjectURL(url);
                }
                
                this.showNotification('下载完成', `文件 "${transfer.file}" 已成功下载`, false);
            }, 500); // 半秒后显示完成
        }, 300); // 0.3秒后显示高进度
    }

    /**
     * 更新传输列表UI
     */
    updateTransferList() {
        try {
            const containerSelector = this.containerSelector || '#file-transfer-container';
            const listContainer = document.querySelector(`${containerSelector} .transfer-list`);
            
            if (!listContainer) {
                console.warn('找不到传输列表容器:', containerSelector);
                return;
            }
            
            // 清空现有内容
            listContainer.innerHTML = '';
            
            if (!this.transfers || this.transfers.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-transfers';
                emptyMessage.textContent = '无传输任务';
                listContainer.appendChild(emptyMessage);
                return;
            }
            
            // 确保transfers是数组且有序
            if (!Array.isArray(this.transfers)) {
                console.error('transfers不是数组:', this.transfers);
                return;
            }
            
            // 对传输按照开始时间倒序排序（最新的在前面）
            const sortedTransfers = [...this.transfers].sort((a, b) => {
                if (!a.startTime) return 1;
                if (!b.startTime) return -1;
                return new Date(b.startTime) - new Date(a.startTime);
            });
            
            // 为每个传输创建UI元素
            sortedTransfers.forEach(transfer => {
                try {
                    if (!transfer) {
                        console.warn('跳过空传输对象');
                        return;
                    }
                    
                    const transferItem = document.createElement('div');
                    transferItem.className = `transfer-item transfer-${transfer.status || 'unknown'}`;
                    transferItem.setAttribute('data-id', transfer.id);
                    
                    // 创建状态图标
                    const statusIcon = document.createElement('div');
                    statusIcon.className = 'transfer-status-icon';
                    let iconClass = '';
                    
                    switch (transfer.status) {
                        case 'preparing':
                        case 'waiting':
                        case 'saving':
                            iconClass = 'fa-spinner fa-spin';
                            break;
                        case 'downloading':
                        case 'uploading':
                            iconClass = 'fa-clock';
                            break;
                        case 'completed':
                            iconClass = 'fa-check';
                            break;
                        case 'failed':
                            iconClass = 'fa-times';
                            break;
                        default:
                            iconClass = 'fa-question';
                    }
                    
                    statusIcon.innerHTML = `<i class="fas ${iconClass}"></i>`;
                    transferItem.appendChild(statusIcon);
                    
                    // 创建信息容器
                    const infoContainer = document.createElement('div');
                    infoContainer.className = 'transfer-info';
                    
                    // 添加标题
                    const titleText = transfer.type === 'download' ? '下载' : '上传';
                    const filename = transfer.filename || (transfer.files && transfer.files.length > 0 ? 
                        (transfer.files.length > 1 ? `${transfer.files.length} 个文件` : transfer.files[0].name) : 
                        (transfer.file || '未知文件'));
                        
                    const title = document.createElement('div');
                    title.className = 'transfer-title';
                    title.textContent = `${titleText}: ${filename}`;
                    infoContainer.appendChild(title);
                    
                    // 添加状态文本
                    const statusText = document.createElement('div');
                    statusText.className = 'transfer-status-text';
                    
                    let statusMessage = this.getStatusText(transfer) || '状态未知';
                    statusText.textContent = statusMessage;
                    infoContainer.appendChild(statusText);
                    
                    // 添加进度条
                    if (['downloading', 'uploading', 'completed', 'saving', 'waiting'].includes(transfer.status)) {
                        const progressContainer = document.createElement('div');
                        progressContainer.className = 'transfer-progress-container';
                        
                        const progressBar = document.createElement('div');
                        progressBar.className = 'transfer-progress-bar';
                        progressBar.style.width = `${transfer.progress || 0}%`;
                        
                        const progressText = document.createElement('div');
                        progressText.className = 'transfer-progress-text';
                        progressText.textContent = `${Math.floor(transfer.progress || 0)}%`;
                        
                        progressContainer.appendChild(progressBar);
                        progressContainer.appendChild(progressText);
                        infoContainer.appendChild(progressContainer);
                    }
                    
                    transferItem.appendChild(infoContainer);
                    
                    // 添加操作按钮
                    const actionContainer = document.createElement('div');
                    actionContainer.className = 'transfer-actions';
                    
                    // 只有失败的传输可以重试
                    if (transfer.status === 'failed') {
                        const retryButton = document.createElement('button');
                        retryButton.className = 'btn btn-sm btn-outline-primary';
                        retryButton.innerHTML = '<i class="fas fa-redo"></i> 重试';
                        retryButton.addEventListener('click', () => this.retryTransfer(transfer.id));
                        actionContainer.appendChild(retryButton);
                    }
                    
                    // 添加删除按钮
                    const deleteButton = document.createElement('button');
                    deleteButton.className = 'btn btn-sm btn-outline-danger';
                    deleteButton.innerHTML = '<i class="fas fa-trash-alt"></i> 删除';
                    deleteButton.addEventListener('click', () => this.removeTransfer(transfer.id));
                    actionContainer.appendChild(deleteButton);
                    
                    transferItem.appendChild(actionContainer);
                    
                    // 添加到容器
                    listContainer.appendChild(transferItem);
                } catch (innerError) {
                    console.error('处理单个传输项时出错:', innerError, transfer);
                }
            });
        } catch (error) {
            console.error('更新传输列表时出错:', error);
        }
    }
    
    /**
     * 重试传输
     * @param {number} transferId - 传输ID
     */
    retryTransfer(transferId) {
        const transfer = this.transfers.find(t => t.id === transferId);
        if (!transfer) return;
        
        // 重置传输状态
        transfer.status = 'preparing';
        transfer.progress = 0;
        transfer.error = null;
        this.updateTransferList();
        
        // 根据类型重新执行传输
        if (transfer.type === 'download') {
            this.batchDownloadFiles(transfer.files);
            // 从列表中移除旧的传输记录
            this.removeTransfer(transferId);
        } else if (transfer.type === 'upload') {
            // TODO: 实现上传重试逻辑
            this.showNotification('功能开发中', '上传重试功能尚未实现', false);
        }
    }
    
    /**
     * 从传输列表中移除传输
     * @param {number} transferId - 传输ID
     */
    removeTransfer(transferId) {
        const index = this.transfers.findIndex(t => t.id === transferId);
        if (index !== -1) {
            this.transfers.splice(index, 1);
            this.updateTransferList();
        }
    }

    /**
     * 显示传输管理器
     */
    showTransferManager() {
        const container = document.querySelector(this.containerSelector);
        if (!container) {
            console.warn('找不到传输管理器容器');
            return;
        }
        
        // 确保容器可见并且不是最小化状态
        container.style.display = 'flex';
        container.classList.remove('minimized');
        
        // 更新最小化按钮图标
        const minimizeBtn = container.querySelector('.minimize-btn');
        if (minimizeBtn) {
            minimizeBtn.innerHTML = '<i class="fas fa-minus"></i>';
        }
        
        // 滚动到底部
        const listContainer = container.querySelector('.transfer-list');
        if (listContainer) {
            listContainer.scrollTop = listContainer.scrollHeight;
        }
    }
}

// 创建全局实例
let fileTransferManager;

// 确保DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 初始化文件传输管理器...');
    fileTransferManager = new FileTransferManager();

    // 挂载到全局对象
    window.fileTransferManager = fileTransferManager;

    console.log('✅ 文件传输管理器已初始化并挂载到全局对象');

    // 立即显示传输管理器以便测试
    setTimeout(() => {
        if (fileTransferManager && fileTransferManager.showTransferManager) {
            console.log('📊 显示传输管理器界面');
            fileTransferManager.showTransferManager();
        }
    }, 1000);
});