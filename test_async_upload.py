#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步文件上传功能测试脚本
"""

import requests
import time
import os
import tempfile
from io import BytesIO

class AsyncUploadTester:
    def __init__(self, base_url="http://localhost:5100"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def authenticate(self, password):
        """认证文件管理器"""
        url = f"{self.base_url}/ansible/api/file-manager/authenticate"
        data = {"password": password}
        
        response = self.session.post(url, json=data)
        result = response.json()
        
        if result.get('success'):
            print("✅ 认证成功")
            return True
        else:
            print(f"❌ 认证失败: {result.get('message')}")
            return False
    
    def create_test_file(self, filename, size_mb=1):
        """创建测试文件"""
        content = b"0" * (size_mb * 1024 * 1024)  # 创建指定大小的文件
        return BytesIO(content), filename
    
    def upload_file_sync(self, file_data, filename, destination_path="/tmp"):
        """同步上传文件"""
        url = f"{self.base_url}/ansible/api/file-manager/upload"
        
        files = {'file': (filename, file_data, 'application/octet-stream')}
        data = {
            'path': destination_path,
            'overwrite': 'true'
        }
        
        start_time = time.time()
        response = self.session.post(url, files=files, data=data)
        end_time = time.time()
        
        result = response.json()
        duration = end_time - start_time
        
        return result.get('success'), result.get('message'), duration
    
    def upload_file_async(self, file_data, filename, destination_path="/tmp"):
        """异步上传文件"""
        url = f"{self.base_url}/ansible/api/file-manager/upload-async"
        
        files = {'file': (filename, file_data, 'application/octet-stream')}
        data = {
            'path': destination_path,
            'overwrite': 'true'
        }
        
        start_time = time.time()
        response = self.session.post(url, files=files, data=data)
        result = response.json()
        
        if not result.get('success'):
            return False, result.get('message'), 0, None
        
        task_id = result.get('task_id')
        print(f"📤 异步上传任务已创建: {task_id}")
        
        # 轮询任务状态
        return self.poll_upload_status(task_id, start_time)
    
    def poll_upload_status(self, task_id, start_time):
        """轮询上传状态"""
        url = f"{self.base_url}/ansible/api/file-manager/upload-status/{task_id}"
        
        last_progress = 0
        while True:
            response = self.session.get(url)
            result = response.json()
            
            if not result.get('success'):
                return False, "获取状态失败", 0, task_id
            
            status_data = result.get('status', {})
            status = status_data.get('status')
            progress = status_data.get('progress', 0)
            speed = status_data.get('speed', 0)
            
            # 显示进度（只在进度变化时显示）
            if progress != last_progress:
                speed_mb = speed / (1024 * 1024) if speed else 0
                print(f"📊 进度: {progress:.1f}% - 速度: {speed_mb:.2f} MB/s - 状态: {status}")
                last_progress = progress
            
            if status == 'completed':
                duration = time.time() - start_time
                return True, "上传完成", duration, task_id
            elif status == 'failed':
                error_msg = status_data.get('error_message', '未知错误')
                duration = time.time() - start_time
                return False, f"上传失败: {error_msg}", duration, task_id
            elif status == 'cancelled':
                duration = time.time() - start_time
                return False, "上传已取消", duration, task_id
            
            time.sleep(1)  # 等待1秒后再次查询
    
    def cancel_upload(self, task_id):
        """取消上传任务"""
        url = f"{self.base_url}/ansible/api/file-manager/upload-cancel/{task_id}"
        
        response = self.session.post(url)
        result = response.json()
        
        return result.get('success'), result.get('message')
    
    def test_sync_vs_async(self, file_size_mb=5):
        """测试同步vs异步上传性能"""
        print(f"\n🧪 开始性能测试 (文件大小: {file_size_mb}MB)")
        print("=" * 50)
        
        # 测试同步上传
        print("\n📤 测试同步上传...")
        file_data, filename = self.create_test_file(f"sync_test_{file_size_mb}mb.txt", file_size_mb)
        success, message, duration = self.upload_file_sync(file_data, filename)
        
        if success:
            speed_mb = file_size_mb / duration
            print(f"✅ 同步上传成功")
            print(f"   耗时: {duration:.2f}秒")
            print(f"   速度: {speed_mb:.2f} MB/s")
        else:
            print(f"❌ 同步上传失败: {message}")
        
        # 等待一下
        time.sleep(2)
        
        # 测试异步上传
        print("\n📤 测试异步上传...")
        file_data, filename = self.create_test_file(f"async_test_{file_size_mb}mb.txt", file_size_mb)
        success, message, duration, task_id = self.upload_file_async(file_data, filename)
        
        if success:
            speed_mb = file_size_mb / duration
            print(f"✅ 异步上传成功")
            print(f"   耗时: {duration:.2f}秒")
            print(f"   速度: {speed_mb:.2f} MB/s")
            print(f"   任务ID: {task_id}")
        else:
            print(f"❌ 异步上传失败: {message}")
    
    def test_concurrent_uploads(self, num_files=3, file_size_mb=2):
        """测试并发上传"""
        print(f"\n🚀 测试并发上传 ({num_files}个文件，每个{file_size_mb}MB)")
        print("=" * 50)
        
        task_ids = []
        start_time = time.time()
        
        # 启动多个异步上传任务
        for i in range(num_files):
            file_data, filename = self.create_test_file(f"concurrent_test_{i}_{file_size_mb}mb.txt", file_size_mb)
            
            url = f"{self.base_url}/ansible/api/file-manager/upload-async"
            files = {'file': (filename, file_data, 'application/octet-stream')}
            data = {'path': '/tmp', 'overwrite': 'true'}
            
            response = self.session.post(url, files=files, data=data)
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                task_ids.append(task_id)
                print(f"📤 启动上传任务 {i+1}: {task_id}")
            else:
                print(f"❌ 启动上传任务 {i+1} 失败: {result.get('message')}")
        
        # 监控所有任务
        completed_tasks = 0
        while completed_tasks < len(task_ids):
            time.sleep(2)
            
            for task_id in task_ids:
                url = f"{self.base_url}/ansible/api/file-manager/upload-status/{task_id}"
                response = self.session.get(url)
                result = response.json()
                
                if result.get('success'):
                    status_data = result.get('status', {})
                    status = status_data.get('status')
                    progress = status_data.get('progress', 0)
                    
                    if status in ['completed', 'failed', 'cancelled']:
                        if task_id in task_ids:
                            task_ids.remove(task_id)
                            completed_tasks += 1
                            print(f"✅ 任务完成: {task_id} - 状态: {status}")
        
        total_duration = time.time() - start_time
        total_size = num_files * file_size_mb
        avg_speed = total_size / total_duration
        
        print(f"\n📊 并发上传结果:")
        print(f"   总耗时: {total_duration:.2f}秒")
        print(f"   总大小: {total_size}MB")
        print(f"   平均速度: {avg_speed:.2f} MB/s")
    
    def test_cancel_upload(self, file_size_mb=10):
        """测试取消上传"""
        print(f"\n🛑 测试取消上传功能 (文件大小: {file_size_mb}MB)")
        print("=" * 50)
        
        # 启动一个大文件上传
        file_data, filename = self.create_test_file(f"cancel_test_{file_size_mb}mb.txt", file_size_mb)
        
        url = f"{self.base_url}/ansible/api/file-manager/upload-async"
        files = {'file': (filename, file_data, 'application/octet-stream')}
        data = {'path': '/tmp', 'overwrite': 'true'}
        
        response = self.session.post(url, files=files, data=data)
        result = response.json()
        
        if not result.get('success'):
            print(f"❌ 启动上传失败: {result.get('message')}")
            return
        
        task_id = result.get('task_id')
        print(f"📤 上传任务已启动: {task_id}")
        
        # 等待一段时间后取消
        print("⏳ 等待3秒后取消上传...")
        time.sleep(3)
        
        success, message = self.cancel_upload(task_id)
        if success:
            print(f"✅ 取消成功: {message}")
        else:
            print(f"❌ 取消失败: {message}")

def main():
    """主测试函数"""
    print("🧪 异步文件上传功能测试")
    print("=" * 50)
    
    # 创建测试器
    tester = AsyncUploadTester()
    
    # 认证（需要根据实际情况修改密码）
    password = input("请输入7.26服务器密码: ")
    if not tester.authenticate(password):
        return
    
    try:
        # 测试1: 同步vs异步性能对比
        tester.test_sync_vs_async(file_size_mb=3)
        
        # 测试2: 并发上传
        tester.test_concurrent_uploads(num_files=3, file_size_mb=2)
        
        # 测试3: 取消上传
        tester.test_cancel_upload(file_size_mb=5)
        
        print("\n🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
