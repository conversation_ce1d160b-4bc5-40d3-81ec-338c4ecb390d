from flask import Flask, render_template, request, jsonify, send_file, current_app
import os
import sys
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from .models import db, Server, AnsibleTask, ServerGroup, Playbook, AnsibleTaskExecution, ServerGroupMapping
from .utils.ssh_proxy import SSHProxy
from .utils.ansible_api import AnsibleAPI
from .utils.file_manager import FileManager
# from config import Config  # <-- This line is removed/commented out
from datetime import datetime
from . import ansible_bp

# 初始化全局对象
ssh_proxy = SSHProxy()
ansible_api = AnsibleAPI(ssh_proxy=ssh_proxy)
file_manager = FileManager()

# 添加文件传输管理相关的API
import uuid
import time
import threading
import logging
import os
from pathlib import Path

# 全局任务存储
transfer_tasks = {}
task_lock = threading.Lock()

# 使用应用日志系统，不再创建单独的日志文件
# 不再需要单独的日志记录器 - transfer_logger = logging.getLogger('file_transfer')
# 不再需要单独的日志记录器 - transfer_logger.setLevel(logging.INFO)
# 不再需要单独的日志记录器 - if not transfer_logger.handlers:
# 不再需要单独的日志记录器 -     fh = logging.FileHandler('transfer.log')
# 不再需要单独的日志记录器 -     fh.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
# 不再需要单独的日志记录器 -     transfer_logger.addHandler(fh)

class TransferTask:
    def __init__(self, task_id, file_path, file_name, task_type='download'):
        # 保存当前应用的logger引用，以便在线程中使用
        from flask import current_app
        self.logger = current_app._get_current_object().logger  # 获取当前应用的logger对象
        self.type = task_type
        self.progress = 0
        self.speed = '0 KB/s'
        self.status = 'waiting'  # waiting, in_progress, paused, completed, failed
        self.start_time = time.time()
        self.last_update = time.time()
        self.bytes_transferred = 0
        self.total_size = self._get_file_size()
        self.error = None
        self.thread = None
        self.is_paused = False
        self.is_cancelled = False
    
    def _get_file_size(self):
        try:
            if self.type == 'download':
                return os.path.getsize(self.file_path)
            else:
                # 对于上传任务，文件大小可能需要从请求中获取
                return 0
        except Exception as e:
            self.logger.error(f"Error getting size for {self.file_path}: {str(e)}")
            return 0
    
    def start(self):
        if self.type == 'download':
            self.thread = threading.Thread(target=self._download_task)
            self.thread.daemon = True
            self.thread.start()
        else:
            # 上传任务的处理逻辑
            pass
    
    def _download_task(self):
        try:
            self.status = 'in_progress'
            self.logger.info(f"Starting download task {self.id} for file {self.file_path}")
            
            # 创建下载目录
            download_dir = os.path.join(os.path.dirname(__file__), 'downloads')
            os.makedirs(download_dir, exist_ok=True)
            
            temp_path = os.path.join(download_dir, f"{self.id}_{self.file_name}")
            
            source_size = os.path.getsize(self.file_path)
            chunk_size = 1024 * 1024  # 1MB
            
            with open(self.file_path, 'rb') as src, open(temp_path, 'wb') as dst:
                start_time = time.time()
                last_update_time = start_time
                last_bytes = 0
                
                while True:
                    if self.is_cancelled:
                        self.logger.info(f"Download task {self.id} cancelled")
                        os.remove(temp_path)
                        return
                    
                    if self.is_paused:
                        time.sleep(0.5)
                        continue
                    
                    chunk = src.read(chunk_size)
                    if not chunk:
                        break
                    
                    dst.write(chunk)
                    self.bytes_transferred += len(chunk)
                    
                    # 更新进度
                    self.progress = int((self.bytes_transferred / source_size) * 100)
                    
                    # 每秒更新一次速度
                    current_time = time.time()
                    if current_time - last_update_time >= 1:
                        time_diff = current_time - last_update_time
                        bytes_diff = self.bytes_transferred - last_bytes
                        speed_bytes_per_sec = bytes_diff / time_diff
                        
                        # 格式化速度
                        if speed_bytes_per_sec < 1024:
                            self.speed = f"{speed_bytes_per_sec:.2f} B/s"
                        elif speed_bytes_per_sec < 1024 * 1024:
                            self.speed = f"{speed_bytes_per_sec/1024:.2f} KB/s"
                        else:
                            self.speed = f"{speed_bytes_per_sec/(1024*1024):.2f} MB/s"
                        
                        last_update_time = current_time
                        last_bytes = self.bytes_transferred
                        self.last_update = current_time
            
            # 完成下载
            self.status = 'completed'
            self.progress = 100
            self.logger.info(f"Download task {self.id} completed")
            
        except Exception as e:
            self.status = 'failed'
            self.error = str(e)
            self.logger.error(f"Error in download task {self.id}: {str(e)}")
            if os.path.exists(temp_path):
                os.remove(temp_path)
    
    def pause(self):
        if self.status == 'in_progress':
            self.is_paused = True
            self.status = 'paused'
            self.logger.info(f"Task {self.id} paused")
            return True
        return False
    
    def resume(self):
        if self.status == 'paused':
            self.is_paused = False
            self.status = 'in_progress'
            self.logger.info(f"Task {self.id} resumed")
            return True
        return False
    
    def cancel(self):
        self.is_cancelled = True
        self.status = 'cancelled'
        self.logger.info(f"Task {self.id} cancelled")
        return True
    
    def get_download_path(self):
        download_dir = os.path.join(os.path.dirname(__file__), 'downloads')
        return os.path.join(download_dir, f"{self.id}_{self.file_name}")
    
    def to_dict(self):
        return {
            'id': self.id,
            'file_name': self.file_name,
            'type': self.type,
            'progress': self.progress,
            'speed': self.speed,
            'status': self.status,
            'total_size': self.total_size,
            'bytes_transferred': self.bytes_transferred,
            'start_time': self.start_time,
            'last_update': self.last_update,
            'error': self.error
        }

def convert_to_linux_path(path):
    """将Windows路径转换为Linux路径格式"""
    if path is None:
        return None
    # 将反斜杠转换为正斜杠
    path = path.replace('\\', '/')
    # 如果是完整的Windows路径，转换为Linux路径格式
    if ':' in path:
        # 移除驱动器号（例如 C:）并添加 /etc/ansible/playbooks
        path = '/etc/ansible/playbooks/' + path.split(':', 1)[1].lstrip('/')
    return path

@ansible_bp.route('/')
def index():
    """首页路由"""
    # Use the renamed template to avoid conflict with the main app's index.html
    return render_template('ansible_work/ansible_index.html')

@ansible_bp.route('/api/groups', methods=['GET', 'POST'])
def manage_groups():
    """服务器分组管理接口"""
    if request.method == 'GET':
        try:
            groups = ServerGroup.query.all()
            return jsonify([{
                'id': g.id,
                'name': g.name,
                'description': g.description,
                'server_count': len(g.servers)
            } for g in groups])
        except Exception as e:
            current_app.logger.error(f'获取分组列表失败: {str(e)}')
            return jsonify({'error': '获取分组列表失败'}), 500
    
    elif request.method == 'POST':
        try:
            data = request.json
            # 检查分组名称是否已存在
            existing_group = ServerGroup.query.filter_by(name=data['name']).first()
            if (existing_group):
                return jsonify({'error': '分组名称已存在'}), 400
                
            group = ServerGroup(
                name=data['name'],
                description=data.get('description', '')
            )
            if 'server_ids' in data:
                servers = Server.query.filter(Server.id.in_(data['server_ids'])).all()
                group.servers.extend(servers)
            
            db.session.add(group)
            db.session.commit()
            return jsonify({'message': '服务器分组创建成功'}), 201
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'创建服务器分组失败: {str(e)}')
            return jsonify({'error': '创建服务器分组失败'}), 500

@ansible_bp.route('/api/playbooks', methods=['GET', 'POST'])
def playbooks():
    """Playbook管理接口"""
    if request.method == 'GET':
        # 获取所有Playbook
        playbooks = Playbook.query.all()
        return jsonify([{
            'id': p.id,
            'name': p.name,
            'description': p.description,
            'hosts': p.hosts if hasattr(p, 'hosts') and p.hosts else 'localhost',
            'created_at': p.created_at.strftime('%Y-%m-%d %H:%M:%S') if p.created_at else None
        } for p in playbooks])
    
    elif request.method == 'POST':
        try:
            data = request.json
            
            # 生成Playbook文件路径
            import os
            from datetime import datetime
            
            # 确保playbooks目录存在
            playbooks_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'playbooks')
            if not os.path.exists(playbooks_dir):
                os.makedirs(playbooks_dir)
            
            # 生成文件名（使用时间戳和名称，避免冲突）
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            safe_name = data['name'].replace(' ', '_').lower()
            playbook_filename = f"{timestamp}_{safe_name}.yml"
            
            # 使用绝对路径进行文件操作
            full_playbook_path = os.path.join(playbooks_dir, playbook_filename)
            
            # 构建Playbook内容（如果没有提供内容）
            if 'content' not in data or not data['content'].strip():
                # 创建基本的Playbook内容模板
                hosts = data.get('hosts', 'localhost')
                playbook_content = f"""---
# Playbook: {data['name']}
# 描述: {data.get('description', '')}
- hosts: {hosts}
  gather_facts: no  # 不收集目标服务器的实际信息（加快执行速度）
  tasks:
    - name: 测试服务器是否可以被 ping 通
      ansible.builtin.ping:
"""
            else:
                playbook_content = data['content']
            
            # 创建Playbook文件
            try:
                with open(full_playbook_path, 'w', encoding='utf-8') as f:
                    f.write(playbook_content)
                current_app.logger.info(f"创建Playbook文件成功: {full_playbook_path}")
            except Exception as e:
                current_app.logger.error(f"创建Playbook文件失败: {str(e)}")
                return jsonify({'error': f'创建Playbook文件失败: {str(e)}'}), 500
            
            # 创建Playbook记录 - 使用相对路径存储
            relative_path = f"playbooks/{playbook_filename}"
            
            playbook = Playbook(
                name=data['name'],
                description=data.get('description', ''),
                playbook_path=relative_path,
                created_by=1
            )
            db.session.add(playbook)
            db.session.commit()
            return jsonify({'message': 'Playbook创建成功'}), 201
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'创建Playbook失败: {str(e)}')
            return jsonify({'error': '创建Playbook失败'}), 500

@ansible_bp.route('/api/playbooks/<int:playbook_id>', methods=['GET', 'PUT', 'DELETE'])
def manage_playbook(playbook_id):
    """管理单个Playbook"""
    # 查询Playbook
    playbook = db.session.get(Playbook, playbook_id)
    
    if request.method == 'GET':
        # 获取单个Playbook详情
        try:
            # 检查文件是否存在
            import os
            
            # 获取playbooks目录的绝对路径
            playbooks_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'playbooks')
            
            # 处理路径为空的情况
            if not playbook.playbook_path or playbook.playbook_path == '--' or playbook.playbook_path.startswith('---'):
                # 生成新的相对路径
                safe_name = playbook.name.replace(' ', '_').lower() if playbook.name else f"playbook_{playbook_id}"
                playbook_filename = f"{playbook.id}_{safe_name}.yml"
                relative_path = f"playbooks/{playbook_filename}"
                full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), relative_path)
                
                # 确保目录存在
                if not os.path.exists(playbooks_dir):
                    os.makedirs(playbooks_dir)
                
                # 更新数据库 - 存储相对路径
                playbook.playbook_path = relative_path
                db.session.commit()
                current_app.logger.info(f"为Playbook ID {playbook.id}生成新路径: {relative_path}")
                
                # 创建默认内容的文件
                try:
                    default_content = f"""---
# Playbook: {playbook.name}
# 描述: {playbook.description or ''}
- hosts: all  # 默认使用all，实际执行时会被任务中指定的目标主机覆盖
  gather_facts: yes
  tasks:
    - name: 示例任务
      debug:
        msg: "这是一个示例任务"
"""
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(default_content)
                    current_app.logger.info(f"创建默认Playbook文件: {full_path}")
                except Exception as e:
                    current_app.logger.error(f"创建默认Playbook文件失败: {str(e)}")
            
            # 将相对路径转换为绝对路径以便读取文件
            full_path = get_absolute_playbook_path(playbook.playbook_path)
            
            if not os.path.exists(full_path):
                current_app.logger.warning(f"Playbook文件不存在: {full_path}")
                # 尝试创建文件
                try:
                    # 确保目录存在
                    playbook_dir = os.path.dirname(full_path)
                    if not os.path.exists(playbook_dir):
                        os.makedirs(playbook_dir, exist_ok=True)
                    
                    # 创建默认内容的文件
                    default_content = f"""---
# Playbook: {playbook.name}
# 描述: {playbook.description or ''}
- hosts: all  # 默认使用all，实际执行时会被任务中指定的目标主机覆盖
  gather_facts: yes
  tasks:
    - name: 示例任务
      debug:
        msg: "这是一个示例任务"
"""
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(default_content)
                    current_app.logger.info(f"创建默认Playbook文件: {full_path}")
                    content = default_content
                except Exception as e:
                    current_app.logger.error(f"创建默认Playbook文件失败: {str(e)}")
                    content = f"Playbook文件不存在，将在保存时创建"
            else:
                # 读取Playbook文件内容
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                except UnicodeDecodeError:
                    # 尝试不同的编码
                    with open(full_path, 'r', encoding='latin-1') as f:
                        content = f.read()
                except Exception as e:
                    current_app.logger.error(f"读取Playbook文件失败: {str(e)}")
                    content = "无法读取Playbook文件内容"
            
            return jsonify({
                'id': playbook.id,
                'name': playbook.name,
                'description': playbook.description,
                'playbook_path': playbook.playbook_path,
                'content': content,
                'created_at': playbook.created_at.isoformat() if playbook.created_at else None,
                'updated_at': playbook.updated_at.isoformat() if playbook.updated_at else None
            })
        
        except Exception as e:
            current_app.logger.error(f"读取Playbook文件失败: {str(e)}")
            return jsonify({'error': '读取Playbook文件失败'}), 500
        
    elif request.method == 'PUT':
        # 更新Playbook
        try:
            data = request.json
            playbook.name = data['name']
            playbook.description = data.get('description', '')
            
            # 如果提供了内容，则更新Playbook文件
            if 'content' in data:
                try:
                    import os
                    
                    # 处理路径为空的情况
                    if not playbook.playbook_path or playbook.playbook_path == '--' or playbook.playbook_path.startswith('---'):
                        # 生成新的相对路径
                        safe_name = playbook.name.replace(' ', '_').lower()
                        playbook_filename = f"{playbook.id}_{safe_name}.yml"
                        relative_path = f"playbooks/{playbook_filename}"
                        
                        # 确保目录存在
                        playbooks_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'playbooks')
                        if not os.path.exists(playbooks_dir):
                            os.makedirs(playbooks_dir, exist_ok=True)
                        
                        # 更新数据库 - 存储相对路径
                        playbook.playbook_path = relative_path
                        db.session.commit()
                        current_app.logger.info(f"为Playbook ID {playbook.id}生成新路径: {relative_path}")
                    
                    # 获取文件的绝对路径
                    full_path = get_absolute_playbook_path(playbook.playbook_path)
                    
                    # 如果内容为空，则根据hosts生成默认内容
                    if not data['content'].strip():
                        hosts = data.get('hosts', playbook.hosts) or 'localhost'
                        data['content'] = f"""---
# Playbook: {data['name']}
# 描述: {data.get('description', '')}
- hosts: {hosts}
  gather_facts: no  # 不收集目标服务器的实际信息（加快执行速度）
  tasks:
    - name: 测试服务器是否可以被 ping 通
      ansible.builtin.ping:
"""
                        
                    # 写入文件内容
                    current_app.logger.info(f"准备写入文件: {full_path}")
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(data['content'])
                    current_app.logger.info(f"更新Playbook文件成功: {full_path}")
                    
                except Exception as e:
                    current_app.logger.error(f"更新Playbook文件失败: {str(e)}")
                    return jsonify({'error': f'更新Playbook文件失败: {str(e)}'}), 500
            
            # 如果提供了新的路径，则更新路径
            if 'playbook_path' in data and data['playbook_path'] != playbook.playbook_path:
                # 转换为相对路径进行存储
                new_path = get_relative_playbook_path(data['playbook_path'])
                playbook.playbook_path = new_path
            
            db.session.commit()
            return jsonify({'message': 'Playbook更新成功'})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新Playbook失败: {str(e)}")
            return jsonify({'error': f'更新Playbook失败: {str(e)}'}), 500
        
    elif request.method == 'DELETE':
        db.session.delete(playbook)
        db.session.commit()
        current_app.logger.info(f"Playbook ID {playbook_id} 删除成功")
        return jsonify({'message': 'Playbook删除成功'})

@ansible_bp.route('/api/playbooks/hosts', methods=['POST'])
def get_playbook_hosts():
    """获取Playbook中定义的目标服务器信息"""
    try:
        data = request.json
        if not data or not data.get('playbook_content'):
            return jsonify({'error': 'Playbook内容不能为空'}), 400
        
        import yaml
        playbook_content = data['playbook_content'].strip()
        
        # 基本格式验证
        if not playbook_content:
            return jsonify({'error': 'Playbook内容不能为空'}), 400

        # 确保内容是YAML格式
        try:
            playbook_data = yaml.safe_load(playbook_content)
        except yaml.YAMLError as e:
            current_app.logger.error(f'YAML解析错误: {str(e)}')
            return jsonify({'error': 'YAML格式错误，请检查语法'}), 400

        # 验证playbook结构
        if not isinstance(playbook_data, list):
            return jsonify({'error': 'Playbook必须是任务列表格式'}), 400
            
        if not playbook_data:
            return jsonify({'error': 'Playbook不能为空列表'}), 400
            
        if not isinstance(playbook_data[0], dict) or 'hosts' not in playbook_data[0]:
            return jsonify({'error': 'Playbook必须包含hosts字段'}), 400

        # 提取hosts信息
        hosts = playbook_data[0]['hosts']
        if not hosts:
            return jsonify({'error': 'hosts不能为空'}), 400
            
        # 处理hosts字段
        if isinstance(hosts, str):
            hosts = [h.strip() for h in hosts.split(',') if h.strip()]
        elif isinstance(hosts, list):
            hosts = [str(h).strip() for h in hosts if str(h).strip()]
        else:
            return jsonify({'error': 'hosts格式无效'}), 400
            
        if not hosts:
            return jsonify({'error': 'hosts不能为空'}), 400
            
        return jsonify({'hosts': hosts})
        
    except Exception as e:
        current_app.logger.error(f'处理Playbook hosts信息失败: {str(e)}')
        return jsonify({'error': '处理Playbook hosts信息失败'}), 500

@ansible_bp.route('/api/servers', methods=['GET', 'POST'])
def manage_servers():
    """服务器管理接口"""
    if request.method == 'GET':
        # 获取所有服务器列表
        servers = Server.query.all()
        return jsonify([{
            'id': s.id,
            'hostname': s.hostname,
            'ip_address': s.ip_address,
            'ssh_port': s.ssh_port,
            'description': s.description,
            'group_name': s.groups[0].name if s.groups else None,
            'group_id': s.groups[0].id if s.groups else None
        } for s in servers])
    
    elif request.method == 'POST':
        # 添加新服务器
        data = request.json
        server = Server(
            hostname=data['hostname'],
            ip_address=data['ip_address'],
            ssh_port=data.get('ssh_port', 22),
            description=data.get('description', '')
        )
        if 'group_id' in data:
            group = ServerGroup.query.get(data['group_id'])
            if group:
                server.groups.append(group)
        db.session.add(server)
        db.session.commit()
        return jsonify({'message': '服务器添加成功'}), 201

@ansible_bp.route('/api/servers/<int:server_id>', methods=['GET', 'PUT', 'DELETE'])
def manage_server(server_id):
    """单个服务器管理接口"""
    server = Server.query.get(server_id)
    if not server:
        return jsonify({'error': '服务器不存在'}), 404
    
    if request.method == 'GET':
        return jsonify({
            'id': server.id,
            'hostname': server.hostname,
            'ip_address': server.ip_address,
            'ssh_port': server.ssh_port,
            'description': server.description,
            'group_name': server.groups[0].name if server.groups else None,
            'group_id': server.groups[0].id if server.groups else None
        })
    
    elif request.method == 'PUT':
        data = request.json
        hostname = data.get('hostname')
        ip_address = data.get('ip_address')
        ssh_port = data.get('ssh_port', 22)
        group_id = data.get('group_id')  # 可以为None
        description = data.get('description', '')
        
        if not hostname or not ip_address:
            return jsonify({'error': '主机名和IP地址不能为空'}), 400
        
        try:
            server.hostname = hostname
            server.ip_address = ip_address
            server.ssh_port = ssh_port
            server.description = description
            
            # 更新分组关系，使用正确的方法
            # 先删除所有关联关系
            ServerGroupMapping.query.filter_by(server_id=server.id).delete()
            db.session.flush()  # 确保删除操作先完成
            
            # 如果选择了新分组，则添加新的关联关系
            if group_id:
                group = ServerGroup.query.get(group_id)
                if group:
                    mapping = ServerGroupMapping(server_id=server.id, group_id=group.id)
                    db.session.add(mapping)
            
            db.session.commit()
            return jsonify({'message': '服务器更新成功'})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'更新服务器失败: {str(e)}')
            return jsonify({'error': '更新服务器失败：' + str(e)}), 500
    
    elif request.method == 'DELETE':
        try:
            db.session.delete(server)
            db.session.commit()
            return jsonify({'message': '服务器删除成功'})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'删除服务器失败: {str(e)}')
            return jsonify({'error': '删除服务器失败'}), 500

@ansible_bp.route('/api/tasks', methods=['GET', 'POST'])
def manage_tasks():
    """任务管理接口"""
    if request.method == 'GET':
        try:
            tasks = AnsibleTask.query.order_by(AnsibleTask.created_at.desc()).all()
            return jsonify([{
                'id': task.id,
                'task_name': getattr(task, 'task_name', ''),
                'task_type': getattr(task, 'task_type', ''),
                'target_type': getattr(task, 'target_type', 'server'),
                'target_servers': getattr(task, 'target_servers', ''),
                'playbook_path': getattr(task, 'playbook_path', ''),
                'playbook_id': getattr(task, 'playbook_id', None),
                'command': getattr(task, 'command', ''),
                'execution_content': getattr(task, 'command', '') if getattr(task, 'task_type', '') == 'adhoc' else (
                    get_relative_playbook_path(getattr(task, 'playbook_path', '')) if getattr(task, 'playbook_path', '') else '--'
                ),
                'status': getattr(task, 'status', 'pending'),
                'created_at': getattr(task, 'created_at', datetime.now()).strftime('%Y-%m-%d %H:%M:%S') if getattr(task, 'created_at', None) else None,
                'start_time': getattr(task, 'start_time', None).strftime('%Y-%m-%d %H:%M:%S') if getattr(task, 'start_time', None) else None,
                'end_time': getattr(task, 'end_time', None).strftime('%Y-%m-%d %H:%M:%S') if getattr(task, 'end_time', None) else None
            } for task in tasks])
        except Exception as e:
            current_app.logger.error(f'获取任务列表失败: {str(e)}')
            return jsonify([]), 500
    elif request.method == 'POST':
        data = request.json
        try:
            # 如果提供了playbook_id，获取对应的playbook_path
            playbook_path = None
            if data.get('playbook_id') and data['task_type'] == 'playbook':
                playbook = Playbook.query.get(data['playbook_id'])
                if playbook:
                    playbook_path = playbook.playbook_path
                else:
                    return jsonify({'error': '找不到指定的Playbook'}), 404
            # 确保hosts是一个列表
            hosts = data.get('hosts', [])
            if not isinstance(hosts, list):
                hosts = [hosts]
            # 获取前端传递的 target_type
            target_type = data.get('target_type')
            if not target_type:
                if hosts and len(hosts) == 1:
                    try:
                        if ServerGroup.query.get(hosts[0]):
                            target_type = 'group'
                        else:
                            target_type = 'server'
                    except:
                        target_type = 'server'
                else:
                    target_type = 'server'
            task = AnsibleTask(
                task_name=data['task_name'],
                task_type=data['task_type'],
                target_type=target_type,
                target_servers=','.join(str(h) for h in hosts),
                playbook_path=playbook_path,
                command=data.get('command'),
                status='pending'
            )
            db.session.add(task)
            db.session.commit()
            return jsonify({'message': '任务创建成功', 'task_id': task.id})
        except Exception as e:
            import traceback
            current_app.logger.error(f'创建任务失败: {str(e)}\n{traceback.format_exc()}')
            return jsonify({'error': '创建任务失败'}), 500

@ansible_bp.route('/api/tasks/<int:task_id>', methods=['GET', 'PUT', 'DELETE'])
def get_task(task_id):
    """获取任务详情、更新任务或删除任务"""
    try:
        task = db.session.get(AnsibleTask, task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        if request.method == 'GET':
            # 尝试解析结果为JSON
            formatted_result = None
            try:
                if getattr(task, 'result', None):
                    result_data = json.loads(task.result)
                    formatted_result = json.dumps(result_data, indent=2, ensure_ascii=False)
            except:
                formatted_result = getattr(task, 'result', None)
            
            playbook_id = getattr(task, 'playbook_id', None)
            playbook_path = getattr(task, 'playbook_path', '')
            # 自动补全playbook_id
            if not playbook_id and playbook_path:
                playbook = Playbook.query.filter_by(playbook_path=playbook_path).first()
                if playbook:
                    playbook_id = playbook.id
            
            return jsonify({
                'id': task.id,
                'task_name': getattr(task, 'task_name', ''),
                'task_type': getattr(task, 'task_type', ''),
                'target_type': getattr(task, 'target_type', 'server'),
                'target_servers': getattr(task, 'target_servers', ''),
                'playbook_path': playbook_path,
                'playbook_id': playbook_id,
                'command': getattr(task, 'command', ''),
                'status': getattr(task, 'status', 'pending'),
                'result': getattr(task, 'result', None),
                'formatted_result': formatted_result,
                'start_time': getattr(task, 'start_time', None).strftime('%Y-%m-%d %H:%M:%S') if getattr(task, 'start_time', None) else None,
                'end_time': getattr(task, 'end_time', None).strftime('%Y-%m-%d %H:%M:%S') if getattr(task, 'end_time', None) else None
            })
        
        elif request.method == 'PUT':
            # 编辑任务
            data = request.json
            
            # 更新任务名称
            if 'task_name' in data:
                task.task_name = data['task_name']
            
            # 如果提供了playbook_id，获取对应的playbook_path
            if data.get('playbook_id') and getattr(task, 'task_type', '') == 'playbook':
                playbook = Playbook.query.get(data['playbook_id'])
                if playbook:
                    task.playbook_path = playbook.playbook_path
                else:
                    return jsonify({'error': '找不到指定的Playbook'}), 404
            
            # 更新命令（如果是adhoc类型）
            if getattr(task, 'task_type', '') == 'adhoc' and 'command' in data:
                task.command = data['command']
            
            # 更新目标服务器
            if 'target_servers' in data:
                # 确保hosts是一个列表
                hosts = data.get('target_servers', [])
                if not isinstance(hosts, list):
                    hosts = [hosts]
                
                task.target_servers = ','.join(str(h) for h in hosts)
            
            # 更新 target_type
            if 'target_type' in data:
                task.target_type = data['target_type']
            elif 'target_servers' in data: # 如果只更新了 target_servers，尝试推断 target_type
                # 这是一个简化的推断，与创建任务时类似
                # 假设 target_servers 在这里只包含一个 ID (server_id 或 group_id)
                current_target_id_str = task.target_servers.split(',')[0] if task.target_servers else None
                if current_target_id_str:
                    try:
                        if ServerGroup.query.get(current_target_id_str):
                            task.target_type = 'group'
                        else:
                            task.target_type = 'server'
                    except:
                        task.target_type = 'server' # 出错默认为 server
                else:
                    task.target_type = 'server' # 如果没有target_servers，默认为server

            db.session.commit()
            return jsonify({'message': '任务更新成功'})
            
        elif request.method == 'DELETE':
            try:
                # 先删除关联的执行记录
                AnsibleTaskExecution.query.filter_by(task_id=task_id).delete()
                # 再删除任务
                db.session.delete(task)
                db.session.commit()
                return jsonify({'message': '任务删除成功'})
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"删除任务失败: {str(e)}")
                return jsonify({'error': f'删除任务失败: {str(e)}'}), 500
                
    except Exception as e:
        current_app.logger.error(f"操作任务失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@ansible_bp.route('/api/tasks/<int:task_id>/execute', methods=['POST'])
def execute_task(task_id):
    """执行指定的任务"""
    client = None
    try:
        # 获取任务信息
        task = db.session.get(AnsibleTask, task_id)
        if not task:
            return jsonify({'error': '找不到指定的任务'}), 404

        # 更新任务状态
        task.status = 'running'
        task.start_time = datetime.now()
        db.session.commit()

        # 创建新的执行记录
        execution = AnsibleTaskExecution(
            task_id=task.id,
            status='running',
            start_time=datetime.now()
        )
        db.session.add(execution)
        db.session.commit()

        # 获取密码
        data = request.get_json()
        password = data.get('password')
        if not password:
            raise Exception("需要提供跳板机密码")

        # 连接到跳板机
        client = ssh_proxy.create_proxy_connection('root', password)
        if not client:
            raise Exception("无法连接到跳板机")

        # 测试与Ansible服务器的连接
        test_cmd = "echo 'Testing connection to Ansible server'"
        stdin, stdout, stderr = client.exec_command(test_cmd)
        err = stderr.read().decode()
        if err:
            raise Exception(f"测试Ansible服务器连接失败: {err}")

        # 设置SSH代理
        # 不要直接传递client，因为ssh_proxy对象已经在create_proxy_connection中设置了client
        # ansible_api.set_ssh_proxy(client)
        current_app.logger.info("开始执行任务命令")

        if task.task_type == 'playbook':
            current_app.logger.info(f"执行Playbook: {task.playbook_path}")
            
            # 添加：尝试提取playbook_id并检查是否有更新
            playbook_id = None
            try:
                # 尝试从路径中提取ID
                if task.playbook_path:
                    # 解析路径，通常形式为 playbooks/{playbook_id}_{name}.yml
                    import re
                    match = re.search(r'(\d+)_', os.path.basename(task.playbook_path))
                    if match:
                        playbook_id = int(match.group(1))
                        current_app.logger.info(f"从路径 {task.playbook_path} 提取到playbook_id: {playbook_id}")
            except Exception as e:
                current_app.logger.warning(f"从路径提取playbook_id失败: {str(e)}")
            
            # 如果找到了对应的playbook记录，使用它的最新路径
            if playbook_id:
                playbook = Playbook.query.get(playbook_id)
                if playbook and playbook.playbook_path != task.playbook_path:
                    current_app.logger.info(f"检测到Playbook路径已更新，从 {task.playbook_path} 到 {playbook.playbook_path}")
                    task.playbook_path = playbook.playbook_path
                    db.session.commit()
                    current_app.logger.info(f"已更新任务中的Playbook路径为: {task.playbook_path}")
            
            # 读取本地playbook文件内容
            playbook_path = get_absolute_playbook_path(task.playbook_path)
            playbook_content = read_local_playbook(playbook_path)
            
            # 获取目标服务器列表
            target_ips = get_target_servers(task.target_servers)
            if not target_ips:
                raise Exception("没有找到有效的目标服务器")
            
            # 在远程服务器上创建临时文件
            import uuid
            import base64
            
            # 创建临时库存文件
            inventory_filename = f"inventory_{task.id}_{str(uuid.uuid4())[:8]}"
            inventory_path = f"/tmp/{inventory_filename}"
            
            # 创建库存文件内容
            inventory_content = "[targets]\n"  # 使用固定的组名'targets'
            for ip in target_ips:
                inventory_content += f"{ip}\n"
            
            current_app.logger.debug(f"临时库存文件内容:\n{inventory_content}")
            
            # 创建临时库存文件
            encoded_inventory = base64.b64encode(inventory_content.encode('utf-8')).decode('utf-8')
            create_inv_cmd = f"echo '{encoded_inventory}' | base64 -d > {inventory_path} && chmod 644 {inventory_path}"
            result = ssh_proxy.execute_command(client, create_inv_cmd)
            
            stderr = result.get('stderr', '')
            if stderr and "cannot create" in stderr.lower():
                current_app.logger.error(f"创建临时库存文件失败: {stderr}")
                raise Exception(f"创建临时库存文件失败: {stderr}")
            
            # 生成临时playbook文件名
            temp_filename = f"temp_playbook_{task.id}_{str(uuid.uuid4())[:8]}.yml"
            remote_path = f"/tmp/{temp_filename}"
            
            # 修改playbook内容，替换hosts
            import yaml
            try:
                playbook_data = yaml.safe_load(playbook_content)
                if isinstance(playbook_data, list) and len(playbook_data) > 0:
                    playbook_data[0]['hosts'] = 'targets'  # 使用与inventory文件中相同的组名
                    playbook_content = yaml.dump(playbook_data, default_flow_style=False)
            except Exception as e:
                current_app.logger.warning(f"修改playbook hosts失败: {str(e)}")
            
            # 将playbook内容编码并创建临时文件
            encoded_content = base64.b64encode(playbook_content.encode('utf-8')).decode('utf-8')
            create_file_cmd = f"echo '{encoded_content}' | base64 -d > {remote_path} && chmod 644 {remote_path}"
            result = ssh_proxy.execute_command(client, create_file_cmd)
            
            # 使用正确的返回字段名
            stderr = result.get('stderr', '')
            if stderr and "cannot create" in stderr.lower():
                current_app.logger.error(f"创建临时Playbook文件失败: {stderr}")
                raise Exception(f"创建临时Playbook文件失败: {stderr}")
            
            try:
                # 执行playbook命令
                current_app.logger.info(f"使用临时文件执行Playbook: {remote_path}")
                result = ansible_api.run_playbook(task.id, remote_path, None, inventory_path=f"-i {inventory_path}")
            finally:
                # 清理临时文件
                try:
                    cleanup_cmd = f"rm -f {remote_path} {inventory_path}"
                    cleanup_result = ssh_proxy.execute_command(client, cleanup_cmd)
                    current_app.logger.info("已删除临时文件")
                except Exception as e:
                    current_app.logger.warning(f"清理临时文件失败: {str(e)}")
        else:
            current_app.logger.info(f"执行Ad-hoc命令: {task.task_type} 在 {task.target_servers}")
            
            # 处理命令参数
            command_args = None
            if task.command:
                current_app.logger.info(f"原始命令参数: {task.command}，类型: {type(task.command)}")
                command_args = task.command
            
            # 获取实际的服务器IP地址，而不是使用ID
            servers = []
            for server_id in task.target_servers.split(','):
                try:
                    # 尝试将ID转换为整数
                    try:
                        # 首先尝试作为服务器ID处理
                        server = Server.query.get(int(server_id))
                        if server and server.ip_address:
                            servers.append(server.ip_address)
                            current_app.logger.info(f"添加服务器IP: {server.ip_address}")
                        else:
                            # 如果找不到服务器，尝试作为服务器组ID处理
                            group = ServerGroup.query.get(int(server_id))
                            if group:
                                current_app.logger.info(f"找到服务器组: {group.name}，包含 {len(group.servers)} 台服务器")
                                for server in group.servers:
                                    if server.ip_address:
                                        servers.append(server.ip_address)
                                        current_app.logger.info(f"从组 {group.name} 添加服务器IP: {server.ip_address}")
                            else:
                                current_app.logger.warning(f"ID {server_id} 既不是有效的服务器ID也不是有效的服务器组ID")
                    except ValueError:
                        current_app.logger.warning(f"无法解析ID: {server_id}")
                except Exception as e:
                    current_app.logger.warning(f"获取服务器信息失败: {str(e)}")
            
            # 检查是否有有效的服务器IP
            if not servers:
                current_app.logger.error("没有有效的目标服务器IP地址")
                task.status = 'failed'
                error_result = {
                    'rc': 1,
                    'stdout': '',
                    'stderr': '没有有效的目标服务器IP地址',
                    'formatted_output': "<div class='ansible-error'>没有有效的目标服务器IP地址</div>",
                    'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                task.result = json.dumps(error_result)
                task.end_time = datetime.now()
                db.session.commit()
                return jsonify({'error': '没有有效的目标服务器IP地址'}), 400
            
            current_app.logger.info(f"目标服务器IP列表: {servers}")
            
            result = ansible_api.run_adhoc_command(
                servers,  # 使用实际的服务器IP地址列表
                task.task_type,  # 使用task_type作为模块名
                command_args
            )
        
        current_app.logger.info(f"任务执行结果: {result}")
        
        # 使用原始输出作为formatted_output，确保显示命令执行结果
        result['formatted_output'] = f"""<style>
.ansible-output {{ 
    font-family: monospace; 
    white-space: pre-wrap; 
    line-height: 1.5; 
    padding: 10px; 
    background-color: #f5f5f5; 
    border-radius: 5px; 
}}
.host-success {{ color: #27ae60; }}
.host-changed {{ color: #f39c12; }}
.host-failed {{ color: #e74c3c; }}
.ansible-error {{ color: #e74c3c; font-weight: bold; }}
</style>
<div class='ansible-output'>
<pre>{result.get('stdout', '')}</pre>
</div>"""
        
        current_app.logger.info(f"任务执行结果: {result}")
        
        # 更新任务状态为已完成
        task.status = 'completed'
        task.result = json.dumps(result)
        task.end_time = datetime.now()
        
        # 更新执行记录
        execution.status = 'completed'
        execution.result = json.dumps(result)
        execution.formatted_result = result.get('formatted_output', '')
        execution.end_time = datetime.now()
        
        db.session.commit()
        
        return jsonify(result)
    except Exception as e:
        current_app.logger.error(f"执行任务命令失败: {str(e)}")
        task.status = 'failed'
        error_result = {
            'rc': 1,
            'stdout': '',
            'stderr': str(e),
            'formatted_output': f"<div class='ansible-error'>执行任务命令失败: {str(e)}</div>",
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        task.result = json.dumps(error_result)
        task.end_time = datetime.now()
        
        # 更新执行记录
        execution.status = 'failed'
        execution.result = json.dumps(error_result)
        execution.formatted_result = error_result.get('formatted_output', '')
        execution.end_time = datetime.now()
        
        db.session.commit()
        return jsonify({'error': f'执行任务命令失败: {str(e)}'}), 500

    finally:
        if client:
            try:
                client.close()
                current_app.logger.info("关闭SSH连接")
            except:
                pass

@ansible_bp.route('/api/server-groups', methods=['GET'])
def get_server_groups():
    """获取所有服务器组"""
    try:
        groups = ServerGroup.query.all()
        return jsonify([{
            'id': g.id,
            'name': g.name,
            'description': g.description,
            'created_at': g.created_at.strftime('%Y-%m-%d %H:%M:%S') if g.created_at else None
        } for g in groups])
    except Exception as e:
        current_app.logger.error(f'获取服务器组列表失败: {str(e)}')
        return jsonify({'error': '获取服务器组列表失败'}), 500

@ansible_bp.route('/api/groups/<int:group_id>', methods=['PUT', 'DELETE'])
def manage_group(group_id):
    """管理单个服务器分组的API"""
    group = ServerGroup.query.get(group_id)
    if not group:
        return jsonify({'error': '找不到指定的分组'}), 404
    
    if request.method == 'PUT':
        try:
            data = request.json
            group.name = data['name']
            group.description = data.get('description', '')
            db.session.commit()
            return jsonify({'message': '服务器分组更新成功'})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'更新服务器分组失败: {str(e)}')
            return jsonify({'error': '更新服务器分组失败：' + str(e)}), 500
    
    elif request.method == 'DELETE':
        try:
            group.servers = []
            db.session.delete(group)
            db.session.commit()
            return jsonify({'message': '服务器分组删除成功'})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'删除服务器分组失败: {str(e)}')
            return jsonify({'error': '删除服务器分组失败'}), 500

@ansible_bp.route('/api/server-groups/<int:group_id>/servers', methods=['GET'])
def get_group_servers(group_id):
    """获取分组下的服务器列表"""
    try:
        group = ServerGroup.query.get(group_id)
        servers = group.servers
        return jsonify([{
            'id': server.id,
            'hostname': server.hostname,
            'ip_address': server.ip_address,
            'ssh_port': server.ssh_port,
            'description': server.description
        } for server in servers])
    except Exception as e:
        current_app.logger.error(f'获取分组服务器列表失败: {str(e)}')
        return jsonify({'error': '获取分组服务器列表失败'}), 500

@ansible_bp.route('/api/tasks/<int:task_id>/executions', methods=['GET'])
def get_task_executions(task_id):
    """获取任务的执行历史记录"""
    try:
        # 检查任务是否存在
        task = db.session.get(AnsibleTask, task_id)
        if not task:
            return jsonify({'error': '找不到指定的任务'}), 404
        
        # 获取任务的所有执行记录，按时间倒序排列
        executions = AnsibleTaskExecution.query.filter_by(task_id=task_id).order_by(AnsibleTaskExecution.created_at.desc()).all()
        
        return jsonify([{
            'id': execution.id,
            'task_id': execution.task_id,
            'status': execution.status,
            'start_time': execution.start_time.strftime('%Y-%m-%d %H:%M:%S') if execution.start_time else None,
            'end_time': execution.end_time.strftime('%Y-%m-%d %H:%M:%S') if execution.end_time else None,
            'created_at': execution.created_at.strftime('%Y-%m-%d %H:%M:%S') if execution.created_at else None
        } for execution in executions])
    except Exception as e:
        current_app.logger.error(f'获取任务执行历史失败: {str(e)}')
        return jsonify({'error': '获取任务执行历史失败'}), 500

@ansible_bp.route('/api/tasks/executions/<int:execution_id>', methods=['GET'])
def get_execution_details(execution_id):
    """获取单个执行记录的详细信息"""
    try:
        # 获取执行记录
        execution = db.session.get(AnsibleTaskExecution, execution_id)
        if not execution:
            return jsonify({'error': '找不到指定的执行记录'}), 404
        
        # 获取关联的任务
        task = db.session.get(AnsibleTask, execution.task_id)
        
        # 构建响应数据
        response = {
            'id': execution.id,
            'task_id': execution.task_id,
            'task_name': task.task_name if task else 'Unknown',
            'status': execution.status,
            'start_time': execution.start_time.strftime('%Y-%m-%d %H:%M:%S') if execution.start_time else None,
            'end_time': execution.end_time.strftime('%Y-%m-%d %H:%M:%S') if execution.end_time else None,
            'created_at': execution.created_at.strftime('%Y-%m-%d %H:%M:%S') if execution.created_at else None
        }
        
        # 处理结果数据
        if execution.result:
            try:
                result_obj = json.loads(execution.result)
                if 'formatted_output' in result_obj:
                    response['formatted_result'] = result_obj['formatted_output']
                elif execution.formatted_result:
                    response['formatted_result'] = execution.formatted_result
                else:
                    response['formatted_result'] = f"<pre>{json.dumps(result_obj, indent=2)}</pre>"
            except Exception as e:
                current_app.logger.error(f'解析执行结果失败: {str(e)}')
                response['formatted_result'] = execution.formatted_result or execution.result
        else:
            response['formatted_result'] = execution.formatted_result or '<div class="alert alert-info">无执行结果</div>'
        
        return jsonify(response)
    except Exception as e:
        current_app.logger.error(f'获取执行记录详情失败: {str(e)}')
        return jsonify({'error': '获取执行记录详情失败'}), 500

@ansible_bp.route('/api/playbooks/content', methods=['GET'])
def get_playbook_content():
    """获取Playbook文件内容"""
    try:
        # 获取文件路径
        path = request.args.get('path')
        if not path:
            return jsonify({'error': '未提供Playbook路径'}), 400
        
        # 确保使用绝对路径读取文件
        full_path = get_absolute_playbook_path(path)
        
        # 检查文件是否存在
        if not os.path.exists(full_path):
            # 如果文件不存在，尝试创建空文件
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                
                # 创建文件并写入默认内容
                with open(full_path, 'w', encoding='utf-8') as f:
                    default_content = """---
# Ansible Playbook
# 创建时间: {}
- name: Default Playbook
  hosts: all
  tasks:
    - name: Echo message
      debug:
        msg: "Hello from Ansible"
""".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                    f.write(default_content)
                
                current_app.logger.info(f"已创建Playbook文件: {full_path}")
                return default_content
            except Exception as e:
                current_app.logger.error(f"创建Playbook文件失败: {str(e)}")
                return jsonify({'error': f'Playbook文件不存在且无法创建: {str(e)}'}), 500
        
        # 读取文件内容
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except Exception as e:
            current_app.logger.error(f"读取Playbook文件失败: {str(e)}")
            return jsonify({'error': f'读取Playbook文件失败: {str(e)}'}), 500
            
    except Exception as e:
        current_app.logger.error(f"获取Playbook内容失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@ansible_bp.route('/api/tasks/<int:task_id>/target_servers', methods=['GET'])
def get_task_target_servers(task_id):
    """获取任务的目标服务器详细信息"""
    try:
        task = db.session.get(AnsibleTask, task_id)
        if not task:
            return jsonify({'error': '找不到指定的任务'}), 404
        
        target_servers_info = []
        if task.target_servers:
            server_ids = task.target_servers.split(',') if ',' in task.target_servers else [task.target_servers]
            
            for server_id in server_ids:
                server_id = server_id.strip()
                try:
                    # 尝试将ID转换为整数
                    try:
                        # 首先尝试作为服务器ID处理
                        server = Server.query.get(int(server_id))
                        if server:
                            target_servers_info.append({
                                'id': server.id,
                                'type': 'server',
                                'name': server.hostname,
                                'ip': server.ip_address
                            })
                        else:
                            # 如果找不到服务器，尝试作为服务器组ID处理
                            group = ServerGroup.query.get(int(server_id))
                            if group:
                                group_info = {
                                    'id': group.id,
                                    'type': 'group',
                                    'name': group.name,
                                    'servers': []
                                }
                                for srv in group.servers:
                                    group_info['servers'].append({
                                        'id': srv.id,
                                        'name': srv.hostname,
                                        'ip': srv.ip_address
                                    })
                                target_servers_info.append(group_info)
                    except ValueError:
                        current_app.logger.warning(f"无法解析ID: {server_id}")
                except Exception as e:
                    current_app.logger.warning(f"获取服务器信息失败: {str(e)}")
        
        return jsonify(target_servers_info)
    except Exception as e:
        current_app.logger.error(f'获取任务目标服务器失败: {str(e)}')
        return jsonify({'error': '获取任务目标服务器失败'}), 500

def read_local_playbook(playbook_path):
    """读取本地playbook文件内容"""
    try:
        # 确保使用绝对路径读取文件
        if not os.path.isabs(playbook_path):
            playbook_path = get_absolute_playbook_path(playbook_path)
            
        with open(playbook_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        current_app.logger.error(f"读取本地Playbook文件失败: {str(e)}")
        raise Exception(f"读取本地Playbook文件失败: {str(e)}")

def get_target_servers(target_servers_str):
    """获取目标服务器列表"""
    target_ips = []
    if not target_servers_str:
        return target_ips

    # 解析目标服务器字符串
    server_ids = target_servers_str.split(',') if ',' in target_servers_str else [target_servers_str]
    
    for server_id in server_ids:
        server_id = server_id.strip()
        try:
            # 尝试将ID转换为整数
            try:
                # 首先尝试作为服务器ID处理
                server = Server.query.get(int(server_id))
                if server and server.ip_address:
                    target_ips.append(server.ip_address)
                    current_app.logger.info(f"添加单台服务器IP: {server.ip_address}")
                else:
                    # 如果找不到服务器，尝试作为服务器组ID处理
                    group = ServerGroup.query.get(int(server_id))
                    if group and group.servers:
                        current_app.logger.info(f"找到服务器组: {group.name}，包含 {len(group.servers)} 台服务器")
                        for server in group.servers:
                            if server.ip_address:
                                target_ips.append(server.ip_address)
                                current_app.logger.info(f"从组 {group.name} 添加服务器IP: {server.ip_address}")
                    else:
                        current_app.logger.warning(f"ID {server_id} 既不是有效的服务器ID也不是有效的服务器组ID")
            except ValueError:
                current_app.logger.warning(f"无法解析ID: {server_id}")
        except Exception as e:
            current_app.logger.warning(f"获取服务器信息失败: {str(e)}")
    
    return target_ips

def get_relative_playbook_path(path):
    """获取Playbook的相对路径"""
    if not path:
        return '--'
    
    # 如果路径包含playbooks目录，只保留该目录及之后的部分
    if 'playbooks' in path:
        return path[path.find('playbooks'):]
    return os.path.basename(path)

def get_absolute_playbook_path(path):
    """将相对路径转换为绝对路径"""
    if not path:
        return None
        
    # 如果已经是绝对路径，直接返回
    if os.path.isabs(path):
        return path
        
    # 将相对路径转换为绝对路径
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), path)

# 文件管理模块API路由
@ansible_bp.route('/api/file-manager/authenticate', methods=['POST'])
def file_manager_authenticate():
    """文件管理模块认证接口"""
    try:
        data = request.json
        password = data.get('password')
        
        if not password:
            return jsonify({'success': False, 'message': '密码不能为空'}), 400
        
        success, message = file_manager.authenticate(password)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        current_app.logger.error(f"文件管理认证失败: {str(e)}")
        return jsonify({'success': False, 'message': f'认证失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/list', methods=['GET'])
def file_manager_list():
    """列出目录内容"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401
        
        directory_path = request.args.get('path', '/')
        success, result = file_manager.list_directory(directory_path)
        
        return jsonify({'success': success, 'data': result if success else None, 'message': None if success else result})
    except Exception as e:
        current_app.logger.error(f"获取目录列表失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取目录列表失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/upload', methods=['POST'])
def file_manager_upload():
    """上传文件（同步方式，保持兼容性）"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有文件'}), 400

        file = request.files['file']
        destination_path = request.form.get('path', '/')

        # 检查是否要覆盖文件
        overwrite = request.form.get('overwrite', 'false').lower() == 'true'

        success, message = file_manager.upload_file(file, destination_path, overwrite)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        current_app.logger.error(f"上传文件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'上传文件失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/upload-async', methods=['POST'])
def file_manager_upload_async():
    """异步上传文件"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有文件'}), 400

        file = request.files['file']
        destination_path = request.form.get('path', '/')

        # 检查是否要覆盖文件
        overwrite = request.form.get('overwrite', 'false').lower() == 'true'

        success, message, task_id = file_manager.upload_file_async(file, destination_path, overwrite)

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'task_id': task_id
            })
        else:
            return jsonify({'success': False, 'message': message})
    except Exception as e:
        current_app.logger.error(f"异步上传文件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'异步上传文件失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/upload-status/<task_id>', methods=['GET'])
def file_manager_upload_status(task_id):
    """获取上传任务状态"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401

        status = file_manager.get_upload_status(task_id)

        if status:
            return jsonify({
                'success': True,
                'status': status
            })
        else:
            return jsonify({'success': False, 'message': '任务不存在'})
    except Exception as e:
        current_app.logger.error(f"获取上传状态失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取上传状态失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/upload-cancel/<task_id>', methods=['POST'])
def file_manager_upload_cancel(task_id):
    """取消上传任务"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401

        success, message = file_manager.cancel_upload(task_id)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        current_app.logger.error(f"取消上传失败: {str(e)}")
        return jsonify({'success': False, 'message': f'取消上传失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/download', methods=['GET'])
def file_manager_download():
    """下载文件"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401
        
        file_path = request.args.get('path')
        if not file_path:
            return jsonify({'success': False, 'message': '缺少文件路径参数'}), 400
        
        # 检查文件是否存在
        if not file_manager.check_file_exists(file_path):
            return jsonify({'success': False, 'message': '文件不存在或无法访问'}), 404
        
        # 获取文件
        success, message, file_data = file_manager.download_file(file_path)
        
        if not success:
            return jsonify({'success': False, 'message': message}), 404
        
        filename = os.path.basename(file_path)
        
        # 返回文件内容
        return send_file(
            file_data,
            as_attachment=True,
            download_name=filename,
            mimetype='application/octet-stream'
        )
    except Exception as e:
        current_app.logger.error(f"下载文件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'下载文件失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/check-auth', methods=['GET'])
def file_manager_check_auth():
    """检查文件管理器认证状态"""
    try:
        is_auth = file_manager.is_authenticated()
        return jsonify({
            'success': True,
            'authenticated': is_auth
        })
    except Exception as e:
        current_app.logger.error(f"检查认证状态失败: {str(e)}")
        return jsonify({'success': False, 'message': f'检查认证状态失败: {str(e)}'}), 500

@ansible_bp.route('/api/file-manager/batch-download', methods=['POST'])
def file_manager_batch_download():
    """批量下载文件"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401
        
        # 检查参数来源：JSON或表单
        if request.is_json:
            data = request.json
            file_paths = data.get('paths', [])
        elif request.form and 'paths' in request.form:
            # 从表单获取paths参数
            try:
                file_paths = json.loads(request.form.get('paths', '[]'))
            except json.JSONDecodeError:
                return jsonify({'success': False, 'message': '无效的paths参数格式'}), 400
        else:
            return jsonify({'success': False, 'message': '未指定要下载的文件'}), 400
        
        if not file_paths:
            return jsonify({'success': False, 'message': '未指定要下载的文件'}), 400
        
        # 调用批量下载方法
        success, message, file_data = file_manager.batch_download(file_paths)
        
        if not success:
            return jsonify({'success': False, 'message': message}), 404
        
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f'批量下载_{timestamp}.tar.gz'
        
        # 返回打包后的文件
        return send_file(
            file_data,
            as_attachment=True,
            download_name=filename,
            mimetype='application/x-gzip'
        )
    except Exception as e:
        current_app.logger.error(f"批量下载文件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'批量下载文件失败: {str(e)}'}), 500

@ansible_bp.route('/api/transfer/download', methods=['POST'])
def create_download_task():
    try:
        data = request.json
        file_path = data.get('file_path')
        file_name = data.get('file_name')
        
        if not file_path:
            return jsonify({'success': False, 'message': '缺少文件路径参数'}), 400
        
        if not file_name:
            file_name = os.path.basename(file_path)
        
        # 检查文件是否存在
        if not os.path.isfile(file_path):
            return jsonify({'success': False, 'message': '文件不存在'}), 404
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 创建下载任务
        task = TransferTask(task_id, file_path, file_name, 'download')
        
        # 存储任务
        with task_lock:
            transfer_tasks[task_id] = task
        
        # 启动任务
        task.start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'file_name': file_name,
            'file_size': task.total_size
        })
    
    except Exception as e:
        current_app.logger.error(f"Error creating download task: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@ansible_bp.route('/api/transfer/status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    with task_lock:
        task = transfer_tasks.get(task_id)
    
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'}), 404
    
    return jsonify({
        'success': True,
        'task': task.to_dict(),
        'progress': task.progress,
        'speed': task.speed,
        'status': task.status
    })

@ansible_bp.route('/api/transfer/list', methods=['GET'])
def list_tasks():
    with task_lock:
        tasks = [task.to_dict() for task in transfer_tasks.values()]
    
    return jsonify({
        'success': True,
        'tasks': tasks
    })

@ansible_bp.route('/api/transfer/pause/<task_id>', methods=['POST'])
def pause_task(task_id):
    with task_lock:
        task = transfer_tasks.get(task_id)
    
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'}), 404
    
    result = task.pause()
    
    return jsonify({
        'success': result,
        'message': '任务已暂停' if result else '无法暂停任务'
    })

@ansible_bp.route('/api/transfer/resume/<task_id>', methods=['POST'])
def resume_task(task_id):
    with task_lock:
        task = transfer_tasks.get(task_id)
    
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'}), 404
    
    result = task.resume()
    
    return jsonify({
        'success': result,
        'message': '任务已继续' if result else '无法继续任务'
    })

@ansible_bp.route('/api/transfer/cancel/<task_id>', methods=['POST'])
def cancel_task(task_id):
    with task_lock:
        task = transfer_tasks.get(task_id)
    
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'}), 404
    
    result = task.cancel()
    
    # 如果取消成功，删除任务
    if result:
        with task_lock:
            del transfer_tasks[task_id]
    
    return jsonify({
        'success': result,
        'message': '任务已取消' if result else '无法取消任务'
    })

@ansible_bp.route('/api/transfer/retry/<task_id>', methods=['POST'])
def retry_task(task_id):
    with task_lock:
        task = transfer_tasks.get(task_id)
    
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'}), 404
    
    # 创建新任务
    new_task = TransferTask(task_id, task.file_path, task.file_name, task.type)
    
    # 存储新任务
    with task_lock:
        transfer_tasks[task_id] = new_task
    
    # 启动新任务
    new_task.start()
    
    return jsonify({
        'success': True,
        'message': '任务已重新开始'
    })

@ansible_bp.route('/api/transfer/download/<task_id>', methods=['GET'])
def download_completed_file(task_id):
    with task_lock:
        task = transfer_tasks.get(task_id)
    
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'}), 404
    
    if task.status != 'completed':
        return jsonify({'success': False, 'message': '任务未完成'}), 400
    
    download_path = task.get_download_path()
    
    if not os.path.exists(download_path):
        return jsonify({'success': False, 'message': '下载文件不存在'}), 404
    
    try:
        return send_file(
            download_path,
            as_attachment=True,
            download_name=task.file_name,
            mimetype='application/octet-stream'
        )
    except Exception as e:
        current_app.logger.error(f"Error sending file {download_path}: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@ansible_bp.route('/api/file-manager/prepare-batch-download', methods=['POST'])
def prepare_batch_download():
    """准备批量下载，但不实际开始下载，主要用于获取文件元数据"""
    try:
        if not file_manager.is_authenticated():
            return jsonify({'success': False, 'message': '未认证，请先登录'}), 401
        
        # 检查参数来源：JSON或表单
        if request.is_json:
            data = request.json
            file_paths = data.get('paths', [])
        elif request.form and 'paths' in request.form:
            # 从表单获取paths参数
            try:
                file_paths = json.loads(request.form.get('paths', '[]'))
            except json.JSONDecodeError:
                return jsonify({'success': False, 'message': '无效的paths参数格式'}), 400
        else:
            return jsonify({'success': False, 'message': '未指定要下载的文件'}), 400
        
        if not file_paths:
            return jsonify({'success': False, 'message': '未指定要下载的文件'}), 400
        
        # 获取文件总大小等元数据，但不实际创建打包文件
        total_size = 0
        file_count = len(file_paths)
        
        for path in file_paths:
            try:
                file_info = file_manager.get_file_info(path)
                if file_info and not file_info.get('is_directory'):
                    total_size += file_info.get('size', 0)
            except Exception as e:
                current_app.logger.error(f"获取文件信息失败: {str(e)}")
                # 继续处理其他文件
        
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f'批量下载_{timestamp}.tar.gz'
        
        # 设置下载文件名
        response = jsonify({
            'success': True,
            'file_count': file_count,
            'total_size': total_size,
            'filename': filename
        })
        
        return response
    except Exception as e:
        current_app.logger.error(f"准备批量下载失败: {str(e)}")
        return jsonify({'success': False, 'message': f'准备批量下载失败: {str(e)}'}), 500

if __name__ == '__main__':
    # 这个文件是蓝图模块，不能直接运行
    # 如果需要测试，请运行主应用文件
    print("这是一个蓝图模块，请运行主应用文件 start.py 或 app.py")


