#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误处理集成脚本
将统一错误处理机制集成到现有系统中
"""

import os
import re
import shutil
from typing import List, Dict, Any
from pathlib import Path

class ErrorHandlingIntegrator:
    """错误处理集成器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "backups" / "error_integration"
        self.integration_log = []
    
    def integrate_all(self):
        """执行完整的错误处理集成"""
        print("🚀 开始集成统一错误处理机制...")
        
        # 1. 创建备份
        self._create_backup()
        
        # 2. 更新主应用文件
        self._update_main_app()
        
        # 3. 更新路由文件
        self._update_route_files()
        
        # 4. 更新数据库操作文件
        self._update_database_files()
        
        # 5. 创建错误处理配置
        self._create_error_config()
        
        # 6. 生成集成报告
        self._generate_integration_report()
        
        print("✅ 错误处理机制集成完成！")
    
    def _create_backup(self):
        """创建备份"""
        print("📁 创建文件备份...")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份关键文件
        key_files = [
            "app.py",
            "src/app.py",
            "src/county_data/summary_stats_routes.py",
            "src/mysql_audit/log_parser.py",
            "src/ansible_work/app.py"
        ]
        
        for file_path in key_files:
            source = self.project_root / file_path
            if source.exists():
                dest = self.backup_dir / file_path
                dest.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, dest)
                print(f"  ✅ 备份: {file_path}")
    
    def _update_main_app(self):
        """更新主应用文件"""
        print("🔧 更新主应用文件...")
        
        app_files = ["app.py", "src/app.py"]
        for app_file in app_files:
            app_path = self.project_root / app_file
            if app_path.exists():
                self._integrate_app_file(app_path)
    
    def _integrate_app_file(self, app_path: Path):
        """集成应用文件"""
        print(f"  📝 更新 {app_path}")
        
        with open(app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加错误处理导入
        import_addition = """
# 统一错误处理机制
from src.utils.error_handler import register_error_handlers, error_handler
from src.utils.error_monitor import error_monitor
"""
        
        # 在Flask导入后添加错误处理导入
        if "from flask import Flask" in content and "error_handler" not in content:
            content = content.replace(
                "from flask import Flask",
                f"from flask import Flask{import_addition}"
            )
        
        # 添加错误处理器注册
        register_addition = """
# 注册统一错误处理器
register_error_handlers(app)

# 启用错误监控
app.config['ERROR_MONITOR'] = error_monitor
"""
        
        # 在app创建后添加错误处理器注册
        if "app = Flask(__name__" in content and "register_error_handlers" not in content:
            # 找到app创建的位置
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "app = Flask(__name__" in line:
                    # 在app配置后插入错误处理器注册
                    insert_pos = i + 1
                    while insert_pos < len(lines) and (
                        lines[insert_pos].startswith('app.') or 
                        lines[insert_pos].strip() == '' or
                        lines[insert_pos].startswith('#')
                    ):
                        insert_pos += 1
                    
                    lines.insert(insert_pos, register_addition)
                    break
            
            content = '\n'.join(lines)
        
        # 写回文件
        with open(app_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.integration_log.append(f"更新应用文件: {app_path}")
    
    def _update_route_files(self):
        """更新路由文件"""
        print("🛣️  更新路由文件...")
        
        route_patterns = [
            "src/**/routes.py",
            "src/**/*_routes.py",
            "src/**/app.py"
        ]
        
        for pattern in route_patterns:
            for route_file in self.project_root.glob(pattern):
                if route_file.is_file():
                    self._integrate_route_file(route_file)
    
    def _integrate_route_file(self, route_path: Path):
        """集成路由文件"""
        print(f"  📝 更新路由文件: {route_path}")
        
        with open(route_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加错误处理导入
        if "from flask import" in content and "handle_exceptions" not in content:
            import_addition = """
from src.utils.error_handler import (
    handle_exceptions, DatabaseError, NetworkError, ValidationError,
    BusinessError, create_error_response, create_success_response,
    ErrorCategory, ErrorLevel
)
"""
            # 在Flask导入后添加
            content = re.sub(
                r'(from flask import[^\n]*\n)',
                r'\1' + import_addition,
                content,
                count=1
            )
        
        # 替换现有的错误处理模式
        self._replace_error_patterns(content, route_path)
    
    def _replace_error_patterns(self, content: str, file_path: Path):
        """替换现有的错误处理模式"""
        # 替换常见的错误处理模式
        patterns = [
            # 替换 try-except 返回 jsonify 错误
            (
                r'except Exception as e:\s*\n\s*return jsonify\(\{"error":[^}]*\}\), 500',
                'except Exception as e:\n        return create_error_response(str(e), "OPERATION_ERROR", 500)'
            ),
            # 替换简单的错误响应
            (
                r'return jsonify\(\{"error":\s*"([^"]*)"[^}]*\}\), (\d+)',
                r'return create_error_response("\1", "ERROR", \2)'
            ),
            # 替换成功响应
            (
                r'return jsonify\(\{"success":\s*True[^}]*\}\)',
                'return create_success_response()'
            )
        ]
        
        modified = False
        for pattern, replacement in patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                modified = True
        
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.integration_log.append(f"更新错误处理模式: {file_path}")
    
    def _update_database_files(self):
        """更新数据库操作文件"""
        print("🗄️  更新数据库操作文件...")
        
        db_files = [
            "src/utils/database_helpers.py",
            "src/county_data/db_manager.py",
            "src/mysql_audit/models.py"
        ]
        
        for db_file in db_files:
            db_path = self.project_root / db_file
            if db_path.exists():
                self._integrate_database_file(db_path)
    
    def _integrate_database_file(self, db_path: Path):
        """集成数据库文件"""
        print(f"  📝 更新数据库文件: {db_path}")
        
        with open(db_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加数据库错误处理
        if "import" in content and "DatabaseError" not in content:
            import_addition = """
from src.utils.error_handler import DatabaseError, retry_on_error
"""
            content = import_addition + content
        
        # 添加重试装饰器到数据库连接函数
        if "def get_db_connection" in content and "@retry_on_error" not in content:
            content = content.replace(
                "def get_db_connection",
                "@retry_on_error(max_attempts=3, delay=1.0)\ndef get_db_connection"
            )
        
        with open(db_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.integration_log.append(f"更新数据库文件: {db_path}")
    
    def _create_error_config(self):
        """创建错误处理配置"""
        print("⚙️  创建错误处理配置...")
        
        # 更新 .env.example 文件
        env_example_path = self.project_root / ".env.example"
        if env_example_path.exists():
            with open(env_example_path, 'a', encoding='utf-8') as f:
                f.write("""
# 错误处理配置
LOG_LEVEL=INFO
ERROR_MONITOR_ENABLED=true
ERROR_RETENTION_DAYS=7
DEFAULT_RETRY_ATTEMPTS=3
SHOW_DETAILED_ERRORS=false
""")
            self.integration_log.append("更新 .env.example 配置")
    
    def _generate_integration_report(self):
        """生成集成报告"""
        print("📊 生成集成报告...")
        
        report_path = self.project_root / "ERROR_INTEGRATION_REPORT.md"
        
        report_content = f"""# 统一错误处理集成报告

## 集成概述
本报告记录了统一错误处理机制的集成过程和结果。

## 集成时间
{self._get_current_time()}

## 集成内容

### 1. 新增文件
- `src/utils/error_handler.py` - 核心错误处理模块
- `src/utils/error_monitor.py` - 错误监控模块
- `src/utils/error_config.py` - 错误处理配置
- `src/utils/error_examples.py` - 使用示例

### 2. 修改的文件
"""
        
        for log_entry in self.integration_log:
            report_content += f"- {log_entry}\n"
        
        report_content += """
### 3. 新增功能
- 统一的异常类型定义
- 自动错误日志记录
- 错误监控和统计
- 重试机制
- 标准化错误响应
- Flask错误处理器

### 4. 使用方法

#### 4.1 装饰器方式
```python
@handle_exceptions(category=ErrorCategory.DATABASE)
def database_operation():
    # 你的代码
    pass
```

#### 4.2 手动异常处理
```python
try:
    # 业务逻辑
    pass
except Exception as e:
    raise DatabaseError("数据库操作失败", details={'error': str(e)})
```

#### 4.3 重试机制
```python
@retry_on_error(max_attempts=3, delay=1.0)
def network_operation():
    # 可能失败的网络操作
    pass
```

### 5. 配置说明
在 `.env` 文件中添加以下配置：
```
LOG_LEVEL=INFO
ERROR_MONITOR_ENABLED=true
ERROR_RETENTION_DAYS=7
DEFAULT_RETRY_ATTEMPTS=3
SHOW_DETAILED_ERRORS=false
```

### 6. 监控和报告
- 错误统计: 访问 `/api/error-stats` 查看错误统计
- 错误详情: 通过 `error_monitor.get_error_details()` 获取
- 错误报告: 通过 `error_monitor.generate_report()` 生成

## 注意事项
1. 备份文件保存在 `backups/error_integration/` 目录
2. 如有问题，可以从备份恢复原始文件
3. 建议在测试环境先验证集成效果
4. 定期清理错误日志，避免占用过多存储空间

## 下一步
1. 测试错误处理功能
2. 根据需要调整错误处理策略
3. 监控系统错误情况
4. 优化错误处理性能
"""
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 集成报告已生成: {report_path}")
    
    def _get_current_time(self):
        """获取当前时间"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def main():
    """主函数"""
    integrator = ErrorHandlingIntegrator()
    integrator.integrate_all()

if __name__ == '__main__':
    main()
