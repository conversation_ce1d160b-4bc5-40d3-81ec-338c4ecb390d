#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Flask上下文修复效果
"""

import os
import sys
import time
import requests
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class FlaskContextFixTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def login(self, password="your_password"):
        """登录到文件管理器"""
        print("🔐 正在登录文件管理器...")
        
        login_url = f"{self.base_url}/ansible/api/file-manager/login"
        response = self.session.post(login_url, json={'password': password})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return False
    
    def create_test_file(self, size_mb=1):
        """创建测试文件"""
        print(f"📄 创建 {size_mb}MB 测试文件...")
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
        
        # 写入测试数据
        data = "这是测试数据，用于验证Flask上下文修复效果。\n" * (size_mb * 1024 * 10)  # 约1MB
        temp_file.write(data.encode('utf-8'))
        temp_file.close()
        
        print(f"✅ 测试文件已创建: {temp_file.name}")
        return temp_file.name
    
    def test_async_upload(self, file_path, destination="/data/image_bak"):
        """测试异步上传"""
        print(f"🚀 测试异步上传: {os.path.basename(file_path)}")
        
        upload_url = f"{self.base_url}/ansible/api/file-manager/upload-async"
        
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f, 'text/plain')}
            data = {
                'path': destination,
                'overwrite': 'true'
            }
            
            print("📤 发送异步上传请求...")
            response = self.session.post(upload_url, files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 异步上传任务已创建: {task_id}")
                return self.monitor_upload_progress(task_id)
            else:
                print(f"❌ 异步上传失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 异步上传请求失败: {response.status_code}")
            return False
    
    def test_chunked_upload(self, file_path, destination="/data/image_bak"):
        """测试分片上传"""
        print(f"🧩 测试分片上传: {os.path.basename(file_path)}")
        
        upload_url = f"{self.base_url}/ansible/api/file-manager/upload-chunked"
        
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f, 'text/plain')}
            data = {
                'path': destination,
                'overwrite': 'true',
                'chunk_size': str(1024 * 1024)  # 1MB分片
            }
            
            print("📤 发送分片上传请求...")
            response = self.session.post(upload_url, files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 分片上传任务已创建: {task_id}")
                return self.monitor_upload_progress(task_id)
            else:
                print(f"❌ 分片上传失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 分片上传请求失败: {response.status_code}")
            return False
    
    def monitor_upload_progress(self, task_id, timeout=60):
        """监控上传进度"""
        print(f"📊 监控上传进度: {task_id}")
        
        status_url = f"{self.base_url}/ansible/api/file-manager/upload-status/{task_id}"
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            response = self.session.get(status_url)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    status = result.get('status')
                    progress = status.get('progress', 0)
                    upload_status = status.get('status', 'unknown')
                    speed = status.get('speed', 0)
                    error_msg = status.get('error_message')
                    
                    # 格式化速度
                    speed_str = self.format_speed(speed) if speed > 0 else "0 B/s"
                    
                    print(f"📈 进度: {progress:.1f}% | 状态: {upload_status} | 速度: {speed_str}")
                    
                    if upload_status == 'completed':
                        print("🎉 上传完成!")
                        return True
                    elif upload_status == 'failed':
                        print(f"💥 上传失败: {error_msg}")
                        return False
                    elif upload_status in ['cancelled', 'paused']:
                        print(f"⏸️ 上传已{upload_status}")
                        return False
                else:
                    print(f"❌ 获取状态失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 状态请求失败: {response.status_code}")
                return False
            
            time.sleep(2)  # 每2秒检查一次
        
        print("⏰ 监控超时")
        return False
    
    def format_speed(self, bytes_per_second):
        """格式化传输速度"""
        if bytes_per_second < 1024:
            return f"{bytes_per_second:.1f} B/s"
        elif bytes_per_second < 1024 * 1024:
            return f"{bytes_per_second / 1024:.1f} KB/s"
        else:
            return f"{bytes_per_second / (1024 * 1024):.1f} MB/s"
    
    def cleanup_test_file(self, file_path):
        """清理测试文件"""
        try:
            os.unlink(file_path)
            print(f"🗑️ 已清理测试文件: {file_path}")
        except Exception as e:
            print(f"⚠️ 清理测试文件失败: {e}")
    
    def run_tests(self):
        """运行所有测试"""
        print("🧪 开始Flask上下文修复测试")
        print("=" * 50)
        
        # 1. 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return False
        
        # 2. 创建测试文件
        test_file = self.create_test_file(size_mb=2)  # 2MB测试文件
        
        try:
            # 3. 测试异步上传
            print("\n" + "=" * 30)
            print("测试1: 异步上传")
            print("=" * 30)
            async_result = self.test_async_upload(test_file)
            
            # 4. 测试分片上传
            print("\n" + "=" * 30)
            print("测试2: 分片上传")
            print("=" * 30)
            chunked_result = self.test_chunked_upload(test_file)
            
            # 5. 总结结果
            print("\n" + "=" * 50)
            print("🏁 测试结果总结")
            print("=" * 50)
            print(f"异步上传: {'✅ 成功' if async_result else '❌ 失败'}")
            print(f"分片上传: {'✅ 成功' if chunked_result else '❌ 失败'}")
            
            if async_result and chunked_result:
                print("\n🎉 所有测试通过！Flask上下文问题已修复！")
                return True
            else:
                print("\n💥 部分测试失败，需要进一步检查")
                return False
                
        finally:
            # 清理测试文件
            self.cleanup_test_file(test_file)

def main():
    """主函数"""
    print("🔧 Flask上下文修复验证工具")
    print("=" * 50)
    
    # 检查服务器是否运行
    tester = FlaskContextFixTester()
    
    try:
        response = requests.get(f"{tester.base_url}/", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行或无法访问")
            return False
    except requests.exceptions.RequestException:
        print("❌ 无法连接到服务器，请确保Flask应用正在运行")
        return False
    
    # 运行测试
    success = tester.run_tests()
    
    if success:
        print("\n✅ 所有测试通过，Flask上下文问题已解决！")
    else:
        print("\n❌ 测试失败，请检查日志获取更多信息")
    
    return success

if __name__ == "__main__":
    main()
