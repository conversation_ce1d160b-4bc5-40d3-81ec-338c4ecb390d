# 安全配置设置指南

## 概述

本指南将帮助您为系统配置安全的环境变量和密钥，确保系统在生产环境中的安全性。

## 快速开始

### 方法1：使用现有的 .env 文件（推荐）

系统已经为您准备了一个包含复杂密钥的 `.env` 文件：

```bash
# 直接启动应用
python quick_start.py
```

### 方法2：生成新的安全密钥

如果您想生成全新的密钥：

```bash
# 生成新的密钥和配置
python generate_keys.py

# 使用生成的配置文件
cp .env.generated .env

# 启动应用
python quick_start.py
```

### 方法3：使用批处理文件

```bash
# Windows
start_dev.bat
```

## 配置文件说明

### .env 文件结构

当前 `.env` 文件包含以下安全配置：

#### 数据库配置
- `DB_PASSWORD`: 数据库密码（当前为开发环境默认值）
- `DATABASE_HOST/PORT/USER`: 数据库连接信息

#### 安全密钥（已生成复杂密钥）
- `SECRET_KEY`: Flask会话密钥（64字符随机密钥）
- `JWT_SECRET`: JWT认证密钥（64字符随机密钥）
- `EDIT_PASSWORD_HASH`: 编辑模式密码哈希
- `API_ACCESS_KEY`: API访问密钥
- `RATE_LIMIT_SECRET`: 频率限制密钥

#### 管理员配置
- `ADMIN_USERNAME`: 管理员用户名
- `ADMIN_PASSWORD_HASH`: 管理员密码哈希

## 密码信息

### 默认密码
- **数据库密码**: `123456` （开发环境）
- **编辑模式密码**: `AdminEdit@2024!`
- **管理员密码**: `Admin@123456!`

### 生产环境建议

1. **更改所有默认密码**
2. **使用强密码**（至少12位，包含大小写字母、数字、特殊字符）
3. **定期轮换密钥**（建议每3-6个月）

## 安全检查

### 检查当前配置
```bash
python check_config.py
```

### 预期输出
```
🔍 系统配置检查工具
==================================================
✅ 所有检查通过 (5/5)
🚀 系统配置正常，可以启动应用
```

## 启动应用

### 推荐启动方式
```bash
# 使用快速启动脚本（自动加载 .env 文件）
python quick_start.py
```

### 验证启动成功
- 访问 `http://localhost:5100`
- 检查控制台输出无错误信息
- 验证数据库连接正常

## 生产环境部署

### 1. 环境变量设置

**Linux/macOS:**
```bash
export DB_PASSWORD="your_production_password"
export SECRET_KEY="your_production_secret_key"
# ... 其他环境变量
```

**Windows:**
```cmd
set DB_PASSWORD=your_production_password
set SECRET_KEY=your_production_secret_key
```

### 2. 安全建议

1. **不要在生产环境使用默认密码**
2. **启用HTTPS**
3. **配置防火墙**
4. **定期备份密钥**
5. **监控访问日志**

### 3. 密钥管理

推荐使用专业的密钥管理服务：
- AWS Secrets Manager
- Azure Key Vault
- HashiCorp Vault
- Google Secret Manager

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证数据库密码是否正确
   - 确认网络连接正常

2. **环境变量未加载**
   - 确认 `.env` 文件存在
   - 检查文件格式是否正确
   - 使用 `python check_config.py` 验证

3. **密钥格式错误**
   - 重新生成密钥：`python generate_keys.py`
   - 检查密钥长度是否符合要求

### 获取帮助

如果遇到问题，请：
1. 运行 `python check_config.py` 检查配置
2. 查看应用启动日志
3. 检查 `.env` 文件格式

## 安全最佳实践

1. **定期更新密钥**
2. **使用强密码策略**
3. **启用访问日志**
4. **实施最小权限原则**
5. **定期安全审计**
6. **备份重要配置**
7. **监控异常访问**

## 文件清单

- `.env`: 实际环境变量配置文件
- `.env.example`: 配置模板文件
- `generate_keys.py`: 密钥生成工具
- `check_config.py`: 配置检查工具
- `quick_start.py`: 快速启动脚本
- `start_dev.bat`: Windows批处理启动文件

---

**注意**: 请妥善保管所有密钥和密码，不要将包含真实密码的文件提交到版本控制系统。
