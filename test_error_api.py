#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试错误统计API的脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:5100"

def test_generate_errors():
    """生成一些测试错误"""
    print("🧪 生成测试错误数据...")
    
    error_types = ['database', 'network', 'validation', 'business', 'generic']
    
    for i, error_type in enumerate(error_types):
        try:
            response = requests.post(
                f"{BASE_URL}/api/test-error",
                json={'type': error_type},
                timeout=5
            )
            print(f"  {i+1}. 生成 {error_type} 错误: {response.status_code}")
        except Exception as e:
            print(f"  {i+1}. 生成 {error_type} 错误失败: {str(e)}")
        
        time.sleep(0.5)  # 稍微延迟一下

def test_error_stats():
    """测试错误统计API"""
    print("\n📊 测试错误统计API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/error-stats", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 错误统计API正常工作")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 错误统计API返回错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求错误统计API失败: {str(e)}")

def test_error_details():
    """测试错误详情API"""
    print("\n📋 测试错误详情API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/error-details?limit=5", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 错误详情API正常工作")
            print(f"错误数量: {data.get('data', {}).get('total_count', 0)}")
        else:
            print(f"❌ 错误详情API返回错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求错误详情API失败: {str(e)}")

def test_error_report():
    """测试错误报告API"""
    print("\n📄 测试错误报告API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/error-report?format=json", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 错误报告API正常工作")
            print(f"报告类型: {data.get('data', {}).get('report_info', {}).get('report_type', 'unknown')}")
        else:
            print(f"❌ 错误报告API返回错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求错误报告API失败: {str(e)}")

def test_monitor_status():
    """测试监控状态API"""
    print("\n🔍 测试监控状态API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/error-monitor/status", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 监控状态API正常工作")
            print(f"缓冲区大小: {data.get('data', {}).get('buffer_size', 0)}")
            print(f"监控健康状态: {data.get('data', {}).get('monitor_health', 'unknown')}")
        else:
            print(f"❌ 监控状态API返回错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求监控状态API失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始测试错误统计API")
    print("=" * 50)
    
    # 1. 生成测试错误
    test_generate_errors()
    
    # 等待一下让错误被记录
    print("\n⏳ 等待错误记录...")
    time.sleep(2)
    
    # 2. 测试各个API
    test_error_stats()
    test_error_details()
    test_error_report()
    test_monitor_status()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")
    print("\n💡 现在您可以在浏览器中访问:")
    print(f"   - 错误统计: {BASE_URL}/api/error-stats")
    print(f"   - 错误详情: {BASE_URL}/api/error-details")
    print(f"   - 错误报告: {BASE_URL}/api/error-report")
    print(f"   - 监控状态: {BASE_URL}/api/error-monitor/status")

if __name__ == '__main__':
    main()
