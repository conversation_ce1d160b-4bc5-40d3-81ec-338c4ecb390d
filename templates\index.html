<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入湖数据统计 - 数据管理系统</title>
    <!-- 本地Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <!-- 本地datepicker CSS -->
    <link rel="stylesheet" href="/static/css/bootstrap-datepicker.min.css">
    <!-- jQuery UI CSS -->
    <link rel="stylesheet" href="/static/css/jquery-ui.min.css">
    <!-- 自定义CSS -->
    <link rel="stylesheet" href="/static/css/custom.css">
    <link rel="stylesheet" href="/static/css/jquery-ui.min.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="/static/css/all.min.css">
    <style>
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
        }
        .btn-group {
            margin-bottom: 20px;
        }
        .container {
            margin-top: 80px; /* 增加顶部边距，为导航栏留出空间 */
            max-width: 1400px;  /* 增加容器宽度 */
        }
        /* 美化统计信息卡片 */
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .stats-card p {
            margin-bottom: 10px;
            font-size: 1.1em;
            color: #495057;
        }
        .stats-card strong {
            color: #0d6efd;
        }
        /* 美化选择器区域 */
        .selector-area {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        /* 美化表格 */
        .table-container {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .table {
            margin-bottom: 0;
        }
        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        /* 美化按钮组 */
        .action-buttons {
            margin: 20px 0;
            padding: 15px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .action-buttons button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        /* 页面标题样式 */
        .page-title {
            color: #2c3e50;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        /* 添加自动完成下拉框的样式 */
        .ui-autocomplete {
            max-height: 200px;
            overflow-y: auto;
            overflow-x: hidden;
            z-index: 9999 !important;
            background: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .ui-menu-item {
            padding: 8px 12px;
            cursor: pointer;
        }
        .ui-menu-item:hover {
            background-color: #f8f9fa;
        }
        .ui-menu-item-wrapper {
            padding: 5px;
        }
        .ui-menu-item small {
            color: #6c757d;
            display: block;
            margin-top: 4px;
        }
        .card-header-custom {
            background-color: #f8f9fa;
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 10px;
            font-size: 12px;
        }
        .status-no {
            background-color: #f8f9fa;
            color: #495057;
        }
        .status-yes {
            background-color: #0d6efd;
            color: #fff;
        }
        
        /* 分页样式 */
        .pagination {
            margin-bottom: 0;
        }
        .page-link {
            color: #495057;
            border-color: #dee2e6;
            transition: all 0.2s;
            font-weight: bold;
            font-size: 1.2em;
            padding: 0.375rem 0.75rem;
            min-width: 40px;
            text-align: center;
        }
        .page-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }
        .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }
        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
        
        /* 导航栏样式 */
        .main-navbar {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            padding: 0.5rem 1rem;
        }
        
        .navbar-brand {
            font-size: 1.4rem;
            font-weight: 500;
        }
        
        .navbar-nav {
            display: flex;
            align-items: center;
        }
        
        .nav-item {
            position: relative;
            margin: 0 5px;
        }
        
        .nav-link {
            color: #fff;
            font-weight: 500;
            padding: 0.7rem 1.2rem;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link:focus {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        
        /* 高亮Dashboard链接 */
        .nav-link.dashboard-link {
            background-color: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .nav-link.dashboard-link:hover {
            background-color: rgba(255, 255, 255, 0.25);
        }
        
        /* 下拉菜单样式 */
        .dropdown-menu {
            border: none;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            min-width: 180px;
            animation: fadeIn 0.3s ease;
        }
        
        .dropdown-item {
            padding: 0.6rem 1.2rem;
            color: #495057;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .dropdown-item:hover, .dropdown-item:focus {
            background-color: #f8f9fa;
            color: #3498db;
        }
        
        /* 二级下拉菜单 */
        .dropdown-submenu {
            position: relative;
        }
        
        .dropdown-submenu .dropdown-menu {
            top: 0;
            left: 100%;
            margin-top: -0.5rem;
            margin-left: 0.1rem;
        }
        
        .dropdown-submenu:hover > .dropdown-menu {
            display: block;
        }
        
        .dropdown-submenu > .dropdown-item:after {
            display: inline-block;
            margin-left: 0.5rem;
            content: "\f054";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            font-size: 0.8rem;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 搜索框样式 */
        .search-container {
            position: relative;
            margin-left: auto;
            width: 300px;
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            right: 0;
            width: 100%;
            max-height: 400px;
            overflow-y: auto;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }
        
        .search-results ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .search-results li {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        
        .search-results li:hover {
            background-color: #f5f5f5;
        }
        
        .search-loading, .search-no-results {
            padding: 15px;
            text-align: center;
            color: #666;
        }
        
        .search-loading i {
            margin-right: 5px;
        }
        
        /* 搜索表单样式 */
        #searchForm {
            width: 100%;
        }
        
        #searchForm .btn {
            margin-left: 5px;
        }
        
        /* 搜索结果模态窗口样式 */
        .search-modal .modal-dialog {
            max-width: 90%;
        }
        
        .search-modal .modal-header {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }
        
        .search-modal .modal-title {
            color: #2c3e50;
            font-weight: 500;
        }
        
        /* 移除搜索结果模态窗口的底部和右侧边框 */
        #searchResultModal .modal-content {
            border-right: none;
            border-bottom: none;
            border-radius: 0.3rem 0.3rem 0 0; /* 只保留顶部和左侧的圆角 */
            box-shadow: none; /* 移除阴影 */
        }
        
        #searchResultModal .table {
            border-right: none;
            border-bottom: none;
            margin-bottom: 0; /* 移除表格底部边距 */
        }
        
        #searchResultModal .table td:last-child,
        #searchResultModal .table th:last-child {
            border-right: none;
        }
        
        #searchResultModal .table tr:last-child td {
            border-bottom: none !important; /* 强制移除最后一行的底部边框 */
        }
        
        #searchResultModal .modal-body {
            border-bottom: none;
            padding-bottom: 0; /* 移除底部内边距 */
        }
        
        #searchResultModal .table-responsive {
            border-bottom: none;
            margin-bottom: 0; /* 移除底部边距 */
            overflow-x: hidden; /* 隐藏水平滚动条 */
        }
        
        #searchResultModal .table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            text-align: center;
            vertical-align: middle;
        }
        
        #searchResultModal .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        #searchResultPagination {
            border-bottom: none;
            margin-bottom: 0 !important; /* 强制移除底部边距 */
            margin-top: 15px;
            padding: 10px 10px 0 10px; /* 移除底部内边距 */
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            border-bottom: none !important; /* 强制移除底部边框 */
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        /* 全局样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        
        /* 自定义按钮样式 */
        .btn-light-success {
            color: #28a745;
            background-color: rgba(255, 255, 255, 0.9);
            border-color: transparent;
        }
        
        .btn-light-success:hover {
            color: #fff;
            background-color: #28a745;
        }
        
        .btn-light {
            background-color: rgba(255, 255, 255, 0.9);
            border-color: transparent;
        }
        
        .btn-light:hover {
            background-color: rgba(255, 255, 255, 1);
            border-color: transparent;
        }
        
        /* 搜索框样式优化 */
        #tableSearchInput {
            border-radius: 20px;
            border: none;
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        #searchForm .btn {
            border-radius: 50%;
            width: 38px;
            height: 38px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg main-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-database me-2"></i>数据管理
            </a>
            
            <!-- 添加响应式菜单按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="切换导航">
                <i class="fas fa-bars" style="color: white;"></i>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarContent">
                <!-- 操作按钮组 -->
                <div class="ms-4 d-flex">
                    <button class="btn btn-light-success btn-sm rounded-pill px-4 mx-2" id="addRecordBtn">
                        <i class="fas fa-plus me-1"></i>添加记录
                    </button>
                    <button class="btn btn-light btn-sm rounded-pill px-4 mx-2" id="exportCurrentBtn">
                        <i class="fas fa-download me-1"></i>导出当前数据
                    </button>
                    <button class="btn btn-light btn-sm rounded-pill px-4 mx-2" id="exportAllBtn">
                        <i class="fas fa-file-export me-1"></i>导出所有数据
                    </button>
                </div>
                
                <!-- 搜索框 -->
                <div class="d-flex ms-auto">
                    <div class="search-container">
                        <form id="searchForm" class="d-flex">
                            <input type="text" id="tableSearchInput" class="form-control" placeholder="请输入中英文表名查询" aria-label="请输入中英文表名查询">
                            <button type="submit" class="btn btn-light ms-2">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                        <div id="searchResults" class="search-results"></div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <header class="mb-4">
            <!-- 移除数据管理系统标题 -->
            <!-- 原操作工具按钮区域已移至导航栏，此处隐藏 -->
            <div class="action-buttons" style="display: none;">
                <div class="card-header-custom mb-3">
                    <i class="fas fa-tools me-2"></i>操作工具
                </div>
                <div class="d-flex flex-wrap">
                    <a href="/server_management" class="btn btn-info me-2 mb-2">
                        <i class="fas fa-server me-2"></i>服务器管理
                    </a>
                    <a href="/visualization" class="btn btn-info me-2 mb-2">
                        <i class="fas fa-chart-bar me-2"></i>数据可视化
                    </a>
                    <button type="button" class="btn btn-primary me-2 mb-2" id="toggleEditModeBtn">
                        <i class="fas fa-edit me-2"></i>进入编辑模式
                    </button>
                    <button type="button" class="btn btn-success me-2 mb-2" id="addRecordBtn" style="display: none;">
                        <i class="fas fa-plus me-2"></i>添加新记录
                    </button>
                    <button type="button" class="btn btn-success me-2 mb-2" id="exportAllBtn" style="display: none;">
                        <i class="fas fa-file-export me-2"></i>导出所有数据
                    </button>
                    <button type="button" class="btn btn-info me-2 mb-2" id="exportCurrentBtn" style="display: none;">
                        <i class="fas fa-download me-2"></i>导出当前数据
                    </button>
                    <button type="button" class="btn btn-warning me-2 mb-2" id="yearlyIncrementBtn" style="display: none;">
                        <i class="fas fa-chart-bar me-2"></i>年度新增统计
                    </button>
                </div>
            </div>
        </header>

        <!-- 统计信息卡片 -->
        <div class="stats-card">
            <div class="card-header-custom">
                <i class="fas fa-chart-pie me-2"></i>数据统计
            </div>
            <div class="row mt-3">
                {% if table_stats %}
                    {% if table_stats.is_provider_view %}
                        <div class="col-md-6">
                            <p><strong><i class="fas fa-map-marker-alt me-2"></i>当前区县：</strong><span class="value">{{ table_stats.current_county }}</span></p>
                            <p><strong><i class="fas fa-building me-2"></i>当前单位：</strong><span class="value">{{ table_stats.current_provider }}</span></p>
                            <p><strong><i class="fas fa-database me-2"></i>总条数：</strong><span class="value">{{ table_stats.total_records }}</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong><i class="fas fa-percentage me-2"></i>占比：</strong><span class="value">{{ table_stats.percentage }}%</span></p>
                            <p><strong><i class="fas fa-chart-line me-2"></i>年度新增：</strong><span class="value">{{ table_stats.yearly_increment }}</span></p>
                        </div>
                    {% else %}
                        <div class="col-md-6">
                            <p><strong><i class="fas fa-map-marker-alt me-2"></i>当前区县：</strong><span class="value">{{ table_stats.current_county }}</span></p>
                            <p><strong><i class="fas fa-building me-2"></i>单位数量：</strong><span class="value">{{ table_stats.provider_count }}</span></p>
                            <p><strong><i class="fas fa-database me-2"></i>总条数：</strong><span class="value">{{ table_stats.total_records }}</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong><i class="fas fa-user-friends me-2"></i>人均汇聚量：</strong><span class="value">{{ table_stats.per_capita }}</span></p>
                            {% if not table_stats.is_city_unit %}
                                <p><strong><i class="fas fa-user-friends me-2"></i>总人口：</strong><span class="value">{{ table_stats.total_population }}人</span></p>
                            {% endif %}
                            <p><strong><i class="fas fa-chart-line me-2"></i>年度新增：</strong><span class="value">{{ table_stats.yearly_increment }}</span></p>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
        
        <!-- 工具卡片区域 -->
        <div class="row mb-4">
            <!-- 网络小工具卡片 -->
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <i class="fas fa-network-wired me-2"></i>网络小工具
                        </h5>
                        <p class="card-text">
                            提供常用的网络诊断工具，如 Ping 和 Telnet 端口测试。
                        </p>
                        <a href="{{ url_for('net_tools_bp.network_tools_ui') }}" class="btn btn-primary mt-auto align-self-start">
                            <i class="fas fa-tools me-1"></i>进入工具
                        </a>
                    </div>
                </div>
            </div>
            <!-- 这里可以添加更多工具卡片 -->
        </div>
        
        <!-- 选择器区域 -->
        <div class="selector-area">
            <div class="card-header-custom mb-3">
                <i class="fas fa-filter me-2"></i>数据筛选
            </div>
            <div class="row g-2 align-items-end">
                <div class="col-md-6">
                        <label for="tableSelect">选择区县:</label>
                    <select class="form-control" id="tableSelect" name="table" onchange="changeTable(this.value)">
                            {% for table in tables %}
                            {% if table.startswith('excel_data_') %}
                            <option value="{{ table }}" {% if current_table == table %}selected{% endif %}>
                                {{ county_names.get(table, table.replace('excel_data_', '').replace('_', ' ')) }}
                            </option>
                            {% endif %}
                            {% endfor %}
                        </select>
                </div>
                <div class="col-md-6">
                    <label for="providerSelect" class="form-label"><i class="fas fa-building me-2"></i>选择单位:</label>
                    <select class="form-control" id="providerSelect" onchange="changeProvider(this.options[this.selectedIndex].text, this.options[this.selectedIndex].value)">
                        <option value="">全部</option>
                        {% for provider in providers %}
                        <option value="{{ provider.org_code }}" {% if provider.name == current_provider %}selected{% endif %}>
                            {{ provider.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <!-- 表格容器 -->
        <div class="table-container">
            <div class="card-header-custom mb-3">
                <i class="fas fa-list me-2"></i>数据列表
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-building me-1"></i>单位名称</th>
                            <th><i class="fas fa-font me-1"></i>中文表名</th>
                            <th><i class="fas fa-table me-1"></i>表名</th>
                            <th><i class="fas fa-database me-1"></i>数据条数</th>
                            <th><i class="fas fa-calendar-alt me-1"></i>提供时间</th>
                            <th><i class="fas fa-paper-plane me-1"></i>是否推送</th>
                            <th><i class="fas fa-cloud-upload-alt me-1"></i>是否入湖</th>
                            <th><i class="fas fa-cogs me-1"></i>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in data %}
                        <tr>
                            <td>{{ row[7] }}</td>
                            <td>{{ row[1] }}</td>
                            <td>{{ row[2] }}</td>
                            <td>{{ row[3] }}</td>
                            <td>{{ row[4][:10] if row[4] else '' }}</td>
                            <td>
                                <span class="status-badge {% if row[5] == 0 or row[5] == '0' %}status-no{% else %}status-yes{% endif %}">
                                    {{ '否' if row[5] == 0 or row[5] == '0' else '是' }}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge {% if row[6] == 0 or row[6] == '0' %}status-no{% else %}status-yes{% endif %}">
                                    {{ '否' if row[6] == 0 or row[6] == '0' else '是' }}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-warning btn-sm edit-btn" style="display: none;" data-id="{{ row[0] }}">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-danger btn-sm delete-btn" style="display: none;" data-id="{{ row[0] }}">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页控件 -->
            {% if total_pages is defined and total_pages > 1 %}
            <nav aria-label="数据分页" class="mt-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted" data-total-count="{{ total_count }}" data-current-page="{{ page }}" data-total-pages="{{ total_pages }}">共 {{ total_count }} 条记录，当前显示第 {{ page }} 页，共 {{ total_pages }} 页</small>
                    </div>
                    <ul class="pagination">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="javascript:void(0)" data-page="{{ 1 }}" onclick="changePage(this.getAttribute('data-page'))" aria-label="首页" title="首页">&laquo;</a>
                        </li>
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="javascript:void(0)" 
                               data-prev-page="{% if page > 1 %}{{ page - 1 }}{% else %}1{% endif %}"
                               onclick="changePage(parseInt(this.getAttribute('data-prev-page')))" 
                               aria-label="上一页" title="上一页">&lsaquo;</a>
                        </li>
                        
                        {% set start_page = [page - 2, 1] | max %}
                        {% set end_page = [start_page + 4, total_pages] | min %}
                        {% set start_page = [end_page - 4, 1] | max %}
                        
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="javascript:void(0)" data-page="{{ p }}" onclick="changePage(this.getAttribute('data-page'))" title="第{{ p }}页">{{ p }}</a>
                        </li>
                        {% endfor %}
                        
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="javascript:void(0)" 
                               data-next-page="{% if page < total_pages %}{{ page + 1 }}{% else %}{{ total_pages }}{% endif %}"
                               onclick="changePage(parseInt(this.getAttribute('data-next-page')))" 
                               aria-label="下一页" title="下一页">&rsaquo;</a>
                        </li>
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="javascript:void(0)" data-page="{{ total_pages }}" onclick="changePage(this.getAttribute('data-page'))" aria-label="末页" title="末页">&raquo;</a>
                        </li>
                    </ul>
                </div>
            </nav>
            {% endif %}
        </div>
        
        <footer class="mt-5 text-center text-muted">
            <p><small> 2025 数据管理系统 | 版本 <span id="appVersion">v4</span>
                <button type="button" class="btn btn-link p-0 ms-2" id="changelogBtn" style="font-size: 1em;vertical-align: baseline;">更新日志</button>
            </small></p>
        </footer>
    </div>

    <!-- 编辑/添加记录模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="modalTitle" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">编辑记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <div id="dynamicFields">
                            <!-- 动态字段将在这里生成 -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveData">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索结果模态窗口 -->
    <div class="modal fade search-modal" id="searchResultModal" tabindex="-1" aria-labelledby="searchResultModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchResultModalLabel">搜索结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr id="searchResultTableHeader"></tr>
                            </thead>
                            <tbody id="searchResultTableBody"></tbody>
                        </table>
                    </div>
                    <div id="searchResultPagination" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 版本更新日志模态框 -->
    {% include 'changelog_modal.html' %}

    <!-- 本地JavaScript文件 -->
    <script src="/static/js/lib/jquery-3.6.0.min.js"></script>
    <script src="/static/js/lib/jquery-ui.min.js"></script>
    <script src="/static/js/lib/bootstrap.bundle.min.js"></script>
    <script src="/static/js/lib/bootstrap-datepicker.min.js"></script>
    <script src="/static/js/lib/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="/static/js/main.js"></script>
    
    <script>
        // 搜索框功能
        var showTableDataModal;
        var showMultipleTablesDataModal;
        
        $(document).ready(function() {
            // 监听搜索表单提交
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                const keyword = $('#tableSearchInput').val().trim();
                
                if (keyword === '') {
                    $('#searchResults').hide();
                    return;
                }
                
                // 显示加载中提示
                $('#searchResults').html('<div class="search-loading"><i class="fas fa-spinner fa-spin"></i> 正在搜索...</div>').show();
                
                // 发送搜索请求
                $.ajax({
                    url: '/lookup/tables',
                    method: 'GET',
                    data: { keyword: keyword },
                    success: function(data) {
                        if (data.length === 0) {
                            $('#searchResults').html('<div class="search-no-results">未找到匹配的数据表</div>');
                            return;
                        }
                        
                        // 如果只有一个结果，直接显示数据
                        if (data.length === 1) {
                            showTableDataModal(data[0].table);
                            $('#searchResults').hide();
                            return;
                        }
                        
                        // 如果有多个结果，显示列表
                        const $results = $('#searchResults');
                        $results.empty();
                        
                        // 创建结果列表
                        const $ul = $('<ul></ul>');
                        data.forEach(function(item) {
                            const $li = $('<li></li>');
                            $li.text(`${item.display_name} - ${item.comment}`);
                            $li.attr('data-table', item.table);
                            $li.on('click', function() {
                                showTableDataModal($(this).attr('data-table'));
                            });
                            $ul.append($li);
                        });
                        $results.append($ul);
                        
                        // 显示结果框
                        $results.show();
                        
                        // 如果所有结果都是内容匹配（不是表名匹配），则直接显示合并数据
                        const contentMatches = data.filter(item => item.match_type === 'content');
                        if (contentMatches.length > 0 && contentMatches.length === data.length) {
                            showMultipleTablesDataModal(data.map(item => item.table), keyword);
                            $('#searchResults').hide();
                        }
                    },
                    error: function(error) {
                        $('#searchResults').html('<div class="search-no-results">搜索出错，请重试</div>');
                    }
                });
            });
            
            // 点击页面其他地方时隐藏结果框
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.search-container').length) {
                    $('#searchResults').hide();
                }
            });
            
            // 显示表数据模态窗口
            showTableDataModal = function(tableName, page = 1) {
                // 清空并隐藏搜索结果
                $('#searchResults').hide();
                
                // 获取当前搜索关键词
                const keyword = $('#tableSearchInput').val().trim();
                
                // 清空搜索框
                $('#tableSearchInput').val('');
                
                // 显示加载中状态
                $('#searchResultModalLabel').text('正在加载数据...');
                $('#searchResultTableHeader').empty();
                $('#searchResultTableBody').empty();
                $('#searchResultPagination').empty().hide();
                
                // 显示模态窗口
                const modal = new bootstrap.Modal(document.getElementById('searchResultModal'));
                modal.show();
                
                // 获取表数据
                $.ajax({
                    url: '/lookup/table_data',
                    method: 'GET',
                    data: { 
                        table: tableName,
                        page: page,
                        keyword: keyword
                    },
                    success: function(response) {
                        // 更新模态窗口标题
                        $('#searchResultModalLabel').text(`${response.display_name} - ${response.comment}`);
                        
                        // 构建表头
                        const $header = $('#searchResultTableHeader');
                        $header.empty();
                        
                        // 定义表头图标映射
                        const headerIcons = {
                            '单位名称': '<i class="fas fa-building me-1"></i>',
                            '中文表名': '<i class="fas fa-font me-1"></i>',
                            '表名': '<i class="fas fa-table me-1"></i>',
                            '数据条数': '<i class="fas fa-database me-1"></i>',
                            '提供时间': '<i class="fas fa-calendar-alt me-1"></i>',
                            '是否推送': '<i class="fas fa-paper-plane me-1"></i>',
                            '是否入湖': '<i class="fas fa-cloud-upload-alt me-1"></i>'
                        };
                        
                        response.column_display_names.forEach(function(columnName) {
                            // 检查是否有对应的图标，如果有则添加图标
                            const icon = headerIcons[columnName] || '';
                            $header.append(`<th>${icon}${columnName}</th>`);
                        });
                        
                        // 构建表体
                        const $body = $('#searchResultTableBody');
                        $body.empty();
                        
                        if (response.data.length === 0) {
                            $body.html('<tr><td colspan="100%" class="text-center">没有数据</td></tr>');
                        } else {
                            response.data.forEach(function(row) {
                                const $tr = $('<tr></tr>');
                                
                                // 添加每个单元格
                                for (let i = 0; i < row.length; i++) {
                                    let cell = row[i];
                                    
                                    // 处理特殊列
                                    const columnName = response.column_display_names[i];
                                    if (columnName === '是否推送' || columnName === '是否入湖') {
                                        // 为是/否添加状态样式
                                        const isYes = (cell === '是' || cell === 1);
                                        const statusClass = isYes ? 'status-yes' : 'status-no';
                                        const displayText = isYes ? '是' : '否';
                                        
                                        $tr.append(`<td><span class="status-badge ${statusClass}">${displayText}</span></td>`);
                                    } else {
                                        // 普通单元格
                                        $tr.append(`<td>${cell !== null ? cell : ''}</td>`);
                                    }
                                }
                                
                                $body.append($tr);
                            });
                        }
                        
                        // 添加分页信息和按钮
                        if (response.total_pages > 1) {
                            const $pagination = $('#searchResultPagination');
                            $pagination.empty();
                            
                            const $paginationInfo = $('<div class="d-flex justify-content-between align-items-center mb-2"></div>');
                            const startRecord = (response.page - 1) * response.per_page + 1;
                            const endRecord = Math.min(response.page * response.per_page, response.total_count);
                            
                            $paginationInfo.append(`<span>显示 ${startRecord}-${endRecord} 条记录，共 ${response.total_count} 条</span>`);
                            $paginationInfo.append(`<span>每页最多显示${response.per_page}条记录</span>`);
                            
                            $pagination.append($paginationInfo);
                            
                            // 添加分页按钮
                            const $paginationButtons = $('<nav><ul class="pagination justify-content-center"></ul></nav>');
                            const $ul = $paginationButtons.find('ul');
                            
                            // 首页按钮
                            $ul.append(`<li class="page-item ${response.page === 1 ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showTableDataModal('${tableName}', 1)" aria-label="首页" title="首页">&laquo;</a></li>`);
                            
                            // 上一页按钮
                            $ul.append(`<li class="page-item ${response.page === 1 ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showTableDataModal('${tableName}', ${response.page > 1 ? response.page - 1 : 1})" aria-label="上一页" title="上一页">&lsaquo;</a></li>`);
                            
                            // 页码按钮
                            let startPage = Math.max(1, response.page - 2);
                            let endPage = Math.min(response.total_pages, startPage + 4);
                            
                            if (endPage - startPage < 4) {
                                startPage = Math.max(1, endPage - 4);
                            }
                            
                            for (let i = startPage; i <= endPage; i++) {
                                $ul.append(`<li class="page-item ${i === response.page ? 'active' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showTableDataModal('${tableName}', ${i})" title="第${i}页">${i}</a></li>`);
                            }
                            
                            // 下一页按钮
                            $ul.append(`<li class="page-item ${response.page === response.total_pages ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showTableDataModal('${tableName}', ${response.page < response.total_pages ? response.page + 1 : response.total_pages})" aria-label="下一页" title="下一页">&rsaquo;</a></li>`);
                            
                            // 末页按钮
                            $ul.append(`<li class="page-item ${response.page === response.total_pages ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showTableDataModal('${tableName}', ${response.total_pages})" aria-label="末页" title="末页">&raquo;</a></li>`);
                            
                            $pagination.append($paginationButtons);
                            $pagination.show();
                        } else {
                            // 只有一页的情况
                            const $pagination = $('#searchResultPagination');
                            $pagination.html(`<div class="d-flex justify-content-between align-items-center"><span>显示 ${response.data.length} 条记录，共 ${response.total_count} 条</span><span>每页最多显示${response.per_page}条记录</span></div>`).show();
                        }
                    },
                    error: function(error) {
                        $('#searchResultModalLabel').text('加载数据失败');
                        $('#searchResultTableBody').html(`<tr><td colspan="100%" class="text-center text-danger">获取数据失败: ${error.responseJSON?.error || '未知错误'}</td></tr>`);
                        $('#searchResultPagination').hide();
                    }
                });
            };
            
            // 显示多表数据模态窗口
            showMultipleTablesDataModal = function(tables, keyword, page = 1) {
                // 清空并隐藏搜索结果
                $('#searchResults').hide();
                
                // 清空搜索框
                $('#tableSearchInput').val('');
                
                // 显示加载中状态
                $('#searchResultModalLabel').text('正在加载数据...');
                $('#searchResultTableHeader').empty();
                $('#searchResultTableBody').empty();
                $('#searchResultPagination').empty().hide();
                
                // 显示模态窗口
                const modal = new bootstrap.Modal(document.getElementById('searchResultModal'));
                modal.show();
                
                // 获取多表数据
                $.ajax({
                    url: '/get_multiple_tables_data',
                    method: 'GET',
                    data: { 
                        'tables[]': tables,
                        'keyword': keyword,
                        'page': page
                    },
                    success: function(response) {
                        // 更新模态窗口标题
                        $('#searchResultModalLabel').text(`搜索结果: "${keyword}"`);
                        
                        // 构建表头
                        const $header = $('#searchResultTableHeader');
                        $header.empty();
                        
                        // 定义表头图标映射
                        const headerIcons = {
                            '单位名称': '<i class="fas fa-building me-1"></i>',
                            '中文表名': '<i class="fas fa-font me-1"></i>',
                            '表名': '<i class="fas fa-table me-1"></i>',
                            '数据条数': '<i class="fas fa-database me-1"></i>',
                            '提供时间': '<i class="fas fa-calendar-alt me-1"></i>',
                            '是否推送': '<i class="fas fa-paper-plane me-1"></i>',
                            '是否入湖': '<i class="fas fa-cloud-upload-alt me-1"></i>'
                        };
                        
                        response.column_display_names.forEach(function(columnName) {
                            // 检查是否有对应的图标，如果有则添加图标
                            const icon = headerIcons[columnName] || '';
                            $header.append(`<th>${icon}${columnName}</th>`);
                        });
                        
                        // 构建表体
                        const $body = $('#searchResultTableBody');
                        $body.empty();
                        
                        if (response.data.length === 0) {
                            $body.html('<tr><td colspan="100%" class="text-center">没有数据</td></tr>');
                        } else {
                            response.data.forEach(function(row) {
                                const $tr = $('<tr></tr>');
                                
                                // 添加每个单元格
                                for (let i = 0; i < row.length; i++) {
                                    let cell = row[i];
                                    
                                    // 处理特殊列
                                    const columnName = response.column_display_names[i];
                                    if (columnName === '是否推送' || columnName === '是否入湖') {
                                        // 为是/否添加状态样式
                                        const isYes = (cell === '是' || cell === 1);
                                        const statusClass = isYes ? 'status-yes' : 'status-no';
                                        const displayText = isYes ? '是' : '否';
                                        
                                        $tr.append(`<td><span class="status-badge ${statusClass}">${displayText}</span></td>`);
                                    } else {
                                        // 普通单元格
                                        $tr.append(`<td>${cell !== null ? cell : ''}</td>`);
                                    }
                                }
                                
                                $body.append($tr);
                            });
                        }
                        
                        // 添加分页信息和按钮
                        if (response.total_pages > 1) {
                            const $pagination = $('#searchResultPagination');
                            $pagination.empty();
                            
                            const $paginationInfo = $('<div class="d-flex justify-content-between align-items-center mb-2"></div>');
                            const startRecord = (response.page - 1) * response.per_page + 1;
                            const endRecord = Math.min(response.page * response.per_page, response.total_rows);
                            
                            $paginationInfo.append(`<span>显示 ${startRecord}-${endRecord} 条记录，共 ${response.total_rows} 条</span>`);
                            $paginationInfo.append(`<span>每页最多显示${response.per_page}条记录</span>`);
                            
                            $pagination.append($paginationInfo);
                            
                            // 添加分页按钮
                            const $paginationButtons = $('<nav><ul class="pagination justify-content-center"></ul></nav>');
                            const $ul = $paginationButtons.find('ul');
                            
                            // 首页按钮
                            $ul.append(`<li class="page-item ${response.page === 1 ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showMultipleTablesDataModal(${JSON.stringify(tables).replace(/"/g, '&quot;')}, '${keyword}', 1)" aria-label="首页"><i class="fas fa-angle-double-left"></i></a></li>`);
                            
                            // 上一页按钮
                            $ul.append(`<li class="page-item ${response.page === 1 ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showMultipleTablesDataModal(${JSON.stringify(tables).replace(/"/g, '&quot;')}, '${keyword}', ${response.page > 1 ? response.page - 1 : 1})" aria-label="上一页"><i class="fas fa-angle-left"></i></a></li>`);
                            
                            // 页码按钮
                            let startPage = Math.max(1, response.page - 2);
                            let endPage = Math.min(response.total_pages, startPage + 4);
                            if (endPage - startPage < 4 && startPage > 1) {
                                startPage = Math.max(1, endPage - 4);
                            }
                            
                            for (let i = startPage; i <= endPage; i++) {
                                $ul.append(`<li class="page-item ${i === response.page ? 'active' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showMultipleTablesDataModal(${JSON.stringify(tables).replace(/"/g, '&quot;')}, '${keyword}', ${i})">${i}</a></li>`);
                            }
                            
                            // 下一页按钮
                            $ul.append(`<li class="page-item ${response.page === response.total_pages ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showMultipleTablesDataModal(${JSON.stringify(tables).replace(/"/g, '&quot;')}, '${keyword}', ${response.page < response.total_pages ? response.page + 1 : response.total_pages})" aria-label="下一页"><i class="fas fa-angle-right"></i></a></li>`);
                            
                            // 末页按钮
                            $ul.append(`<li class="page-item ${response.page === response.total_pages ? 'disabled' : ''}"><a class="page-link" href="javascript:void(0)" onclick="showMultipleTablesDataModal(${JSON.stringify(tables).replace(/"/g, '&quot;')}, '${keyword}', ${response.total_pages})" aria-label="末页"><i class="fas fa-angle-double-right"></i></a></li>`);
                            
                            $pagination.append($paginationButtons);
                            $pagination.show();
                        } else {
                            // 只有一页的情况
                            const $pagination = $('#searchResultPagination');
                            $pagination.html(`<div class="d-flex justify-content-between align-items-center"><span>显示 ${response.data.length} 条记录，共 ${response.total_rows} 条</span><span>每页最多显示${response.per_page}条记录</span></div>`).show();
                        }
                    },
                    error: function(error) {
                        $('#searchResultModalLabel').text('加载数据失败');
                        $('#searchResultTableBody').html(`<tr><td colspan="100%" class="text-center text-danger">获取数据失败: ${error.responseJSON?.error || '未知错误'}</td></tr>`);
                        $('#searchResultPagination').hide();
                    }
                });
            };
        });
    </script>
    
    <script>
        $(document).ready(function() {
            const totalCount = parseInt($('small.text-muted').attr('data-total-count'));
            const currentPage = parseInt($('small.text-muted').attr('data-current-page'));
            const totalPages = parseInt($('small.text-muted').attr('data-total-pages'));
            
            const pagination = $('nav[aria-label="数据分页"]');
            const paginationInfo = pagination.find('small.text-muted');
            paginationInfo.text(`共 ${totalCount} 条记录，当前显示第 ${currentPage} 页，共 ${totalPages} 页`);
            
            const prevPageLink = pagination.find('a[aria-label="上一页"]');
            prevPageLink.attr('data-prev-page', currentPage > 1 ? currentPage - 1 : 1);
            prevPageLink.attr('onclick', `changePage(parseInt(this.getAttribute('data-prev-page')))`);
            
            const nextPageLink = pagination.find('a[aria-label="下一页"]');
            nextPageLink.attr('data-next-page', currentPage < totalPages ? currentPage + 1 : totalPages);
            nextPageLink.attr('onclick', `changePage(parseInt(this.getAttribute('data-next-page')))`);
        });
    </script>
    
    <!-- 版本提醒脚本 -->
    <script src="/static/js/version-notification.js"></script>
    <script>
      $(function(){
        $('#changelogBtn').on('click', function(){
          var modal = new bootstrap.Modal(document.getElementById('changelogModal'));
          modal.show();
        });

        // 导出当前数据
        $('#exportCurrentBtn').on('click', function(e){
          e.preventDefault();
          const currentTable = $('#tableSelect').val();
          const currentProvider = $('#providerSelect').val();
          
          if (!currentTable) {
            alert('请先选择区县！');
            return;
          }
          
          const url = `/exports/current?table=${currentTable}${currentProvider ? `&provider=${currentProvider}` : ''}`;
          window.location.href = url;
        });

        // 导出所有数据
        $('#exportAllBtn').on('click', function(e){
          e.preventDefault();
          const currentTable = $('#tableSelect').val();
          
          if (!currentTable) {
            alert('请先选择区县！');
            return;
          }
          
          const url = `/exports/all`;
          window.location.href = url;
        });
      });
    </script>
</body>
</html>
