# db_config.py - 数据库全局配置文件
# 注意：此文件已重构为使用配置管理器，敏感信息通过环境变量管理

import logging
from src.utils.config_manager import config_manager

logger = logging.getLogger(__name__)

# 获取数据库配置
_db_config = config_manager.get_database_config()

# 数据库连接信息（从配置管理器获取）
DB_HOST = _db_config['host']
DB_PORT = _db_config['port']
DB_USER = _db_config['user']
DB_PASSWORD = _db_config['password']
DB_NAME_MAIN = _db_config['database_main']
DB_NAME_MYSQL_LOG = _db_config['database_mysql_log']
DB_NAME_ANSIBLE = _db_config['database_ansible']

# 处理密码配置
if not DB_PASSWORD:
    logger.warning("数据库密码未设置，使用默认密码（仅适用于开发环境）")
    DB_PASSWORD = "123456"  # 开发环境默认密码

# 数据库名称常量，用于信息模式查询等
DB_SCHEMA_MAIN = DB_NAME_MAIN  # 用于信息模式查询

# MySQL审计模块数据库配置 - 保持与原配置文件相同的结构
DB_CONFIG = {
    'host': DB_HOST,
    'port': DB_PORT,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME_MYSQL_LOG
}

# 构建数据库连接字符串的安全函数
def build_db_uri(database_name: str) -> str:
    """安全地构建数据库连接字符串"""
    if not DB_PASSWORD:
        logger.warning("数据库密码未配置，使用默认密码")
        # 在开发环境使用默认密码，生产环境应该通过环境变量设置
        password = "123456"  # 默认密码，仅用于开发环境
    else:
        password = DB_PASSWORD

    return f'mysql+pymysql://{DB_USER}:{password}@{DB_HOST}:{DB_PORT}/{database_name}'

# 数据库连接字符串
try:
    ANSIBLE_DB_URI = build_db_uri(DB_NAME_ANSIBLE)
    MAIN_DB_URI = build_db_uri(DB_NAME_MAIN)
    logger.info("数据库连接字符串构建成功")
except Exception as e:
    logger.error(f"数据库配置错误: {str(e)}")
    # 使用默认配置作为备用
    ANSIBLE_DB_URI = f'mysql+pymysql://{DB_USER}:123456@{DB_HOST}:{DB_PORT}/{DB_NAME_ANSIBLE}'
    MAIN_DB_URI = f'mysql+pymysql://{DB_USER}:123456@{DB_HOST}:{DB_PORT}/{DB_NAME_MAIN}'