# db_config.py - 数据库全局配置文件
# 临时回退到简单配置，避免循环导入问题

import os
import logging

logger = logging.getLogger(__name__)

# 数据库连接信息
DB_HOST = os.environ.get('DATABASE_HOST', '**************')
DB_PORT = int(os.environ.get('DATABASE_PORT', '3310'))
DB_USER = os.environ.get('DATABASE_USER', 'root')
DB_PASSWORD = os.environ.get('DB_PASSWORD', '123456')  # 默认密码用于开发环境
DB_NAME_MAIN = os.environ.get('DATABASE_MAIN', 'excel')
DB_NAME_MYSQL_LOG = os.environ.get('DATABASE_MYSQL_LOG', 'mysql_log')
DB_NAME_ANSIBLE = os.environ.get('DATABASE_ANSIBLE', 'ansible_ui')

# 如果没有设置密码，使用默认值并警告
if DB_PASSWORD == '123456' and not os.environ.get('DB_PASSWORD'):
    logger.warning("使用默认数据库密码，生产环境请设置 DB_PASSWORD 环境变量")

# 数据库名称常量，用于信息模式查询等
DB_SCHEMA_MAIN = DB_NAME_MAIN  # 用于信息模式查询

# MySQL审计模块数据库配置 - 保持与原配置文件相同的结构
DB_CONFIG = {
    'host': DB_HOST,
    'port': DB_PORT,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME_MYSQL_LOG
}

# Ansible UI数据库连接字符串
ANSIBLE_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_ANSIBLE}'

# 主应用数据库连接字符串
MAIN_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_MAIN}'

logger.info(f"数据库配置加载完成 - 主机: {DB_HOST}:{DB_PORT}, 用户: {DB_USER}")