# db_config.py - 数据库全局配置文件

# 数据库连接信息
DB_HOST = '**************'
DB_PORT = 3310
DB_USER = 'root'
DB_PASSWORD = '123456'
DB_NAME_MAIN = 'excel'
DB_NAME_MYSQL_LOG = 'mysql_log'
DB_NAME_ANSIBLE = 'ansible_ui'

# 数据库名称常量，用于信息模式查询等
DB_SCHEMA_MAIN = DB_NAME_MAIN  # 用于信息模式查询

# MySQL审计模块数据库配置 - 保持与原配置文件相同的结构
DB_CONFIG = {
    'host': DB_HOST,
    'port': DB_PORT,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME_MYSQL_LOG
}

# Ansible UI数据库连接字符串
ANSIBLE_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_ANSIBLE}'

# 主应用数据库连接字符串
MAIN_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_MAIN}' 