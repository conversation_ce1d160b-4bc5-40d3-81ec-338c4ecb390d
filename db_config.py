# db_config.py - 数据库全局配置文件
# 所有配置从 .env 文件读取

import os
import logging

logger = logging.getLogger(__name__)

# 从 .env 文件读取数据库配置
DB_HOST = os.environ.get('DB_HOST', '**************')
DB_PORT = int(os.environ.get('DB_PORT', '3310'))
DB_USER = os.environ.get('DB_USER', 'root')
DB_PASSWORD = os.environ.get('DB_PASSWORD', '123456')
DB_NAME_MAIN = os.environ.get('DB_NAME_MAIN', 'excel')
DB_NAME_MYSQL_LOG = os.environ.get('DB_NAME_MYSQL_LOG', 'mysql_log')
DB_NAME_ANSIBLE = os.environ.get('DB_NAME_ANSIBLE', 'ansible_ui')

# 数据库名称常量，用于信息模式查询等
DB_SCHEMA_MAIN = DB_NAME_MAIN

# MySQL审计模块数据库配置
DB_CONFIG = {
    'host': DB_HOST,
    'port': DB_PORT,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME_MYSQL_LOG
}

# 数据库连接字符串
ANSIBLE_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_ANSIBLE}'
MAIN_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_MAIN}'

logger.info(f"数据库配置加载完成 - 主机: {DB_HOST}:{DB_PORT}, 用户: {DB_USER}")