#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置验证工具入口
简化的配置验证脚本，调用 src.utils.validate_config 模块
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == '__main__':
    try:
        from src.utils.validate_config import main
        main()
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        print("请确保在项目根目录运行此脚本")
        print("\n💡 替代方案:")
        print("1. 在项目根目录运行: python validate_config.py")
        print("2. 直接运行工具脚本: python src/utils/validate_config.py")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行错误: {str(e)}")
        sys.exit(1)
