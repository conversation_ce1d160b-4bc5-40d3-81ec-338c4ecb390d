#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置验证脚本
检查 .env 文件中的配置是否完整和正确
"""

import os
import sys

def load_env_file():
    """加载 .env 文件"""
    if os.path.exists('.env'):
        print("📁 加载 .env 配置文件...")
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print("✅ .env 文件加载完成")
        return True
    else:
        print("❌ .env 文件不存在")
        return False

def validate_config():
    """验证配置"""
    print("\n🔍 验证配置...")
    
    # 必需的配置项
    required_configs = {
        'DB_HOST': '数据库主机地址',
        'DB_PORT': '数据库端口',
        'DB_USER': '数据库用户名',
        'DB_PASSWORD': '数据库密码',
        'DB_NAME_MAIN': '主数据库名',
        'DB_NAME_MYSQL_LOG': 'MySQL审计数据库名',
        'DB_NAME_ANSIBLE': 'Ansible数据库名',
        'SECRET_KEY': 'Flask会话密钥',
        'JWT_SECRET': 'JWT认证密钥',
        'EDIT_PASSWORD_HASH': '编辑模式密码哈希'
    }
    
    # 可选的配置项
    optional_configs = {
        'ANSIBLE_HOST': 'Ansible主机地址',
        'JUMP_HOST': '跳板机地址',
        'JUMP_PORT': '跳板机端口',
        'ANSIBLE_PORT': 'Ansible端口'
    }
    
    missing_configs = []
    invalid_configs = []
    
    # 检查必需配置
    for key, description in required_configs.items():
        value = os.environ.get(key)
        if not value:
            missing_configs.append(f"{key} ({description})")
        else:
            # 特殊验证
            if key == 'DB_PORT':
                try:
                    port = int(value)
                    if port < 1 or port > 65535:
                        invalid_configs.append(f"{key}: 端口号必须在1-65535之间")
                except ValueError:
                    invalid_configs.append(f"{key}: 必须是有效的数字")
            elif key == 'SECRET_KEY':
                if len(value) < 32:
                    invalid_configs.append(f"{key}: 长度必须至少32个字符")
            elif key == 'DB_PASSWORD':
                if value == '123456':
                    print(f"⚠️  {key}: 使用默认密码，生产环境请更改")
    
    # 检查可选配置
    for key, description in optional_configs.items():
        value = os.environ.get(key)
        if value:
            if key in ['JUMP_PORT', 'ANSIBLE_PORT']:
                try:
                    port = int(value)
                    if port < 1 or port > 65535:
                        invalid_configs.append(f"{key}: 端口号必须在1-65535之间")
                except ValueError:
                    invalid_configs.append(f"{key}: 必须是有效的数字")
    
    # 输出结果
    if missing_configs:
        print("\n❌ 缺少必需配置:")
        for config in missing_configs:
            print(f"   - {config}")
    
    if invalid_configs:
        print("\n❌ 配置格式错误:")
        for config in invalid_configs:
            print(f"   - {config}")
    
    if not missing_configs and not invalid_configs:
        print("\n✅ 所有配置验证通过")
        return True
    else:
        print(f"\n❌ 发现 {len(missing_configs + invalid_configs)} 个配置问题")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🔌 测试数据库连接...")
    
    try:
        # 先加载配置
        from db_config import DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME_MAIN
        
        # 尝试连接数据库
        import pymysql
        connection = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME_MAIN,
            connect_timeout=5
        )
        connection.close()
        print("✅ 数据库连接成功")
        return True
        
    except ImportError:
        print("⚠️  pymysql 未安装，跳过数据库连接测试")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 配置验证工具")
    print("=" * 50)
    
    # 加载配置
    if not load_env_file():
        print("\n💡 建议:")
        print("1. 复制 .env.example 为 .env")
        print("2. 编辑 .env 文件，填入您的配置")
        print("3. 重新运行此验证脚本")
        return False
    
    # 验证配置
    config_valid = validate_config()
    
    # 测试数据库连接
    if config_valid:
        db_valid = test_database_connection()
    else:
        db_valid = False
    
    # 总结
    print("\n" + "=" * 50)
    if config_valid and db_valid:
        print("🎉 配置验证完成，系统可以正常启动")
        print("\n启动命令: python start.py")
        return True
    else:
        print("❌ 配置验证失败，请修复上述问题后重试")
        print("\n💡 帮助:")
        print("1. 检查 .env 文件是否存在")
        print("2. 确认所有必需配置都已设置")
        print("3. 验证数据库连接信息是否正确")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
