# 统一错误处理机制

## 概述

为了提高系统的稳定性、可维护性和用户体验，我们实施了统一的错误处理机制。该机制提供了标准化的错误处理、日志记录、监控和报告功能。

## 核心特性

### 1. 统一异常类型
- **SystemError**: 系统自定义异常基类
- **DatabaseError**: 数据库相关错误
- **NetworkError**: 网络相关错误
- **ValidationError**: 数据验证错误
- **BusinessError**: 业务逻辑错误

### 2. 错误级别分类
- **LOW**: 低级别错误（如数据验证失败）
- **MEDIUM**: 中级别错误（如网络超时）
- **HIGH**: 高级别错误（如数据库连接失败）
- **CRITICAL**: 严重错误（如系统崩溃）

### 3. 错误分类
- **DATABASE**: 数据库相关
- **NETWORK**: 网络相关
- **AUTHENTICATION**: 身份验证
- **VALIDATION**: 数据验证
- **BUSINESS**: 业务逻辑
- **SYSTEM**: 系统级别
- **EXTERNAL_API**: 外部API
- **FILE_OPERATION**: 文件操作

## 文件结构

```
src/utils/
├── error_handler.py          # 核心错误处理模块
├── error_monitor.py          # 错误监控和统计
├── error_config.py           # 错误处理配置
├── error_examples.py         # 使用示例
└── integrate_error_handling.py  # 集成脚本
```

## 使用方法

### 1. 装饰器方式（推荐）

```python
from src.utils.error_handler import handle_exceptions, ErrorCategory, ErrorLevel

@handle_exceptions(category=ErrorCategory.DATABASE, level=ErrorLevel.HIGH)
def database_operation():
    """数据库操作"""
    # 如果发生异常，装饰器会自动处理
    result = db.execute("SELECT * FROM users")
    return result
```

### 2. 手动异常处理

```python
from src.utils.error_handler import DatabaseError, create_error_response, create_success_response

def manual_error_handling():
    try:
        # 业务逻辑
        result = perform_operation()
        return create_success_response(result, "操作成功")
    except Exception as e:
        raise DatabaseError("数据库操作失败", details={'error': str(e)})
```

### 3. 重试机制

```python
from src.utils.error_handler import retry_on_error

@retry_on_error(max_attempts=3, delay=1.0, backoff_factor=2.0)
def network_operation():
    """网络操作，失败时自动重试"""
    response = requests.get("https://api.example.com/data")
    return response.json()
```

### 4. 错误监控

```python
from src.utils.error_monitor import error_monitor

# 记录错误
error_monitor.record_error({
    'message': '操作失败',
    'category': 'database',
    'level': 'high',
    'details': {'table': 'users'}
})

# 获取错误统计
stats = error_monitor.get_stats(hours=24)
print(f"24小时内总错误数: {stats['total_errors']}")

# 生成错误报告
report = error_monitor.generate_report(hours=24)
print(report)
```

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/system.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# 错误监控配置
ERROR_MONITOR_ENABLED=true
ERROR_STORAGE_PATH=logs/error_monitor.json
ERROR_RETENTION_DAYS=7
ERROR_BUFFER_SIZE=1000

# 重试配置
DEFAULT_RETRY_ATTEMPTS=3
DEFAULT_RETRY_DELAY=1.0
MAX_RETRY_DELAY=60.0

# 错误响应配置
SHOW_DETAILED_ERRORS=false
ERROR_RESPONSE_TIMEOUT=30
```

### 应用集成

在Flask应用中集成错误处理：

```python
from flask import Flask
from src.utils.error_handler import register_error_handlers
from src.utils.error_monitor import error_monitor

app = Flask(__name__)

# 注册错误处理器
register_error_handlers(app)

# 启用错误监控
app.config['ERROR_MONITOR'] = error_monitor
```

## 错误响应格式

### 成功响应
```json
{
    "success": true,
    "message": "操作成功",
    "data": {...},
    "timestamp": "2025-01-09T10:30:00"
}
```

### 错误响应
```json
{
    "success": false,
    "error_code": "DB_CONNECTION_FAILED",
    "message": "数据库连接失败",
    "category": "database",
    "timestamp": "2025-01-09T10:30:00",
    "details": {
        "host": "localhost",
        "port": 3306
    }
}
```

## 错误监控和报告

### 错误统计
```python
# 获取24小时内的错误统计
stats = error_monitor.get_stats(hours=24)

# 统计内容包括：
# - 总错误数
# - 按分类统计
# - 按级别统计
# - 最常见错误
# - 错误趋势
```

### 错误报告
```python
# 生成错误报告
report = error_monitor.generate_report(hours=24)

# 报告内容包括：
# - 错误概览
# - 分类统计
# - 级别分布
# - 常见错误列表
```

## 最佳实践

### 1. 异常处理原则
- 在最接近错误源的地方捕获异常
- 使用具体的异常类型而不是通用的Exception
- 提供有意义的错误消息和上下文信息
- 记录足够的调试信息但不暴露敏感数据

### 2. 错误分类指导
- **ValidationError**: 用户输入验证失败
- **BusinessError**: 业务规则违反
- **DatabaseError**: 数据库操作失败
- **NetworkError**: 网络请求失败
- **SystemError**: 系统级别错误

### 3. 重试策略
- 只对临时性错误进行重试
- 使用指数退避算法
- 设置合理的最大重试次数
- 记录重试过程

### 4. 监控建议
- 定期查看错误统计
- 关注错误趋势变化
- 及时处理高频错误
- 定期清理旧错误记录

## 集成指南

### 自动集成
```bash
# 运行集成脚本
python src/utils/integrate_error_handling.py
```

### 手动集成步骤

1. **导入错误处理模块**
```python
from src.utils.error_handler import (
    handle_exceptions, DatabaseError, NetworkError,
    create_error_response, create_success_response
)
```

2. **注册错误处理器**
```python
from src.utils.error_handler import register_error_handlers
register_error_handlers(app)
```

3. **替换现有错误处理**
```python
# 替换前
try:
    result = operation()
    return jsonify({'success': True, 'data': result})
except Exception as e:
    return jsonify({'error': str(e)}), 500

# 替换后
@handle_exceptions(category=ErrorCategory.BUSINESS)
def operation_handler():
    result = operation()
    return create_success_response(result, "操作成功")
```

## 故障排除

### 常见问题

1. **导入错误**
   - 确保 `src/utils/` 目录在Python路径中
   - 检查模块依赖是否正确安装

2. **配置问题**
   - 验证 `.env` 文件配置
   - 检查日志目录权限

3. **性能问题**
   - 调整错误缓冲区大小
   - 定期清理错误日志
   - 优化错误处理逻辑

### 调试方法

1. **启用详细日志**
```bash
LOG_LEVEL=DEBUG
SHOW_DETAILED_ERRORS=true
```

2. **检查错误统计**
```python
stats = error_monitor.get_stats()
print(f"错误统计: {stats}")
```

3. **查看错误详情**
```python
errors = error_monitor.get_error_details(limit=10)
for error in errors:
    print(f"错误: {error}")
```

## 总结

统一错误处理机制提供了：

✅ **标准化**: 统一的错误类型和响应格式
✅ **可监控**: 完整的错误监控和统计功能
✅ **可重试**: 智能的重试机制
✅ **易集成**: 简单的装饰器和函数接口
✅ **可配置**: 灵活的配置选项
✅ **高性能**: 优化的错误处理性能

通过实施这套错误处理机制，系统的稳定性和可维护性将得到显著提升。
