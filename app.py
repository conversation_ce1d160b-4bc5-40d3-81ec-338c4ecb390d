#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重构后的主应用文件
使用应用工厂模式，简化代码结构
"""

# Flask核心
from flask import Flask, render_template, redirect, request, jsonify

# 应用工厂
from src.utils.app_factory import create_app

# 创建应用实例
app = create_app()

# 业务模块（按需导入）
import os
import logging
import pandas as pd
from datetime import datetime
import uuid
import math

# 工具模块
from src.utils.excel_helpers import get_merged_cell_value, process_excel_data
from src.utils.misc_helpers import download_static_files
from src.utils.db_manager import DatabaseManager
from src.utils.common_utils import safe_int, safe_float, format_date, format_number

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加文件上传配置
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024 * 1024  # 10GB

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# ==================== 主要路由 ====================

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/data/entry')
def redirect_data_entry():
    """重定向到数据录入页面"""
    return redirect('/county/data/entry', code=301)

@app.route('/error-stats')
def error_stats_page():
    """错误统计监控页面"""
    return render_template('error_stats.html')

# ==================== Excel数据处理路由 ====================

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """文件上传处理"""
    if request.method == 'GET':
        return render_template('upload.html')
    
    # POST请求处理逻辑
    try:
        from src.utils.error_handler import handle_exceptions, ErrorCategory
        
        @handle_exceptions(category=ErrorCategory.FILE_OPERATION)
        def process_upload():
            # 文件上传处理逻辑
            if 'file' not in request.files:
                return jsonify({'error': '没有选择文件'}), 400
            
            file = request.files['file']
            if file.filename == '':
                return jsonify({'error': '没有选择文件'}), 400
            
            if file and file.filename.endswith(('.xlsx', '.xls')):
                # 处理Excel文件
                filename = f"{uuid.uuid4()}_{file.filename}"
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)
                
                # 处理Excel数据
                result = process_excel_data(filepath)
                
                # 清理临时文件
                os.remove(filepath)
                
                return jsonify({'success': True, 'data': result})
            else:
                return jsonify({'error': '只支持Excel文件(.xlsx, .xls)'}), 400
        
        return process_upload()
        
    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        return jsonify({'error': f'文件处理失败: {str(e)}'}), 500

@app.route('/download/<path:filename>')
def download_file(filename):
    """文件下载"""
    try:
        return download_static_files(filename)
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        return jsonify({'error': '文件下载失败'}), 404

# ==================== API路由 ====================

@app.route('/api/version')
def api_version():
    """获取版本信息"""
    return jsonify({
        'version': app.config.get('CURRENT_VERSION', 'unknown'),
        'release_notes': app.config.get('VERSION_RELEASE_NOTES', ''),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/health')
def api_health():
    """健康检查"""
    try:
        # 检查数据库连接
        db_status = DatabaseManager.get_connection_status()
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': db_status,
            'version': app.config.get('CURRENT_VERSION', 'unknown')
        })
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/stats')
def api_stats():
    """获取系统统计信息"""
    try:
        # 获取基本统计信息
        stats = {
            'uptime': 'unknown',  # 可以添加应用启动时间计算
            'requests_total': 'unknown',  # 可以添加请求计数
            'database_connections': len(DatabaseManager._engines),
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ==================== 错误处理 ====================

@app.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"内部服务器错误: {str(error)}")
    return render_template('errors/500.html'), 500

# ==================== 应用启动 ====================

def init_app_data():
    """初始化应用数据"""
    try:
        # 这里可以添加应用启动时需要执行的初始化逻辑
        logger.info("应用数据初始化完成")
    except Exception as e:
        logger.error(f"应用数据初始化失败: {str(e)}")

if __name__ == '__main__':
    # 初始化应用数据
    init_app_data()
    
    # 启动应用
    port = int(os.environ.get('PORT', 5100))
    debug = os.environ.get('DEBUG', 'true').lower() == 'true'
    
    logger.info(f"应用启动在端口 {port}, 调试模式: {debug}")
    app.run(host='0.0.0.0', port=port, debug=debug)
