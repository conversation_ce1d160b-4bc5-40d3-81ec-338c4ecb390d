from flask import Flask, render_template, request, redirect, url_for, jsonify, session, send_file, flash

# 统一错误处理机制
from src.utils.error_handler import register_error_handlers, error_handler
from src.utils.error_monitor import error_monitor
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.pool import QueuePool
import pandas as pd
import io
from io import BytesIO, StringIO
import openpyxl
from datetime import datetime
from openpyxl.utils import get_column_letter
import uuid
import functools
import time
import traceback
import math
import logging
from src.utils.extensions import db
from src.mysql_audit.models import (
    init_db, add_user_activity, get_user_activities, get_operation_stats,
    get_all_servers, get_server_by_id, get_server_full_config, add_server, update_server, delete_server,
    get_system_setting, update_system_setting, UserActivity
)
from src.mysql_audit.log_parser import scan_logs_for_server, scan_all_servers
from src.mysql_audit.reports import ReportGenerator
from src.mysql_audit.config import APP_CONFIG, DB_CONFIG
from src.ansible_work.config import Config as AnsibleConfig
import os

# 导入数据展示功能
from src.county_data.data_display import data_display

# 导入新的 utils 模块
from src.utils.database_helpers import (
    get_db_connection, get_table_display_name, get_all_tables, execute_query,
    get_table_comments, get_column_comments, get_chinese_headers, get_table_stats,
    get_providers, get_yearly_increment_stats
)
from src.utils.excel_helpers import get_merged_cell_value, process_excel_data
from src.utils.misc_helpers import download_static_files
from src.county_data.table_connection_rate import DBConnectionManager

app = Flask(__name__, template_folder='templates')

# 添加会话密钥（使用环境变量或默认值）
app.secret_key = os.environ.get('SECRET_KEY', 'dev_secret_key_for_development_only_32chars')

# 配置SQLAlchemy (主数据库)
app.config['SQLALCHEMY_DATABASE_URI'] = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"

# 添加文件上传配置
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024 * 1024  # 调整为 10GB

# 确保上传目录存在
# os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True) # 移除 uploads 目录创建

# 添加数据库绑定配置 for ansible_work
app.config['SQLALCHEMY_BINDS'] = {
    'ansible_db': AnsibleConfig.SQLALCHEMY_DATABASE_URI
} # <--- 添加 Binds

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_POOL_SIZE'] = 10
app.config['SQLALCHEMY_MAX_OVERFLOW'] = 20
app.config['SQLALCHEMY_POOL_TIMEOUT'] = 30
app.config['SQLALCHEMY_POOL_RECYCLE'] = 1800

# 初始化扩展
db.init_app(app)

# 注册统一错误处理器
register_error_handlers(app)

# 启用错误监控
app.config['ERROR_MONITOR'] = error_monitor

# 将版本信息存入 app.config
app.config['CURRENT_VERSION'] = 'v4.8.5'
app.config['SHOW_VERSION_NOTIFICATION'] = False 
app.config['VERSION_RELEASE_NOTES'] = """1. 新增数据治理模块，支持对数据库进行全面质量检查
2. 优化数据中台功能，提升数据加载速度
3. 新增数据显示模块，支持对数据进行可视化展示
4. 修复已知问题，提高系统稳定性
5. 新增ansible模块，支持对服务器进行批量管理
6. 新增入湖数据统计功能，支持对入湖数据进行统计
7. 新增库表挂接率功能，支持对目录挂接情况进行统计分析"""


# 注册核心路由蓝图
from src.core_routes.routes import core_bp
app.register_blueprint(core_bp)

# 注册网络工具模块蓝图
from src.net_tools import net_tools_bp
app.register_blueprint(net_tools_bp)

# 注册服务器管理模块蓝图
from src.server_management.server_routes import server_bp
app.register_blueprint(server_bp, url_prefix='')

# 注册数据中台模块蓝图
from src.data_center.data_center_routes import data_center # 只导入蓝图
app.register_blueprint(data_center, url_prefix='')

# 注册 Ansible Work 模块蓝图
from src.ansible_work import ansible_bp
app.register_blueprint(ansible_bp)

# 注册 MySQL Audit 蓝图
from src.mysql_audit.audit_routes import mysql_audit_bp, load_system_settings as load_mysql_audit_system_settings
app.register_blueprint(mysql_audit_bp)

# 注册区县数据模块蓝图
from src.county_data import county_data_bp
app.register_blueprint(county_data_bp, url_prefix='/county')

# 注册数据展示蓝图
from src.county_data.data_display import data_display
app.register_blueprint(data_display)

# 注册搜索与辅助查询蓝图
from src.county_data.search_routes import search_bp # Updated import path
app.register_blueprint(search_bp)

# 注册数据可视化和统计蓝图
from src.county_data.data_visualization_routes import visualization_bp # Updated import path
app.register_blueprint(visualization_bp)

# 新增：注册县区卡片统计数据蓝图
from src.county_data.summary_stats_routes import summary_stats_bp
app.register_blueprint(summary_stats_bp)

# 新增：注册库表挂接率蓝图
from src.county_data.table_connection_rate import table_connection_rate_bp
app.register_blueprint(table_connection_rate_bp)

# 新增：注册指标统计蓝图
from src.county_data.metrics_stats_routes import metrics_stats_bp
app.register_blueprint(metrics_stats_bp)

# 新增：注册错误统计蓝图
from src.utils.error_stats_routes import error_stats_bp
app.register_blueprint(error_stats_bp)

# 尝试导入 init_cache_tables 函数，并记录导入状态
_init_cache_tables_func = None
try:
    from src.data_center.data_center_routes import init_cache_tables as _imported_init_cache_tables
    _init_cache_tables_func = _imported_init_cache_tables
    if callable(_init_cache_tables_func):
        app.logger.info("成功导入 'init_cache_tables' 函数到 app.py.")
    else:
        app.logger.error("'init_cache_tables' 从 data_center_routes 导入，但它不是一个可调用对象。")
        _init_cache_tables_func = None # 确保不会尝试调用不可调用对象
except ImportError as e:
    app.logger.error(f"从 src.data_center.data_center_routes 导入 'init_cache_tables' 失败: {e}", exc_info=True)
except Exception as e:
    app.logger.error(f"从 src.data_center.data_center_routes 导入 'init_cache_tables' 时发生未知错误: {e}", exc_info=True)

# 获取系统版本信息API
@app.route('/api/version', methods=['GET'])
def get_version_info():
    """获取当前系统版本信息"""
    # 将多行文本转换为列表
    release_notes = [line.strip() for line in app.config.get('VERSION_RELEASE_NOTES','').strip().split('\n') if line.strip()] # Use app.config
    
    return jsonify({
        'version': app.config.get('CURRENT_VERSION'), # Use app.config
        'show_notification': app.config.get('SHOW_VERSION_NOTIFICATION'), # Use app.config
        'release_notes': release_notes
    })

# 添加版本更新日志内容路由
@app.route('/templates/version_release_notes.html')
def version_release_notes():
    # 确保返回最新的内容（添加no-cache头）
    response = render_template('version_release_notes.html')
    response = app.make_response(response)
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# 安全登录页面

# --- 数据库初始化 for mysql_audit ---
with app.app_context():
    try:
        # 创建所有表
        db.create_all()
        print("MySQL审计数据库表创建成功 (app.py)")
        
        # 初始化系统设置
        # Ensure init_db is the correct one, possibly from src.mysql_audit.models if it's specific
        if init_db(): # This should be the init_db related to mysql_audit setup
            print("MySQL审计数据库初始化成功 (app.py)")
        else:
            print("MySQL审计数据库初始化失败 (app.py)")

        # 调用从 audit_routes.py 导入的设置加载函数
        load_mysql_audit_system_settings()

    except Exception as e:
        print(f"MySQL审计数据库初始化过程中发生错误 (app.py): {e}")

# 添加退出钩子函数
@app.teardown_appcontext
def close_db_connections(exception=None):
    """在应用上下文结束时关闭数据库连接"""
    DBConnectionManager.close_all_connections()
    if exception:
        app.logger.error(f"应用上下文结束时发生错误: {str(exception)}")

@app.route('/data/entry')
def redirect_data_entry():
    return redirect('/county/data/entry', code=301)

@app.route('/error-stats')
def error_stats_page():
    """错误统计监控页面"""
    return render_template('error_stats.html')

if __name__ == '__main__':
    with app.app_context():
        # 初始化数据库
        db.create_all()
        init_db() # MySQL Audit 相关的初始化

        # 初始化数据中台缓存表 (确保在 app 上下文内，并且只在主执行块中)
        if _init_cache_tables_func: 
            try:
                app.logger.info("app.py (__main__) 尝试调用 'init_cache_tables'...")
                _init_cache_tables_func() 
                app.logger.info("app.py (__main__) 调用 'init_cache_tables' 完成。")
                print("数据中台缓存表初始化成功 (来自 app.py __main__ print)")
            except Exception as e:
                app.logger.error(f"app.py (__main__) 在执行 'init_cache_tables' 时发生错误: {e}", exc_info=True)
                print(f"数据中台缓存表初始化失败 (来自 app.py __main__ print): {str(e)}")
        else:
            log_message = "app.py (__main__) 跳过调用 \'init_cache_tables\'，因为它未能正确导入或不是可调用对象。"
            app.logger.warning(log_message)
            print(f"数据中台缓存表初始化无法执行 (来自 app.py __main__ print): \'init_cache_tables\' 未正确加载。")

    # 运行应用
    app.run(host='0.0.0.0', port=5100, debug=True)

            