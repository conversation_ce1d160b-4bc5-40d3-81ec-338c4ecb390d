#!/bin/bash

# 测试脚本 - 验证 docker_shell.sh 的配置更新功能
# 此脚本仅测试配置更新，不执行 Docker 构建

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[TEST-INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[TEST-SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[TEST-WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[TEST-ERROR]${NC} $1"
}

# 配置变量（与 docker_shell.sh 保持一致）
VERSION=v4.9

# 数据库配置
OLD_HOST=**************
NEW_HOST=************
OLD_PORT_DB=3310
NEW_PORT_DB=3310
OLD_PASSWORD=12345
NEW_PASSWORD=Vp8bBVcKwKt8RRSJ

# 应用端口配置
OLD_PORT=5100
NEW_PORT=5000

# Ansible配置
OLD_ANSIBLE_HOST=***********
NEW_ANSIBLE_HOST=***********
OLD_JUMP_HOST=************
NEW_JUMP_HOST=************

log_info "开始测试 docker_shell.sh 配置更新功能..."

# 检查必要文件
if [ ! -f ".env" ]; then
    log_error ".env 文件不存在！"
    if [ -f ".env.example" ]; then
        log_info "从 .env.example 创建 .env 文件..."
        cp .env.example .env
        log_success ".env 文件已创建"
    else
        log_error ".env.example 文件也不存在！"
        exit 1
    fi
fi

# 创建测试备份
log_info "创建测试备份..."
cp .env .env.test.bak

# 显示更新前的配置
log_info "更新前的配置："
echo "=================================="
grep -E "^(DB_HOST|DB_PASSWORD|MAIN_DB_URI|MYSQL_AUDIT_DB_URI|ANSIBLE_DB_URI|ANSIBLE_HOST|JUMP_HOST|PORT)=" .env 2>/dev/null || echo "未找到相关配置"
echo "=================================="

# 模拟配置更新（不执行 Docker 构建）
log_info "模拟执行配置更新..."

# 更新基本数据库配置
if grep -q "^DB_HOST=" .env; then
    sed -i "s/^DB_HOST=.*/DB_HOST=$NEW_HOST/" .env
    log_success "DB_HOST 已更新为: $NEW_HOST"
else
    echo "DB_HOST=$NEW_HOST" >> .env
    log_success "已添加 DB_HOST=$NEW_HOST"
fi

if grep -q "^DB_PASSWORD=" .env; then
    sed -i "s/^DB_PASSWORD=.*/DB_PASSWORD=$NEW_PASSWORD/" .env
    log_success "DB_PASSWORD 已更新"
else
    echo "DB_PASSWORD=$NEW_PASSWORD" >> .env
    log_success "已添加 DB_PASSWORD 配置"
fi

# 更新数据库连接URI
if grep -q "^MAIN_DB_URI=" .env; then
    sed -i "s|^MAIN_DB_URI=.*|MAIN_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/excel|" .env
    log_success "MAIN_DB_URI 已更新"
else
    echo "MAIN_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/excel" >> .env
    log_success "已添加 MAIN_DB_URI 配置"
fi

if grep -q "^MYSQL_AUDIT_DB_URI=" .env; then
    sed -i "s|^MYSQL_AUDIT_DB_URI=.*|MYSQL_AUDIT_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/mysql_log|" .env
    log_success "MYSQL_AUDIT_DB_URI 已更新"
else
    echo "MYSQL_AUDIT_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/mysql_log" >> .env
    log_success "已添加 MYSQL_AUDIT_DB_URI 配置"
fi

if grep -q "^ANSIBLE_DB_URI=" .env; then
    sed -i "s|^ANSIBLE_DB_URI=.*|ANSIBLE_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/ansible_ui|" .env
    log_success "ANSIBLE_DB_URI 已更新"
else
    echo "ANSIBLE_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/ansible_ui" >> .env
    log_success "已添加 ANSIBLE_DB_URI 配置"
fi

# 更新端口配置
if grep -q "^PORT=" .env; then
    sed -i "s/^PORT=.*/PORT=$NEW_PORT/" .env
    log_success "PORT 已更新为: $NEW_PORT"
else
    echo "" >> .env
    echo "# 应用端口配置" >> .env
    echo "PORT=$NEW_PORT" >> .env
    log_success "已添加 PORT=$NEW_PORT 配置"
fi

# 更新 Ansible 配置
if grep -q "^ANSIBLE_HOST=" .env; then
    sed -i "s/^ANSIBLE_HOST=.*/ANSIBLE_HOST=$NEW_ANSIBLE_HOST/" .env
    log_success "ANSIBLE_HOST 已更新为: $NEW_ANSIBLE_HOST"
else
    echo "ANSIBLE_HOST=$NEW_ANSIBLE_HOST" >> .env
    log_success "已添加 ANSIBLE_HOST=$NEW_ANSIBLE_HOST"
fi

if grep -q "^JUMP_HOST=" .env; then
    sed -i "s/^JUMP_HOST=.*/JUMP_HOST=$NEW_JUMP_HOST/" .env
    log_success "JUMP_HOST 已更新为: $NEW_JUMP_HOST"
else
    echo "JUMP_HOST=$NEW_JUMP_HOST" >> .env
    log_success "已添加 JUMP_HOST=$NEW_JUMP_HOST"
fi

# 显示更新后的配置
log_info "更新后的配置："
echo "=================================="
grep -E "^(DB_HOST|DB_PASSWORD|MAIN_DB_URI|MYSQL_AUDIT_DB_URI|ANSIBLE_DB_URI|ANSIBLE_HOST|JUMP_HOST|PORT)=" .env
echo "=================================="

# 验证配置
log_info "验证配置更新..."
success=true

if ! grep -q "DB_HOST=$NEW_HOST" .env; then
    log_error "DB_HOST 更新失败"
    success=false
fi

if ! grep -q "DB_PASSWORD=$NEW_PASSWORD" .env; then
    log_error "DB_PASSWORD 更新失败"
    success=false
fi

if ! grep -q "PORT=$NEW_PORT" .env; then
    log_error "PORT 更新失败"
    success=false
fi

if ! grep -q "MAIN_DB_URI=.*$NEW_HOST.*$NEW_PASSWORD" .env; then
    log_error "MAIN_DB_URI 更新失败"
    success=false
fi

if [ "$success" = true ]; then
    log_success "所有配置验证通过！"
    log_info "测试总结："
    echo "  ✓ 数据库主机已更新"
    echo "  ✓ 数据库密码已更新"
    echo "  ✓ 应用端口已更新"
    echo "  ✓ 数据库连接URI已更新"
    echo "  ✓ Ansible配置已更新"
else
    log_error "部分配置验证失败！"
fi

# 询问是否恢复原始配置
read -p "是否恢复原始配置？(y/n): " choice
if [ "$choice" = "y" ]; then
    cp .env.test.bak .env
    log_success "已恢复原始配置"
else
    log_info "保留更新后的配置"
fi

log_info "测试完成！"
