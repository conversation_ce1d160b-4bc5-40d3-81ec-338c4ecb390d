#!/bin/bash

# 配置变量
VERSION=v4.9

# 数据库配置
OLD_HOST=**************
NEW_HOST=************
OLD_PORT_DB=3310
NEW_PORT_DB=3310
OLD_PASSWORD=12345
NEW_PASSWORD=Vp8bBVcKwKt8RRSJ

# 应用端口配置
OLD_PORT=5100
NEW_PORT=5000

# Ansible配置
OLD_ANSIBLE_HOST=***********
NEW_ANSIBLE_HOST=***********
OLD_JUMP_HOST=************
NEW_JUMP_HOST=************

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要文件
check_files() {
    local files=("app.py" "Dockerfile" ".env")

    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "$file 文件不存在！"
            if [ "$file" = ".env" ]; then
                log_info "建议：cp .env.example .env"
            fi
            exit 1
        fi

        if [ ! -w "$file" ]; then
            log_error "$file 文件没有写入权限！"
            exit 1
        fi
    done

    log_success "所有必要文件检查通过"
}

# 创建备份
create_backups() {
    local files=("app.py" "Dockerfile" ".env")

    log_info "创建文件备份..."
    for file in "${files[@]}"; do
        cp "$file" "${file}.bak"
        log_success "已创建 $file 备份为 ${file}.bak"
    done
}

# 执行文件检查和备份
check_files
create_backups

# 更新 .env 文件中的端口配置
update_env_port() {
    log_info "更新 .env 文件中的端口配置 $OLD_PORT → $NEW_PORT..."

    # 检查是否已存在 PORT 配置
    if grep -q "^PORT=" .env; then
        # 更新现有的 PORT 配置
        sed -i "s/^PORT=.*/PORT=$NEW_PORT/" .env
        log_success ".env 文件中的 PORT 配置已更新"
    else
        # 添加新的 PORT 配置
        echo "" >> .env
        echo "# 应用端口配置" >> .env
        echo "PORT=$NEW_PORT" >> .env
        log_success ".env 文件中已添加 PORT=$NEW_PORT 配置"
    fi

    # 验证更新
    if grep -q "PORT=$NEW_PORT" .env; then
        log_success "端口配置验证成功：PORT=$NEW_PORT"
    else
        log_error "端口配置更新失败"
        return 1
    fi
}

# 更新 app.py 中的端口（作为备用方案）
update_app_port() {
    log_info "检查 app.py 中的端口配置..."

    # 替换可能的硬编码端口格式
    sed -i -e "s/:$OLD_PORT/:$NEW_PORT/g" \
           -e "s/port=$OLD_PORT/port=$NEW_PORT/g" \
           -e "s/port: $OLD_PORT/port: $NEW_PORT/g" \
           -e "s/PORT = $OLD_PORT/PORT = $NEW_PORT/g" \
           -e "s/port=$OLD_PORT)/port=$NEW_PORT)/g" \
           -e "s/port = $OLD_PORT/port = $NEW_PORT/g" \
           -e "s/'PORT', $OLD_PORT/'PORT', $NEW_PORT/g" \
           -e "s/\"PORT\", $OLD_PORT/\"PORT\", $NEW_PORT/g" app.py

    # 检查是否有改变
    if ! diff app.py app.py.bak >/dev/null; then
        log_success "app.py 中的端口配置已更新"
    else
        log_info "app.py 中未发现硬编码端口，这是正常的（使用环境变量）"
    fi
}

# 执行端口更新
update_env_port
update_app_port

# 更新 Dockerfile 中的端口号
update_dockerfile_port() {
    log_info "更新 Dockerfile 中的端口 $OLD_PORT → $NEW_PORT..."

    # 更新 EXPOSE 指令
    sed -i "s/EXPOSE $OLD_PORT/EXPOSE $NEW_PORT/g" Dockerfile

    # 验证更新
    if grep -q "EXPOSE $NEW_PORT" Dockerfile; then
        log_success "Dockerfile 端口更新成功：EXPOSE $NEW_PORT"
    else
        log_warning "Dockerfile 中未找到 EXPOSE $OLD_PORT 指令"
        log_info "当前 Dockerfile 中的 EXPOSE 指令："
        grep -n "EXPOSE" Dockerfile || log_info "未找到 EXPOSE 指令"

        # 询问是否添加 EXPOSE 指令
        read -p "是否添加 EXPOSE $NEW_PORT 指令？(y/n): " choice
        if [ "$choice" = "y" ]; then
            # 在 CMD 指令前添加 EXPOSE
            sed -i "/^CMD/i EXPOSE $NEW_PORT" Dockerfile
            log_success "已添加 EXPOSE $NEW_PORT 指令"
        fi
    fi
}

update_dockerfile_port

# 更新 .env 文件中的所有数据库配置
update_database_config() {
    log_info "更新 .env 文件中的所有数据库配置..."

    # 1. 更新基本数据库配置
    log_info "更新基本数据库配置..."

    # 更新 DB_HOST
    if grep -q "^DB_HOST=" .env; then
        sed -i "s/^DB_HOST=.*/DB_HOST=$NEW_HOST/" .env
        log_success "DB_HOST 已更新为: $NEW_HOST"
    else
        echo "DB_HOST=$NEW_HOST" >> .env
        log_success "已添加 DB_HOST=$NEW_HOST"
    fi

    # 更新 DB_PASSWORD
    if grep -q "^DB_PASSWORD=" .env; then
        sed -i "s/^DB_PASSWORD=.*/DB_PASSWORD=$NEW_PASSWORD/" .env
        log_success "DB_PASSWORD 已更新"
    else
        echo "DB_PASSWORD=$NEW_PASSWORD" >> .env
        log_success "已添加 DB_PASSWORD 配置"
    fi

    # 2. 更新数据库连接URI
    log_info "更新数据库连接URI..."

    # 更新 MAIN_DB_URI
    if grep -q "^MAIN_DB_URI=" .env; then
        sed -i "s|^MAIN_DB_URI=.*|MAIN_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/excel|" .env
        log_success "MAIN_DB_URI 已更新"
    else
        echo "MAIN_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/excel" >> .env
        log_success "已添加 MAIN_DB_URI 配置"
    fi

    # 更新 MYSQL_AUDIT_DB_URI
    if grep -q "^MYSQL_AUDIT_DB_URI=" .env; then
        sed -i "s|^MYSQL_AUDIT_DB_URI=.*|MYSQL_AUDIT_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/mysql_log|" .env
        log_success "MYSQL_AUDIT_DB_URI 已更新"
    else
        echo "MYSQL_AUDIT_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/mysql_log" >> .env
        log_success "已添加 MYSQL_AUDIT_DB_URI 配置"
    fi

    # 更新 ANSIBLE_DB_URI
    if grep -q "^ANSIBLE_DB_URI=" .env; then
        sed -i "s|^ANSIBLE_DB_URI=.*|ANSIBLE_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/ansible_ui|" .env
        log_success "ANSIBLE_DB_URI 已更新"
    else
        echo "ANSIBLE_DB_URI=mysql+pymysql://root:$NEW_PASSWORD@$NEW_HOST:$NEW_PORT_DB/ansible_ui" >> .env
        log_success "已添加 ANSIBLE_DB_URI 配置"
    fi

    # 3. 更新 Ansible 主机配置
    log_info "更新 Ansible 配置..."

    # 更新 ANSIBLE_HOST
    if grep -q "^ANSIBLE_HOST=" .env; then
        sed -i "s/^ANSIBLE_HOST=.*/ANSIBLE_HOST=$NEW_ANSIBLE_HOST/" .env
        log_success "ANSIBLE_HOST 已更新为: $NEW_ANSIBLE_HOST"
    else
        echo "ANSIBLE_HOST=$NEW_ANSIBLE_HOST" >> .env
        log_success "已添加 ANSIBLE_HOST=$NEW_ANSIBLE_HOST"
    fi

    # 更新 JUMP_HOST
    if grep -q "^JUMP_HOST=" .env; then
        sed -i "s/^JUMP_HOST=.*/JUMP_HOST=$NEW_JUMP_HOST/" .env
        log_success "JUMP_HOST 已更新为: $NEW_JUMP_HOST"
    else
        echo "JUMP_HOST=$NEW_JUMP_HOST" >> .env
        log_success "已添加 JUMP_HOST=$NEW_JUMP_HOST"
    fi

    # 验证更新
    log_info "验证配置更新..."
    local success=true

    if ! grep -q "DB_HOST=$NEW_HOST" .env; then
        log_error "DB_HOST 更新失败"
        success=false
    fi

    if ! grep -q "DB_PASSWORD=$NEW_PASSWORD" .env; then
        log_error "DB_PASSWORD 更新失败"
        success=false
    fi

    if ! grep -q "MAIN_DB_URI=.*$NEW_HOST.*$NEW_PASSWORD" .env; then
        log_error "MAIN_DB_URI 更新失败"
        success=false
    fi

    if [ "$success" = true ]; then
        log_success "所有数据库配置更新成功"
        log_info "当前配置摘要："
        echo "  DB_HOST: $NEW_HOST"
        echo "  DB_PORT: $NEW_PORT_DB"
        echo "  DB_PASSWORD: [已设置]"
        echo "  ANSIBLE_HOST: $NEW_ANSIBLE_HOST"
        echo "  JUMP_HOST: $NEW_JUMP_HOST"
    else
        log_error "部分配置更新失败"
        log_info "当前 .env 文件中的相关配置："
        grep -n "DB_HOST\|DB_PASSWORD\|_DB_URI\|ANSIBLE_HOST\|JUMP_HOST" .env
        return 1
    fi
}

update_database_config

# Docker 构建和保存
build_and_save_docker() {
    local image_name="myapp:$VERSION"
    local tar_file="myapp_${VERSION}.tar"

    log_info "开始构建 Docker 镜像: $image_name"
    log_info "使用端口: $NEW_PORT"

    # 构建 Docker 镜像
    if docker build -t "$image_name" .; then
        log_success "Docker 镜像构建成功: $image_name"
    else
        log_error "Docker 镜像构建失败"
        exit 1
    fi

    # 显示镜像信息
    log_info "镜像信息："
    docker images | grep "myapp" | head -5

    # 保存 Docker 镜像为 tar 文件
    log_info "正在将 Docker 镜像保存为: $tar_file"
    if docker save -o "$tar_file" "$image_name"; then
        log_success "Docker 镜像保存成功: $tar_file"

        # 显示文件大小
        local file_size=$(du -h "$tar_file" | cut -f1)
        log_info "镜像文件大小: $file_size"
    else
        log_error "Docker 镜像保存失败"
        exit 1
    fi
}

# 显示配置摘要
show_summary() {
    log_info "配置更新摘要："
    echo "=========================================="
    echo "Docker镜像版本: $VERSION"
    echo "应用端口: $OLD_PORT → $NEW_PORT"
    echo ""
    echo "数据库配置更新："
    echo "  主机: $OLD_HOST → $NEW_HOST"
    echo "  端口: $NEW_PORT_DB"
    echo "  密码: $OLD_PASSWORD → $NEW_PASSWORD"
    echo ""
    echo "Ansible配置："
    echo "  ANSIBLE_HOST: $NEW_ANSIBLE_HOST"
    echo "  JUMP_HOST: $NEW_JUMP_HOST"
    echo ""
    echo "数据库连接URI已更新："
    echo "  - MAIN_DB_URI (excel数据库)"
    echo "  - MYSQL_AUDIT_DB_URI (mysql_log数据库)"
    echo "  - ANSIBLE_DB_URI (ansible_ui数据库)"
    echo "=========================================="

    log_info "更新的文件："
    echo "- .env (所有配置项)"
    echo "- Dockerfile (EXPOSE端口)"
    echo "- app.py (如有硬编码端口)"

    log_info "生成的文件："
    echo "- myapp_${VERSION}.tar (Docker镜像文件)"

    log_info "备份文件："
    echo "- .env.bak (原始环境配置)"
    echo "- Dockerfile.bak (原始Dockerfile)"
    echo "- app.py.bak (原始应用文件)"

    log_info "部署说明："
    echo "1. 将 myapp_${VERSION}.tar 上传到生产服务器"
    echo "2. 在生产服务器执行: docker load -i myapp_${VERSION}.tar"
    echo "3. 运行容器: docker run -d -p $NEW_PORT:$NEW_PORT myapp:$VERSION"
}

# 执行构建和显示摘要
build_and_save_docker
show_summary

log_success "脚本执行完成！"
log_info "现在可以将 myapp_${VERSION}.tar 文件部署到生产服务器"
