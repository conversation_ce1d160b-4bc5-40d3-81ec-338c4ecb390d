#!/bin/bash

VERSION=v4.8.4
NEW_HOST=************
NEW_PASSWORD=Vp8bBVcKwKt8RRSJ

# 新增：旧端口和新端口
OLD_PORT=5100
NEW_PORT=5000

# 检查 app.py 是否存在
if [ ! -f app.py ]; then
    echo "错误：app.py 文件不存在！"
    exit 1
fi

# 检查 app.py 是否可写
if [ ! -w app.py ]; then
    echo "错误：app.py 文件没有写入权限！"
    exit 1
fi

# 备份 app.py
cp app.py app.py.bak
echo "已创建 app.py 备份为 app.py.bak"

# 尝试多种可能的端口替换模式
echo "正在更新 app.py 中的端口 $OLD_PORT → $NEW_PORT..."
# 替换可能的格式 ':5100', 'port=5100', 'port: 5100', 'PORT = 5100' 等
sed -i -e "s/:$OLD_PORT/:$NEW_PORT/g" \
       -e "s/port=$OLD_PORT/port=$NEW_PORT/g" \
       -e "s/port: $OLD_PORT/port: $NEW_PORT/g" \
       -e "s/PORT = $OLD_PORT/PORT = $NEW_PORT/g" \
       -e "s/port=$OLD_PORT)/port=$NEW_PORT)/g" \
       -e "s/port = $OLD_PORT/port = $NEW_PORT/g" app.py

# 检查是否有任何改变
if diff app.py app.py.bak >/dev/null; then
    echo "警告：app.py 没有被修改，可能没有找到匹配的端口格式。"
    echo "正在显示 app.py 中可能包含端口的行："
    grep -n "$OLD_PORT" app.py || echo "未找到包含 $OLD_PORT 的行"
    
    # 尝试检测端口定义的行并显示
    echo "尝试检测端口定义："
    grep -n "port" app.py || echo "未找到包含 'port' 的行"
    
    # 询问是否继续
    read -p "是否继续执行脚本？(y/n): " choice
    if [ "$choice" != "y" ]; then
        echo "脚本终止。"
        exit 1
    fi
else
    echo "app.py 已成功更新。"
fi

# 更新 Dockerfile 中的端口号
echo "正在更新 Dockerfile 中的端口 $OLD_PORT → $NEW_PORT..."
if [ ! -f Dockerfile ]; then
    echo "错误：Dockerfile 不存在！"
    exit 1
fi

cp Dockerfile Dockerfile.bak
echo "已创建 Dockerfile 备份为 Dockerfile.bak"

sed -i "s/$OLD_PORT/$NEW_PORT/g" Dockerfile

# 验证 Dockerfile 是否更新成功
if grep -q "$NEW_PORT" Dockerfile; then
    echo "Dockerfile 端口更新成功。"
else
    echo "警告：Dockerfile 中可能没有找到端口号。"
    echo "正在显示 Dockerfile 中可能包含端口的行："
    grep -n "$OLD_PORT" Dockerfile.bak || echo "未找到包含 $OLD_PORT 的行"
    
    # 询问是否继续
    read -p "是否继续执行脚本？(y/n): " choice
    if [ "$choice" != "y" ]; then
        echo "脚本终止。"
        exit 1
    fi
fi

# 更新 db_config.py 中的 DB_HOST 和 DB_PASSWORD
echo "正在更新 db_config.py..."
if [ ! -f db_config.py ]; then
    echo "错误：db_config.py 不存在！"
    exit 1
fi

cp db_config.py db_config.py.bak
echo "已创建 db_config.py 备份为 db_config.py.bak"

sed -i "s/DB_HOST = '.*'/DB_HOST = '$NEW_HOST'/; s/DB_PASSWORD = '.*'/DB_PASSWORD = '$NEW_PASSWORD'/" db_config.py

# 验证 db_config.py 是否更新成功
if grep -q "DB_HOST = '$NEW_HOST'" db_config.py && grep -q "DB_PASSWORD = '$NEW_PASSWORD'" db_config.py; then
    echo "db_config.py 更新成功。"
else
    echo "警告：更新 db_config.py 可能不完全成功。"
    echo "DB_HOST 设置："
    grep -n "DB_HOST" db_config.py
    echo "DB_PASSWORD 设置："
    grep -n "DB_PASSWORD" db_config.py
    
    # 询问是否继续
    read -p "是否继续执行脚本？(y/n): " choice
    if [ "$choice" != "y" ]; then
        echo "脚本终止。"
        exit 1
    fi
fi

# 构建 Docker 镜像
echo "正在构建 Docker 镜像 myapp:$VERSION..."
docker build -t myapp:$VERSION .

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo "Docker 构建失败。"
    exit 1
fi

# 保存 Docker 镜像为 tar 文件
echo "正在将 Docker 镜像保存为 myapp_${VERSION}.tar..."
docker save -o myapp_${VERSION}.tar myapp:$VERSION

# 检查保存是否成功
if [ $? -ne 0 ]; then
    echo "Docker 保存失败。"
    exit 1
fi

echo "脚本执行成功完成。"
