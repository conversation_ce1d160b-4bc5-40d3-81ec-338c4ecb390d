#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一错误处理使用示例
展示如何在项目中使用统一错误处理机制
"""

from flask import Blueprint, jsonify, request
from .error_handler import (
    handle_exceptions, 
    DatabaseError, 
    NetworkError, 
    ValidationError,
    BusinessError,
    retry_on_error,
    create_error_response,
    create_success_response,
    ErrorCategory,
    ErrorLevel
)
from .error_monitor import error_monitor

# 创建示例蓝图
error_examples_bp = Blueprint('error_examples', __name__)

# 示例1: 使用装饰器处理异常
@error_examples_bp.route('/api/example/database')
@handle_exceptions(category=ErrorCategory.DATABASE, level=ErrorLevel.HIGH)
def database_operation_example():
    """数据库操作示例"""
    # 模拟数据库操作
    try:
        # 这里会抛出数据库错误
        raise DatabaseError("数据库连接失败", details={'host': 'localhost', 'port': 3306})
    except DatabaseError:
        # 重新抛出，让装饰器处理
        raise

# 示例2: 手动错误处理
@error_examples_bp.route('/api/example/manual')
def manual_error_handling():
    """手动错误处理示例"""
    try:
        # 模拟业务逻辑
        data = request.get_json()
        if not data:
            raise ValidationError("请求数据不能为空")
        
        if 'required_field' not in data:
            raise ValidationError("缺少必需字段: required_field")
        
        # 模拟业务处理
        result = process_business_logic(data)
        return create_success_response(result, "操作成功")
        
    except ValidationError as e:
        return create_error_response(e.message, e.error_code, 400, e.details)
    except BusinessError as e:
        return create_error_response(e.message, e.error_code, 422, e.details)
    except Exception as e:
        return create_error_response("系统内部错误", "INTERNAL_ERROR", 500)

# 示例3: 重试机制
@error_examples_bp.route('/api/example/retry')
@retry_on_error(max_attempts=3, delay=1.0, backoff_factor=2.0)
def retry_example():
    """重试机制示例"""
    # 模拟可能失败的网络操作
    import random
    if random.random() < 0.7:  # 70%的概率失败
        raise NetworkError("网络连接超时")
    
    return create_success_response({"message": "网络操作成功"})

# 示例4: 复杂错误处理
@error_examples_bp.route('/api/example/complex')
def complex_error_handling():
    """复杂错误处理示例"""
    try:
        # 步骤1: 验证输入
        data = validate_input_data()
        
        # 步骤2: 数据库操作
        db_result = perform_database_operation(data)
        
        # 步骤3: 外部API调用
        api_result = call_external_api(db_result)
        
        # 步骤4: 文件操作
        file_result = save_to_file(api_result)
        
        return create_success_response({
            'database': db_result,
            'api': api_result,
            'file': file_result
        })
        
    except ValidationError as e:
        error_monitor.record_error(e.to_dict())
        return create_error_response(f"数据验证失败: {e.message}", e.error_code, 400)
        
    except DatabaseError as e:
        error_monitor.record_error(e.to_dict())
        return create_error_response(f"数据库操作失败: {e.message}", e.error_code, 500)
        
    except NetworkError as e:
        error_monitor.record_error(e.to_dict())
        return create_error_response(f"网络请求失败: {e.message}", e.error_code, 503)
        
    except Exception as e:
        # 记录未知错误
        error_data = {
            'message': str(e),
            'category': 'system',
            'level': 'high',
            'details': {'function': 'complex_error_handling'}
        }
        error_monitor.record_error(error_data)
        return create_error_response("系统发生未知错误", "UNKNOWN_ERROR", 500)

# 示例5: 错误监控和统计
@error_examples_bp.route('/api/example/error-stats')
def get_error_stats():
    """获取错误统计示例"""
    try:
        hours = request.args.get('hours', 24, type=int)
        stats = error_monitor.get_stats(hours)
        return create_success_response(stats)
    except Exception as e:
        return create_error_response(f"获取错误统计失败: {str(e)}", "STATS_ERROR", 500)

# 辅助函数示例
def validate_input_data():
    """验证输入数据"""
    data = request.get_json()
    if not data:
        raise ValidationError("请求体不能为空")
    
    required_fields = ['name', 'email', 'age']
    for field in required_fields:
        if field not in data:
            raise ValidationError(f"缺少必需字段: {field}")
    
    if not isinstance(data['age'], int) or data['age'] < 0:
        raise ValidationError("年龄必须是非负整数")
    
    return data

def perform_database_operation(data):
    """执行数据库操作"""
    import random
    if random.random() < 0.3:  # 30%的概率失败
        raise DatabaseError("数据库连接超时", details={'operation': 'insert'})
    
    return {'id': 123, 'status': 'saved'}

def call_external_api(data):
    """调用外部API"""
    import random
    if random.random() < 0.2:  # 20%的概率失败
        raise NetworkError("外部API响应超时", details={'api': 'external_service'})
    
    return {'api_response': 'success', 'data': data}

def save_to_file(data):
    """保存到文件"""
    import random
    if random.random() < 0.1:  # 10%的概率失败
        raise Exception("文件写入失败")
    
    return {'file_path': '/tmp/result.json', 'size': 1024}

def process_business_logic(data):
    """处理业务逻辑"""
    if data.get('required_field') == 'invalid':
        raise BusinessError("业务规则验证失败", details={'rule': 'field_validation'})
    
    return {'processed': True, 'result': data}

# 错误处理中间件示例
def error_handling_middleware():
    """错误处理中间件"""
    def middleware(app):
        @app.before_request
        def before_request():
            # 请求前的错误检查
            pass
        
        @app.after_request
        def after_request(response):
            # 请求后的错误处理
            if response.status_code >= 400:
                # 记录错误响应
                error_data = {
                    'status_code': response.status_code,
                    'url': request.url,
                    'method': request.method,
                    'category': 'http_error',
                    'level': 'medium' if response.status_code < 500 else 'high'
                }
                error_monitor.record_error(error_data)
            
            return response
        
        return app
    return middleware

# 使用示例
"""
在Flask应用中使用统一错误处理:

from flask import Flask
from src.utils.error_handler import register_error_handlers
from src.utils.error_examples import error_examples_bp

app = Flask(__name__)

# 注册错误处理器
register_error_handlers(app)

# 注册示例蓝图
app.register_blueprint(error_examples_bp)

# 在路由中使用
@app.route('/api/users')
@handle_exceptions(category=ErrorCategory.DATABASE)
def get_users():
    # 你的代码逻辑
    pass

# 手动抛出自定义异常
@app.route('/api/validate')
def validate_data():
    if not valid_data:
        raise ValidationError("数据格式不正确")
    return success_response(data)
"""
